import {
    AfterViewInit,
    Component,
    Inject,
    On<PERSON><PERSON>roy,
    OnInit,
} from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import {
    MatDialogRef,
    MAT_DIALOG_DATA,
} from '@angular/material/dialog';
import { MatFormField } from '@angular/material/form-field';
import { MatSelect } from '@angular/material/select';
import { MatOption } from '@angular/material/autocomplete';
import { NgFor, NgIf } from '@angular/common';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';

/**
 * วิธีการใช้งาน
 * {
    title: 'ยืมอุปกรณ์',
    status: this.status.slice(1),
    submit: {
        title: 'ยืนยันการเปลี่ยนสถานะ',
        message: 'ยืนยันการเปลี่ยนสถานะหรือไม่'
    },
    cancelStatus: 'cancelled'
 * }
 */

@Component({
    selector: 'leave-update-status-dialog',
    templateUrl: './update-status-dialog.component.html',
    animations: fuseAnimations,
    imports: [FormsModule, ReactiveFormsModule, MatFormField, MatSelect, MatOption, NgIf, MatButton, MatIcon, NgFor],
    standalone: true,
})
export class UpdateStatusDialogComponent implements OnInit, AfterViewInit, OnDestroy {

    title: string = '';
    status: any[] = [];
    cancelStatus: string = 'cancelled';

    formData: FormGroup;

    /**
     * Constructor
     */
    constructor(
        public dialogRef: MatDialogRef<UpdateStatusDialogComponent>,
        @Inject(MAT_DIALOG_DATA) public data: any,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
    ) {
        this.title = data.title;
        this.status = data.status;
        this.cancelStatus = data.cancelStatus;

        this.formData = this._formBuilder.group({
            status: null,
            remark: null,
        });
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {

    }

    GetBranch(): void {

    }

    discard(): void {

    }

    /**
     * After view init
     */
    ngAfterViewInit(): void {

    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
    }

    Submit(): void {
        const confirmation = this._fuseConfirmationService.open({
            title: this.data?.submit?.title,
            message: this.data?.submit?.message,
            icon: {
                show: false,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
            },
            actions: {
                confirm: {
                    show: true,
                    label: 'Confirm',
                    color: 'primary',
                },
                cancel: {
                    show: true,
                    label: 'Cancel',
                },
            },
            dismissible: true,
        });

        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            // If the confirm button pressed...
            if (result === 'confirmed') {
                this.dialogRef.close(this.formData.value);
            }
        });
    }

    onClose() {
        this.dialogRef.close();
    }
}
