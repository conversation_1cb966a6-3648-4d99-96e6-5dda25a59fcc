import { Injectable } from '@angular/core';
import { HttpClient } from '@angular/common/http';
import { Observable, forkJoin } from 'rxjs';
import { map, shareReplay } from 'rxjs/operators';
import { environment } from 'environments/environment';

export interface Permission { id: number; name: string; }
export interface Department { id: number; name: string; }
export interface Position { id: number; name: string; }
export interface Employee { id: number; first_name: string; last_name: string }
export interface WorkShift { id: number; name: string; }
export interface Branch { id: number; name: string; }
export interface Head { id: number; first_name: string; last_name: string }

interface MasterData {
    permissions: Permission[];
    departments: Department[];
    positions: Position[];
    employees: Employee[];
    workShifts: WorkShift[];
    branchs: Branch[];
    heads: Head[];
}

@Injectable({
    providedIn: 'root'
})
export class MasterDataService {
    private cache$: Observable<MasterData> | null = null;

    constructor(private http: HttpClient) { }

    getMasterData(): Observable<{
        permissions: Permission[];
        departments: Department[];
        positions: Position[];
        employees: Employee[];
        workShifts: WorkShift[];
        branchs: Branch[];
        heads: Head[];
    }> {
        if (!this.cache$) {
            this.cache$ = forkJoin({
                permissions: this.http.get<any>(environment.API_URL + 'api/get_permission'),
                departments: this.http.get<any>(environment.API_URL + 'api/get_department'),
                positions: this.http.get<any>(environment.API_URL + 'api/get_position'),
                employees: this.http.get<any>(environment.API_URL + 'api/get_user'),
                heads : this.http.get<any>(`${environment.API_URL}api/get_user`, {
                    params: {
                        is_head: 1
                    }
                }),
                workShifts: this.http.get<any>(environment.API_URL + 'api/get_work_shift'),
                branchs: this.http.get<any>(environment.API_URL + 'api/get_branch'),
            }).pipe(
                map(result => ({
                    permissions: result.permissions.data,
                    departments: result.departments.data,
                    positions: result.positions.data,
                    employees: result.employees.data,
                    workShifts: result.workShifts.data,
                    branchs: result.branchs.data,
                    heads: result.heads.data,
                })),
                shareReplay(1)
            );
        }

        return this.cache$;
    }


    refresh() {
        this.cache$ = null;
    }
}
