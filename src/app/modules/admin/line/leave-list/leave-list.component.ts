import { Component, OnInit, } from '@angular/core';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { LineService } from '../line.service';
import { environment } from 'environments/environment';
import liff from '@line/liff';
import { LeaveListUserComponent } from '../../g-admin/user/leave-list-user/leave-list-user.component';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'leave-list-line',
    templateUrl: './leave-list.component.html',
    styleUrls: ['./leave-list.component.scss'],
    // encapsulation: ViewEncapsulation.None,
    // changeDetection: ChangeDetectionStrategy.OnPush,
    animations: fuseAnimations,
    imports: [LeaveListUserComponent, CommonModule]
})
export class LeaveListComponent implements OnInit {

    profile: any;
    liffInitialized = false;

    lineId: string = null;

    /**
     * Constructor
     */
    constructor(
        private _fuseConfirmationService: FuseConfirmationService,
        private _Service: LineService,
    ) {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    async ngOnInit(): Promise<void> {
        // this.lineId="U7f8d3bed4f907ec2b4bd88d5a40d8937"
        try {
            await liff.init({ liffId: environment.LIFF_LEAVE_LIST });
            this.liffInitialized = true;

            if (liff.isLoggedIn()) {

                this.profile = await liff.getProfile();

                this.lineId = this.profile?.userId;
            } else {
                liff.login();
            }
        } catch (error) {
            console.error('LIFF initialization failed', error);
        }
    }

    goback() {
        liff.closeWindow();
    }

   
}
