import { Component, OnInit, } from '@angular/core';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { LineService } from '../line.service';
import { environment } from 'environments/environment';
import liff from '@line/liff';
import { LeaveListUserComponent } from '../../g-admin/user/leave-list-user/leave-list-user.component';
import { OtListApproveComponent } from '../../g-admin/user/ot-list-approve/ot-list-user.component';
import { CommonModule } from '@angular/common';
import { WarningListAckComponent } from '../../g-admin/user/warning-list-ack/list-user.component';

@Component({
    selector: 'warning-list-line',
    templateUrl: './warning-list.component.html',
    styleUrls: ['./warning-list.component.scss'],
    // encapsulation: ViewEncapsulation.None,
    // changeDetection: ChangeDetectionStrategy.OnPush,
    animations: fuseAnimations,
    imports: [WarningListAckComponent, CommonModule]
})
export class WarningListLineComponent implements OnInit {

    profile: any;
    liffInitialized = false;

    lineId: string = null;

    /**
     * Constructor
     */
    constructor(
        private _fuseConfirmationService: FuseConfirmationService,
        private _Service: LineService,
    ) {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    async ngOnInit(): Promise<void> {
        // this.lineId = "U7f8d3bed4f907ec2b4bd88d5a40d8937"
        try {
            await liff.init({ liffId: environment.LIFF_WARNING_LIST });
            this.liffInitialized = true;

            if (liff.isLoggedIn()) {

                this.profile = await liff.getProfile();

                this.lineId = this.profile?.userId;
            } else {
                liff.login();
            }
        } catch (error) {
            console.error('LIFF initialization failed', error);
        }
    }

    goback() {
        liff.closeWindow();
    }

    submit(data: any) {
        const confirmation = this._fuseConfirmationService.open({
            title: 'บันทึก',
            message: 'คุณต้องการบันทึกใช่หรือไม่ ',
            icon: {
                show: false,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
            },
            actions: {
                confirm: {
                    show: true,
                    label: 'ยืนยัน',
                    color: 'primary',
                },
                cancel: {
                    show: true,
                    label: 'ยกเลิก',
                },
            },
            dismissible: true,
        });

        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            // If the confirm button pressed...
            if (result === 'confirmed') {
                let formValue = {
                    title: data.title,
                    description: data.description,
                    punishment: data.punishment,
                    approved_by: null,
                    acknowledged_by: 1,
                    line_id: this.lineId
                }
                this._Service.approveWarning(formValue, data.id).subscribe({
                    next: (resp: any) => {
                        this._fuseConfirmationService.open({
                            title: 'สำเร็จ',
                            message: 'รับทราบสำเร็จ',
                            icon: {
                                show: true,
                                name: 'heroicons_outline:check-circle',
                                color: 'success',
                            },
                            actions: {
                                confirm: {
                                    show: true,
                                    label: 'ตกลง',
                                    color: 'primary',
                                },
                                cancel: {
                                    show: false,
                                },
                            },
                        }).afterClosed().subscribe(() => {

                        });
                    },
                    error: (err: any) => {
                        this._fuseConfirmationService.open({
                            title: 'เกิดข้อผิดพลาด',
                            message: err?.error?.message,
                            icon: {
                                show: true,
                                name: 'heroicons_outline:exclamation-triangle',
                                color: 'warning',
                            },
                            actions: {
                                confirm: {
                                    show: false,
                                    label: 'ยืนยัน',
                                    color: 'primary',
                                },
                                cancel: {
                                    show: false,
                                    label: 'ยกเลิก',
                                },
                            },
                            dismissible: true,
                        });
                    },
                });
            }
        });
    }


}
