<div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent" *ngIf="lineId">
  <div class="flex flex-col py-4 px-6">
    <div class="grid grid-cols-1 md:grid-cols-3  gap-6 max-w-full">
        <div class="flex flex-col items-start p-6 shadow rounded-2xl bg-white">
     
        <h2 class="text-2xl font-bold mb-2">ข้อมูลส่วนตัว</h2>
      </div>
      <!-- Profile Card -->
      <div class="flex flex-col items-center p-6 shadow rounded-2xl bg-white">
        <ng-container *ngIf="url_pro; else skeleton">
          <img [src]="url_pro" alt="" class="w-32 h-32 rounded-full mb-4 border-4 border-gray-200 object-cover">
        </ng-container>
        <ng-template #skeleton>
          <div class="w-32 h-32 rounded-full mb-4 bg-gray-900 animate-pulse"></div>
        </ng-template>
        <p class="text-gray-900 mb-4">{{this.user?.personnel_id}}</p>
        <h2 class="text-2xl font-bold mb-2">{{this.user?.fullname}}</h2>
        <p class="text-gray-500 mb-4">{{this.user?.position?.name ?? '-'}}</p>
      </div>

      <!-- User Info Section -->
      <div class="p-8 grid grid-cols-1 gap-2 bg-white rounded-2xl shadow">
        <div class="flex flex-col">
          <span class="text-gray-500">อีเมล</span>
          <span class="text-lg font-medium">{{this.user?.email}}</span>
        </div>
        <div class="flex flex-col">
          <span class="text-gray-500">เบอร์โทร</span>
          <span class="text-lg font-medium">{{this.user?.phone_no }}</span>
        </div>
        <div class="flex flex-col">
          <span class="text-gray-500">วันที่เริ่มทำงาน</span>
          <span class="text-lg font-medium">{{user?.register_date | date : 'dd/MM/yyyy'}}</span>
        </div>
      </div>

      <!-- Additional Info -->
      <div class="p-8 bg-white rounded-2xl shadow">
        <h3 class="text-xl font-semibold mb-4">ข้อมูลเพิ่มเติม</h3>
        <ul class="list-disc list-inside text-gray-600 space-y-2">
          <li>เงินเดือน: {{user?.salary | number : '1.2'}}</li>
          <li>เงินเพิ่ม: {{user?.total_income | number : '1.2'}}</li>
          <li>เงินหัก: {{user?.total_deduct | number : '1.2'}}</li>
          <li>เบิกล่วงหน้า: {{user?.total_withdraw_salary | number : '1.2'}}</li>
          <li>ยอดจ่ายสุทธิ: {{user?.total | number : '1.2'}}</li>
        </ul>
      </div>
    </div>
  </div>
</div>