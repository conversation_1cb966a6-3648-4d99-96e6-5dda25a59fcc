import { Component, OnInit, } from '@angular/core';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { LineService } from '../line.service';
import { environment } from 'environments/environment';
import liff from '@line/liff';
import { LeaveListUserComponent } from '../../g-admin/user/leave-list-user/leave-list-user.component';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'profile-line',
    templateUrl: './profile.component.html',
    styleUrls: ['./profile.component.scss'],
    // encapsulation: ViewEncapsulation.None,
    // changeDetection: ChangeDetectionStrategy.OnPush,
    animations: fuseAnimations,
    imports: [CommonModule]
})
export class ProfileLineComponent implements OnInit {

    profile: any;
    liffInitialized = false;
    lineId: string = null;
    url_pro: string = null;
    user: any;
    /**
     * Constructor
     */
    constructor(
        private _fuseConfirmationService: FuseConfirmationService,
        private _Service: LineService,
    ) {
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    async ngOnInit(): Promise<void> {

        // this.lineId = "U7f8d3bed4f907ec2b4bd88d5a40d8937"
        // this._Service.getUserprofile(this.lineId).subscribe((res) => {
        //     this.user = res.data
        //     this.url_pro = this.user.image;

        // })
        try {
            await liff.init({ liffId: environment.LIFF_PROFILE });
            this.liffInitialized = true;

            if (liff.isLoggedIn()) {

                this.profile = await liff.getProfile();

                this.lineId = this.profile?.userId;
                if (this.lineId) {
                    this._Service.getUserprofile(this.lineId).subscribe((res) => {
                        this.user = res.data
                        this.url_pro = this.user.image;
                    })
                }
            } else {
                liff.login();
            }
        } catch (error) {
            console.error('LIFF initialization failed', error);
        }
    }

    goback() {
        liff.closeWindow();
    }


}
