// pipes/buddhist-date.pipe.ts
import { Pipe, PipeTransform } from '@angular/core';
import { DateTime } from 'luxon';

@Pipe({
  name: 'buddhistDate'
})
export class BuddhistDatePipe implements PipeTransform {
  transform(value: string | Date, format: string = 'dd/MM/yyyy'): string {
    if (!value) return '';
    
    const date = DateTime.fromISO(value.toString(), { zone: 'Asia/Bangkok' });
    if (!date.isValid) return '';

    // แปลง ค.ศ. -> พ.ศ.
    const buddhistYear = date.year + 543;

    // ใช้ format แล้วแทน year เป็น พ.ศ.
    return date.set({ year: buddhistYear }).toFormat(format);
  }
}
