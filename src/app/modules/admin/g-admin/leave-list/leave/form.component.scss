/* Toolbar */
.toolbar {
    position: sticky;
    top: 0;
    z-index: 10;
}

/* Page wrapper */
.page {
    padding: 16px;
    display: grid;
    place-items: start center;
}

/* Card sizing */
.card {
    width: 100%;
    max-width: 560px;
    /* ดีบนมือถือและแท็บเล็ตเล็ก */
    padding: 12px 0;
}

/* Form spacing */
.form {
    display: grid;
    gap: 16px;
    padding: 8px 16px 16px;
}

/* Each row becomes single column on mobile */
.row {
    display: grid;
    gap: 8px;
}

/* Label above field on mobile */
.label {
    font-weight: 600;
    color: rgba(0, 0, 0, .8);
}

/* Field takes full width */
.field {
    width: 100%;
}

/* Time range: stack on mobile */
.duo {
    display: grid;
    gap: 12px;
}

.half {
    width: 100%;
}

/* Submit area: sticky on small screens forการกดง่าย */
.actions {
    position: sticky;
    bottom: 0;
    background: linear-gradient(to top, var(--mat-sys-surface, #fff), rgba(255, 255, 255, 0.8));
    padding-top: 8px;
}

.submit {
    width: 100%;
    height: 48px;
    /* แตะง่าย */
    font-weight: 700;
    letter-spacing: .2px;
}

/* -------- Breakpoints -------- */
/* >= 640px: ขยาย card และ spacing เล็กน้อย */
@media (min-width: 640px) {
    .card {
        max-width: 680px;
    }

    .form {
        gap: 18px;
    }
}

/* >= 768px: แสดง label-ขวา field เป็น 2 คอลัมน์ */
@media (min-width: 768px) {
    .card {
        max-width: 760px;
    }

    .row {
        grid-template-columns: 200px 1fr;
        /* label | field */
        align-items: center;
    }

    .label {
        margin: 0;
    }

    .duo {
        grid-template-columns: 1fr 1fr;
        gap: 16px;
    }

    .actions .submit {
        width: auto;
        min-width: 220px;
    }
}

/* >= 1024px: breathing room */
@media (min-width: 1024px) {
    .card {
        max-width: 840px;
    }
}

/* Material tweaks for better mobile density */
::ng-deep .mat-mdc-form-field-infix {
    min-height: 56px;
    /* tap-friendly */
}

::ng-deep .mat-mdc-text-field-wrapper {
    border-radius: 12px;
}

/* สีพื้นหลังแดง ข้อความขาว */
.snack-error {
    background-color: #d32f2f !important;
    /* แดง */
    color: white !important;
}

/* หรือถ้าต้องการ success */
.snack-success {
    background-color: #388e3c !important;
    /* เขียว */
    color: white !important;
}