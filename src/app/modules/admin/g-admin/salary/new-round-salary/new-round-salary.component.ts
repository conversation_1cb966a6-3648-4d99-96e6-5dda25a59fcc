import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { debounceTime, map, merge, Observable, Subject, switchMap, takeUntil } from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA, MatDialogTitle, MatDialogContent, MatDialogActions } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { round, sortBy, startCase } from 'lodash-es';
import { AssetType, BranchPagination } from '../salary.types';
import { SalaryService } from '../salary.service';
import { CdkScrollable } from '@angular/cdk/scrolling';
import { MatFormField, MatPrefix, MatSuffix, MatError, MatFormFieldModule } from '@angular/material/form-field';
import { MatIconButton, MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { MatInput } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { months } from 'moment';
import { CommonModule } from '@angular/common';
// import { ImportOSMComponent } from '../card/import-osm/import-osm.component';

@Component({
    selector: 'new-round-salary',
    templateUrl: './new-round-salary.component.html',
    styleUrls: ['./new-round-salary.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush
    // animations: fuseAnimations
    ,
    imports: [CommonModule,MatDialogTitle, CdkScrollable, MatDialogContent, FormsModule, ReactiveFormsModule, MatFormField, MatIcon, MatInput, MatDialogActions, MatButton, MatFormFieldModule,
    MatSelectModule]
})

export class NewRoundSalaryComponent implements OnInit {
    itemtypeData: any;
    itemtypeId: string;
    formData: FormGroup
    flashErrorMessage: string;
    flashMessage: 'success' | 'error' | null = null;
    isLoading: boolean = false;
    years: any[] = []

    /**
     * Constructor
     */
    constructor(
        public dialogRef: MatDialogRef<NewRoundSalaryComponent>,
        @Inject(MAT_DIALOG_DATA) private _data,
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
        private _Service: SalaryService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService,
    ) {
        this.formData = this._formBuilder.group({
            year: this._data.year,
            month: this._data.month,
            round: this._data.round
        })

    }
    monthsThai = [
        { value: "01", name: 'มกราคม' },
        { value: "02", name: 'กุมภาพันธ์' },
        { value: "03", name: 'มีนาคม' },
        { value: "04", name: 'เมษายน' },
        { value: "05", name: 'พฤษภาคม' },
        { value: "06", name: 'มิถุนายน' },
        { value: "07", name: 'กรกฎาคม' },
        { value: "08", name: 'สิงหาคม' },
        { value: "09", name: 'กันยายน' },
        { value: "10", name: 'ตุลาคม' },
        { value: "11", name: 'พฤศจิกายน' },
        { value: "12", name: 'ธันวาคม' }
    ];
    ngOnInit(): void {
        const currentDate = new Date();
        const currentYear = currentDate.getFullYear();
        const startYear = currentYear - 10; // เริ่มต้นจาก 10 ปีก่อน
        const endYear = currentYear + 10; // สิ้นสุดที่ 10 ปีหลังจากนี้
        this.years = this.generateYears(startYear,endYear)
    }

        generateYears(start: number, end: number) {
        return Array.from({ length: end - start + 1 }, (_, i) => {
            const gregorianYear = start + i;
            const buddhistYear = gregorianYear + 543;
            return { value: gregorianYear, label: buddhistYear.toString() };
        });
    }

    close() {
        this.dialogRef.close();
    }

    create(): void {
        this.flashMessage = null;
        this.flashErrorMessage = null;
        const confirmation = this._fuseConfirmationService.open({
            "title": "สร้างรอบเงินเดือนใหม่",
            "message": "คุณต้องการสร้างรอบเงินเดือนใหม่ใช่หรือไม่ ",
            "icon": {
                "show": false,
                "name": "heroicons_outline:exclamation-triangle",
                "color": "warning"
            },
            "actions": {
                "confirm": {
                    "show": true,
                    "label": "ยืนยัน",
                    "color": "primary"
                },
                "cancel": {
                    "show": true,
                    "label": "ยกเลิก"
                }
            },
            "dismissible": true
        });

        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {

            // If the confirm button pressed...
            if (result === 'confirmed') {
                this._Service.createNewRoundSaraly(this.formData.value).subscribe({
                    next: (resp: any) => {
                        this.dialogRef.close(this.formData.value);
                    },
                    error: (err: any) => {
                        this._fuseConfirmationService.open({
                            "title": "เกิดข้อผิดพลาด",
                            "message": err.error.message,
                            "icon": {
                                "show": true,
                                "name": "heroicons_outline:exclamation-triangle",
                                "color": "warning"
                            },
                            "actions": {
                                "confirm": {
                                    "show": false,
                                    "label": "ยืนยัน",
                                    "color": "primary"
                                },
                                "cancel": {
                                    "show": false,
                                    "label": "ยกเลิก",

                                }
                            },
                            "dismissible": true
                        });
                    }
                });

            }
        });

    }
}
