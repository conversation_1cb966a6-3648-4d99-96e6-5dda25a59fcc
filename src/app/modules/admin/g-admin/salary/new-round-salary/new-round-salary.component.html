<div>
    <h1 mat-dialog-title class="text-xl font-semibold mb-4">สร้างรอบเงินเดือนใหม่</h1>
    <div mat-dialog-content class="">
        <form [formGroup]="formData">
            <div class="grid grid-cols-[20%_1fr] gap-3 items-center ">
                <label class="text-xl -mt-5">ปี</label>
                <mat-form-field appearance="fill" class="w-full">
                    <mat-select formControlName="year">
                        <mat-option *ngFor="let y of years;" [value]="y.value">{{ y.label }}</mat-option>
                    </mat-select>
                </mat-form-field>
                <label class="text-xl -mt-5">เดือน</label>
                <mat-form-field appearance="fill" class="w-full">
                    <mat-select formControlName="month">
                        <mat-option *ngFor="let m of monthsThai;" [value]="m.value">{{ m.name }}</mat-option>
                    </mat-select>
                </mat-form-field>
                <label class="text-xl -mt-5">รอบเดือน</label>
                <mat-form-field class="w-full">
                    <mat-select formControlName="round" >
                        <mat-option value="1">ครึ่งเดือนแรก</mat-option>
                        <mat-option value="2">ครึ่งเดือนหลัง</mat-option>
                    </mat-select>
                </mat-form-field>
             </div>
        </form>
    </div>
    <div mat-dialog-actions class="flex justify-end">
        <button mat-flat-button class="bg-gray-700 text-gray-700 hover:bg-gray-500
        dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4" (click)="close()">
            <mat-icon svgIcon="heroicons_solid:x-mark" class="mr-2"></mat-icon>
            ยกเลิก
        </button>
        <button class="px-6 ml-3 text-white" mat-flat-button [color]="'primary'" (click)="create()">
            บันทึก
        </button>
    </div>
</div>
