import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { debounceTime, map, merge, Observable, Subject, switchMap, takeUntil } from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { sortBy, startCase } from 'lodash-es';
import { AssetType, BranchPagination } from '../salary.types';
import { SalaryService } from '../salary.service';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatLabel } from '@angular/material/form-field';
import { MatOptionModule } from '@angular/material/core';
import { MatSelectModule } from '@angular/material/select';

@Component({
  selector: 'app-edit-bond',
  templateUrl: './edit-bond.component.html',
  styleUrl: './edit-bond.component.scss',
  changeDetection: ChangeDetectionStrategy.OnPush
  // animations: fuseAnimations
  ,
  imports: [FormsModule, ReactiveFormsModule, MatButton, MatIcon, MatFormFieldModule, MatLabel, MatOptionModule, MatSelectModule]
})
export class EditBondComponent {
  formData: FormGroup


  constructor(
    public dialogRef: MatDialogRef<EditBondComponent>,
    @Inject(MAT_DIALOG_DATA) private _data,
    private _changeDetectorRef: ChangeDetectorRef,
    private _fuseConfirmationService: FuseConfirmationService,
    private _formBuilder: FormBuilder,
    private _Service: SalaryService,
    private _matDialog: MatDialog,
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
    private _authService: AuthService,
  ) {
    this.formData = this._formBuilder.group({
      status: [this._data.data.status, Validators.required],
      balance: [this._data.data.balance, Validators.required],
    })
  }

  ngOnInit(): void {
    console.log(this.formData.value)
  }


  onClose(): void {
    this.dialogRef.close();
  }

  editBond(): void {
    const confirmation = this._fuseConfirmationService.open({
      "title": "แก้ไขเงินประกัน",
      "message": "คุณต้องการแก้ไขเงินประกันใช่หรือไม่ ",
      "icon": {
        "show": false,
        "name": "heroicons_outline:exclamation-triangle",
        "color": "warning"
      },
      "actions": {
        "confirm": {
          "show": true,
          "label": "ยืนยัน",
          "color": "primary"
        },
        "cancel": {
          "show": true,
          "label": "ยกเลิก"
        }
      },
      "dismissible": true
    });

    // Subscribe to the confirmation dialog closed action
    confirmation.afterClosed().subscribe((result) => {

      // If the confirm button pressed...
      if (result === 'confirmed') {
        this._Service.putBond(this._data?.id, this.formData.value).subscribe({
          next: (resp: any) => {
            this.dialogRef.close();
          },
          error: (err: any) => {

            this._fuseConfirmationService.open({
              "title": "เกิดข้อผิดพลาด",
              "message": err.error.message,
              "icon": {
                "show": true,
                "name": "heroicons_outline:exclamation-triangle",
                "color": "warning"
              },
              "actions": {
                "confirm": {
                  "show": false,
                  "label": "ยืนยัน",
                  "color": "primary"
                },
                "cancel": {
                  "show": false,
                  "label": "ยกเลิก",

                }
              },
              "dismissible": true
            });
          }
        });

      }
    });

  }
}
