<div class="w-full">
    <h1 mat-dialog-title class="text-xl font-semibold mb-4 ">นำเข้าข้อมูลพนักงาน</h1>
    <div mat-dialog-content class="">
        <form [formGroup]="formData">
            <div class="col-span-5 grid grid-cols-12 gap-4 items-center">
                <div class="grid items-center col-span-12">
                    <mat-form-field class="w-full" (click)="file_name.click()">
                        <div matPrefix class="cursor-pointer pr-2">
                            <p>upload file |</p>
                        </div>
                        <button mat-icon-button matSuffix>
                            <mat-icon>attach_file</mat-icon>
                        </button>
                        <input type="text" readonly matInput [formControlName]="'file_name'" />
                        <input type="file" hidden #file_name (change)="onSelect(file_name.files, 'addfile', 0)"
                            accept=".zip,.rar,.7z" />
                        <mat-error>กรุณาเลือกไฟล์</mat-error>
                    </mat-form-field>
                </div>
            </div>
        </form>
    </div>
    <div mat-dialog-actions class="flex justify-end">
        <!-- ปุ่มยกเลิก -->
        <button mat-flat-button
            class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
            (click)="onClose()">
            <mat-icon svgIcon="heroicons_solid:x-mark" class="mr-2"></mat-icon>
            ยกเลิก
        </button>
        <!-- ปุ่มบันทึกสีเขียวพร้อม icon -->
        <button mat-flat-button
            class="bg-green-600 hover:bg-green-700 text-white rounded-lg py-2 px-6 ml-3 flex items-center"
            (click)="Update()">
            <mat-icon class="mr-2">save</mat-icon>
            บันทึก
        </button>
    </div>

</div>