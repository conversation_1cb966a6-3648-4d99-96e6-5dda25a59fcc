<form class="flex flex-col w-full" [formGroup]="formData">
    <span class="text-2xl">พิมพ์สลิป</span>
    <div class="mt-3">
        <label class="font-bold">เลือกรูปแบบ</label>
        <mat-form-field class="w-full">
            <mat-select formControlName="round">
                <mat-option
                    *ngFor="let item of ['พิมพ์สลิปทั้งเดือน','พิมพ์สลิปครึ่งเดือนแรก','พิมพ์สลิปครึ่งเดือนหลัง']; let i = index"
                    [value]="i" [disabled]="i !== 0 && i !== _data.round">
                    {{item}}
                </mat-option>
            </mat-select>
        </mat-form-field>
    </div>
    <div class="mt-3 flex flex-col">
        <label class="font-bold">เลือกแสดงผลรายการเงินได้เงินหัก</label>
        <mat-radio-group [formControlName]="'type'">
            <mat-radio-button [value]="'visible'">แสดงบางส่วน</mat-radio-button>
            <mat-radio-button [value]="'all'" class="pl-2">แสดงทั้งหมด</mat-radio-button>
        </mat-radio-group>
    </div>
    <div class="flex justify-end gap-3 w-full">
        <button mat-flat-button (click)="onClose()"
            class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4">
            <mat-icon svgIcon="heroicons_solid:x-mark" class="mr-2"></mat-icon>
            <span class="ml-2 mr-1">ยกเลิก</span>
        </button>
        <button mat-flat-button (click)="print()"
            class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4">
            <mat-icon class="mr-2">print</mat-icon>
            <span class="ml-2 mr-1">พิมพ์สลิป</span>
        </button>
    </div>
</form>