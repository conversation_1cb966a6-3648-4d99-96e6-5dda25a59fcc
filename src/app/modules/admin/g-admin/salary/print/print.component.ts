import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, DecimalPipe } from '@angular/common';
import { Component, Inject } from '@angular/core';
import { FormGroup, FormControl, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { <PERSON><PERSON><PERSON>on, MatIconButton } from '@angular/material/button';
import { MatOption } from '@angular/material/core';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatForm<PERSON>ield, MatLabel } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatProgressBar } from '@angular/material/progress-bar';
import { MatSelect } from '@angular/material/select';
import { DataTablesModule } from 'angular-datatables';
import { environment } from 'environments/environment';
import { FormBuilder } from '@angular/forms';
import { MatRadioButton, MatRadioGroup } from '@angular/material/radio';

@Component({
    selector: 'app-print',
    imports: [<PERSON><PERSON><PERSON>, Mat<PERSON>rogressBar, FormsModule,MatButton, ReactiveFormsModule, MatFormField, MatLabel, MatSelect, NgFor, MatOption, MatButton, MatIcon, DataTablesModule, MatIconButton, DecimalPipe, MatRadioButton, MatRadioGroup],
    templateUrl: './print.component.html',
    styleUrl: './print.component.scss'
})
export class PrintComponent {
    formData: FormGroup

    // form = new FormGroup({
    //     round: new FormControl(0),
    // });

    constructor(
        public dialogRef: MatDialogRef<PrintComponent>,
        @Inject(MAT_DIALOG_DATA) private _data,
        private _formBuilder: FormBuilder,
    ) {
        this.formData = this._formBuilder.group({
            round: [0],
            type: [null],
        })
    }

    print() {
        if (this.formData.value.round > 0) {
            window.open(environment.API_URL + 'api/report_salary_user?'
                +`user_id=${this._data.user_id}&month=${this._data.date.month}&year=${this._data.date.year}&round=${this.formData.value.round}&type=${this.formData.value.type}`
            )
        }
        else {
            window.open(environment.API_URL + 'api/report_salary_user?'
                +`user_id=${this._data.user_id}&month=${this._data.date.month}&year=${this._data.date.year}&type=${this.formData.value.type}`
            )
        }
    }
    onClose(): void {
        this.dialogRef.close();
    }
}
