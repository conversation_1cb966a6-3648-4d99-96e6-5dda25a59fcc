import { map, Subscription } from 'rxjs';
import { data } from 'jquery';
import { CommonModule, NgIf, NgFor } from '@angular/common';
import { Component, Inject, OnInit } from '@angular/core';
import { FormArray, FormBuilder, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { DataTablesModule } from 'angular-datatables';
import { MAT_DIALOG_DATA, MatDialogRef } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { NgxMaskDirective } from 'ngx-mask';
import { SalaryService } from '../salary.service';
import { MatSelectModule } from '@angular/material/select';
import { Dialog } from '@angular/cdk/dialog';
import { FuseConfirmationService } from '@fuse/services/confirmation';

@Component({
    selector: 'app-edit-income',
    imports: [
        CommonModule,
        FormsModule,
        MatButton,
        ReactiveFormsModule,
        MatIcon,
        DataTablesModule,
        MatFormFieldModule,
        MatSelectModule,
        MatInputModule],
    standalone: true,
    templateUrl: './edit-income.component.html',
    styleUrl: './edit-income.component.scss'
})
export class EditIncomeComponent implements OnInit {
    id: any;
    data: any;
    form: FormGroup;
    incomeType: any;
    deductType: any;

    constructor(
        @Inject(MAT_DIALOG_DATA) private _data,
        private fb: FormBuilder,
        private _service: SalaryService,
        private dialogRef: MatDialogRef<EditIncomeComponent>,
        private _fuseConfirmationService: FuseConfirmationService,

    ) { }
    ngOnInit(): void {
        this.id = this._data.id
        this.data = this._data.data
        this.form = this.fb.group({
            income: this.fb.array(this.data.income_paids.map(item => this.createIncome(item))),
            deduct: this.fb.array(this.data.deduct_paids.map(item => this.createDeduct(item))),
        });
        this._service.getIncometype().subscribe(data => { this.incomeType = data.data; })
        this._service.getDeducttype().subscribe(data => { this.deductType = data.data; })
    }

    createIncome(item?: any): FormGroup {
        const group = this.fb.group({
            price: [item?.price ?? 0, Validators.required],
            income_type_id: [item?.income_type_id ?? null, [Validators.required]],
            salary_withdraw: 0,
            description: [item?.description ?? null]
        });
        group.get('income_type_id')!.valueChanges.subscribe((selectedId) => {
            const selectedType = this.incomeType.find(t => t.id === selectedId);
            group.get('description')!.setValue(selectedType.description);
        });
        return group
    }

    createDeduct(item?: any): FormGroup {
        const group = this.fb.group({
            price: [item?.price ?? 0, Validators.required],
            deduct_type_id: [item?.deduct_type_id ?? null, [Validators.required]],
            description: [item?.description ?? null]
        });
        group.get('deduct_type_id')!.valueChanges.subscribe((selectedId) => {
            const selectedType = this.deductType.find(t => t.id === selectedId);
            group.get('description')!.setValue(selectedType.description);
        });
        return group
    }

    get income(): FormArray {
        return this.form.get('income') as FormArray;
    }

    get deduct(): FormArray {
        return this.form.get('deduct') as FormArray;
    }

    addIncome(): void {
        this.income.push(this.createIncome());
    }
    addDeduct(): void {
        this.deduct.push(this.createDeduct());
    }

    removeIncome(index: number): void {
        this.income.removeAt(index);
    }
    removeDeduct(index: number): void {
        this.deduct.removeAt(index);
    }
    submit(): void {
        this.data.income_paids = this.income.value
        this.data.deduct_paids = this.deduct.value
        this._service.editIncomeDeduct(this.id, this.data).subscribe({
            next: (res: any) => {
                this.dialogRef.close(this.data);
            },
        })
    }
}
