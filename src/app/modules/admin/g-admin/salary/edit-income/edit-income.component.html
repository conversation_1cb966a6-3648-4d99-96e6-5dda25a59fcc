<div class="flex flex-col gap-3 p-4">
    <div class="flex justify-between items-center">
        <span class="text-2xl font-bold">แก้ไขเงินได้/เงินหัก</span>
        <div class="flex justify-end gap-5">
            <button mat-flat-button
                class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                (click)="addIncome()">
                <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                <span class="ml-2 mr-1">เพิ่มเงินได้</span>
            </button>
            <button mat-flat-button
                class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                (click)="addDeduct()">
                <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                <span class="ml-2 mr-1">เพิ่มเงินหัก</span>
            </button>
        </div>
    </div>

  <form class="flex flex-col gap-3" [formGroup]="form">
    <div class="flex flex-col overflow-auto rounded-md max-h-60 ">
      <span class="text-xl font-bold">เงินได้</span>

      <table class="w-full" >
        <thead>
          <tr class="bg-gray-300 border">
            <th class="p-3">รายการ</th>
            <th class="p-3">จำนวนเงิน</th>
            <th class="p-3">หมายเหตุ</th>
            <th class="p-3"></th>
          </tr>
        </thead>
        <tbody formArrayName="income">
          <tr *ngFor="let group of income.controls; let i = index" [formGroupName]="i">
            <td class="px-3">
              <mat-form-field class="w-full">
                <mat-select formControlName="income_type_id">
                  <mat-option *ngFor="let type of incomeType" [value]="type.id">
                    {{ type.name }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </td>
            <td class="px-3">
              <mat-form-field class="w-full">
                <input matInput type="number" formControlName="price" />
              </mat-form-field>
            </td>
            <td class="px-3">
              <mat-form-field class="w-full">
                <input matInput formControlName="description" readonly/>
              </mat-form-field>
            </td>
            <td class="px-3 text-center">
              <button mat-icon-button color="warn" (click)="removeIncome(i)">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </tr>
            <tr *ngIf="income.controls.length == 0" >
                <td colspan="4" class="p-3 text-center">ไม่มีข้อมูล</td>
            </tr>
        </tbody>
      </table>
    </div>

    <div class="flex flex-col overflow-auto rounded-md max-h-60">
      <span class="text-xl font-bold">เงินหัก</span>

      <table class="w-full">
        <thead>
          <tr class="bg-gray-300 border">
            <th class="p-3">รายการ</th>
            <th class="p-3">จำนวนเงิน</th>
            <th class="p-3">หมายเหตุ</th>
            <th class="p-3"></th>
          </tr>
        </thead>
        <tbody formArrayName="deduct">
          <tr *ngFor="let group of deduct.controls; let i = index" [formGroupName]="i">
            <td class="px-3">
              <mat-form-field class="w-full">
                <mat-select formControlName="deduct_type_id">
                  <mat-option *ngFor="let type of deductType" [value]="type.id">
                    {{ type.name }}
                  </mat-option>
                </mat-select>
              </mat-form-field>
            </td>
            <td class="px-3">
              <mat-form-field class="w-full">
                <input matInput type="number" formControlName="price" />
              </mat-form-field>
            </td>
            <td class="px-3">
              <mat-form-field class="w-full">
                <input matInput formControlName="description" readonly/>
              </mat-form-field>
            </td>
            <td class="px-3 text-center">
              <button mat-icon-button color="warn" (click)="removeDeduct(i)">
                <mat-icon>delete</mat-icon>
              </button>
            </td>
          </tr>
            <tr *ngIf="deduct.controls.length == 0" >
                <td colspan="4" class="p-3 text-center">ไม่มีข้อมูล</td>
            </tr>
        </tbody>
      </table>
    </div>

    <div class="flex justify-end">
        <button mat-flat-button type="submit" (click)="submit()"
            class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4">
            <mat-icon [svgIcon]="'heroicons_outline:document'"></mat-icon>
            <span class="ml-2 mr-1">บันทึก</span>
        </button>
    </div>
  </form>
</div>
