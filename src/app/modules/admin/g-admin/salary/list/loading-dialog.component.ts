import { CommonModule, NgIf } from '@angular/common';
import { Component } from '@angular/core';
import { MatDialogModule, MatDialogRef } from '@angular/material/dialog';
import { MatProgressBarModule } from '@angular/material/progress-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';

@Component({
    standalone: true,
    selector: 'app-loading-dialog',
    template: `
    <div class="bg-white rounded-2xl shadow-xl p-8 min-w-[260px] min-h-[160px] flex flex-col items-center justify-center space-y-5">
      <div class="relative w-14 h-14">
        <div class="absolute inset-0 rounded-full border-4 border-green-500 border-t-transparent animate-spin"></div>
        <div class="absolute inset-1 rounded-full bg-white"></div>
      </div>
      <p class="text-gray-800 text-base font-medium tracking-wide">กำลังประมวลผล...</p>
    </div>
  `,
    styleUrls: ['./loading-dialog.component.scss'],
    imports: [
        MatProgressSpinnerModule,
        CommonModule,
        NgIf,
    ],
})
export class LoadingDialogComponent {
    constructor(public dialogRef: MatDialogRef<LoadingDialogComponent>) { }
}
