import { CommonModule } from '@angular/common';
import { Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButton, MatIconButton } from '@angular/material/button';
import { MatOption } from '@angular/material/core';
import { <PERSON>F<PERSON><PERSON>ield } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput } from '@angular/material/input';
import { MatSelect } from '@angular/material/select';
import { DataTablesModule } from 'angular-datatables';
import { GeneralService } from './general.service';

@Component({
    selector: 'app-general',
    imports: [MatFormField, MatIcon, MatInput, MatButton, FormsModule, ReactiveFormsModule,
        MatSelect, MatOption, DataTablesModule, MatIconButton, ],
    templateUrl: './general.component.html',
    styleUrl: './general.component.scss'
})
export class GeneralComponent {
    constructor(
        private _service: GeneralService
    ) {

    }
    line: any;
    data = null;
    updateLine() {
        this._service.updateLine(this.data).subscribe(data => {
            this.line = data;
        })
    }
}
