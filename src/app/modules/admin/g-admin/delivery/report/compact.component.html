<div class="absolute inset-0 min-w-0 text-center overflow-auto print:overflow-visible" cdkScrollable>

    <!-- Main -->
    <div class="inline-block p-6 sm:p-10 text-left print:p-0">

        <!-- Invoice -->
        <div
            class="w-240 p-16 rounded-2xl shadow bg-card print:w-auto print:rounded-none print:shadow-none print:bg-transparent">


            <div class="text-md">
                <div class="text-xl font-medium">รายการจัดส่งสินค้า </div>
                <div class="text-m mt-3">ตั้งแต่วันที่ {{this.date_start ?? '-'}} ถึงวันที่ {{this.date_end ?? '-'}}</div>
            </div>

            <div class="grid grid-cols-12 gap-x-1 mt-12">
                <!-- Columns -->
                <div class="font-medium text- text-secondary col-span-2">เลขที่ออเดอร์</div>
                <div class="font-medium text-secondary col-span-2">สินค้า</div>
                <div class="font-medium text-secondary ">จำนวน</div>
                <div class="font-medium text-secondary col-span-2">ชื่อลูกค้า</div>
                <div class="font-medium text-secondary col-span-2">เบอร์ติดต่อ</div>
                <div class="font-medium text-secondary col-span-2">ที่อยู่</div>
                <div class="font-medium text-secondary ">เลขที่ขนส่ง</div>

                <!-- Divider -->
                <ng-container *ngFor="let item of dataRow ; let i = index;">
                    <div class="col-span-12 my-4 border-b"></div>
                    <!-- Item -->
                    <div class="self-center col-span-2">{{item.sale_order.order_id}}</div>
                    <div class="self-center col-span-2">{{item.item_name}}</div>
                    <div class="self-center">{{item.qty}}</div>
                    <div class="self-center col-span-2">{{item.sale_order.name}}</div>
                    <div class="self-center col-span-2">{{item.sale_order.telephone}}</div>
                    <div class="self-center col-span-2">{{item.sale_order.address}}</div>
                    <div class="self-center ">{{item.sale_order.track_no}}</div>
                </ng-container>
            </div>
        </div>
    </div>
</div>
