import { CdkScrollable } from '@angular/cdk/scrolling';
import { ChangeDetectionStrategy, Component, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute } from '@angular/router';
import { DeliveryService } from '../delivery.service';
import { NgFor } from '@angular/common';

@Component({
    selector: 'compact',
    templateUrl: './compact.component.html',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.Default,
    imports: [CdkScrollable, NgFor]
})
export class CompactComponent implements OnInit
{
    /**
     * Constructor
     */
    date_start: string;
    date_end: string;
    dataRow: any[] = [];
    constructor(
        private _activatedRoute: ActivatedRoute,
        private _service : DeliveryService
    )
    {
    }

    ngOnInit(): void {
        this.date_start = this._activatedRoute.snapshot.paramMap.get('start');
        this.date_end = this._activatedRoute.snapshot.paramMap.get('end');
        let formValue = {
            date_start : this._activatedRoute.snapshot.paramMap.get('start'),
            date_end : this._activatedRoute.snapshot.paramMap.get('end')
        }
        this._service.getdeliveryReport(formValue).subscribe((resp)=>{
            this.dataRow = resp.data
            if(resp) {
                setTimeout(() => {
                    window.print()
                }, 3000);
            }
        })

    }
}
