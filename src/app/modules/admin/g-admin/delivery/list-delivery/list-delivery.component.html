<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">

    <div class="relative flex flex-col sm:flex-row items-center justify-between py-8 px-6 md:px-8 border-b bg-white ">
        <div class="text-4xl font-extrabold tracking-tight text-gray-900">รายการช่องทางการส่งของ</div>
        <!-- <div *ngIf="roleType == 'marketing'" class="flex items-center mt-6 sm:mt-0">


        </div> -->
    </div>


    <!-- Main -->
    <div class="flex flex-col flex-auto min-w-0 bg-gray-100 p-6">
        <div class="overflow-auto bg-white shadow-lg rounded-lg">
            <div class="flex flex-row justify-between py-2 px-5">
                <div class="flex justify-start items-center w-[405px] mt-5">
                    <div
                        class="flex text-md font-normal tracking-tight leading-7 sm:leading-10 truncate justify-center items-center">
                        <mat-form-field [ngClass]="formFieldHelpers" class="min-w-[405px] min-h-[40px]">
                            <mat-icon svgIcon="search"></mat-icon>
                            <input matInput placeholder="ค้นหา" [(ngModel)]="searchQuery"
                                (ngModelChange)="applySearch()">
                        </mat-form-field>
                    </div>
                </div>
                <div class="flex">
                    <div *ngIf="!hiddenSave()"
                        class="flex text-md font-normal tracking-tight leading-7 sm:leading-10 truncate justify-center items-center">
                        <div class="flex items-center mt-6 sm:mt-0"></div>
                        <a (click)="New()" class="ml-4  px-2 py-1 gradient-button" *ngIf="!hiddenSave()">
                            <mat-icon [ngStyle]="{'color':'white'}" [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                            <span class="ml-1 mr-1">เพิ่มข้อมูล</span>
                        </a>
                    </div>
                </div>
            </div>
            <div class="flex flex-col p-6 sm:overflow-auto overflow-x-scroll">
                <table datatable [dtOptions]="dtOptions" class="table w-full text-left text-gray-500 overflow-hidden">
                    <thead class="bg-gray-300 text-black">
                        <tr>
                            <th class="text-center ">จัดการ</th>
                            <th class="text-center  ">ลำดับ</th>
                            <th class="text-center">รูปภาพ</th>
                            <th class="text-center">ชื่อบริษัทขนส่ง</th>
                            <th class="text-center">สร้างโดย</th>
                            <th class="text-center">วันที่สร้าง</th>
                            <th class="text-center">สถานะ</th>

                        </tr>
                    </thead>
                    <tbody *ngIf="dataRow?.length != 0" class="divide-y divide-gray-200">
                        <tr *ngFor="let item of dataRow; let i = index"
                            class="bg-white hover:bg-gray-200 transition-colors duration-300 ease-in-out">

                            <td class="text-center whitespace-nowrap">
                                <button mat-icon-button (click)="Edit(item.id)" class="icon-button edit-button"
                                    aria-label="Edit">
                                    <mat-icon class="">edit</mat-icon>
                                </button>
                                <button mat-icon-button (click)="Delete(item.id)" class="icon-button delete-button"
                                    aria-label="Delete">
                                    <mat-icon class="delete-icon">delete</mat-icon>
                                </button>
                            </td>
                            <td class="text-center whitespace-nowrap">{{ pages.begin + (i + 1) }}</td>
                            <td class="text-center whitespace-nowrap">
                                <div class="flex justify-center items-center h-full">
                                    <img src="{{item.image}}" class="h-14 w-14 rounded-full shadow-md" alt="">
                                </div>
                            </td>

                            <td class="text-center whitespace-nowrap">{{ item.name }}</td>

                            <td class="text-center whitespace-nowrap">{{ item.user_create ? item.user_create.first_name
                                : 'ไม่มีข้อมูล' }} {{ item.user_create ?
                                item.user_create.last_name: '' }}
                            </td>
                            <!-- <td>{{ item.created_at | thaiDate }}</td> -->
                            <td class="text-center whitespace-nowrap">{{ item.created_at | date: 'dd/MM/yyyy HH:mm' }}
                            </td>
                            <td class="text-center whitespace-nowrap">
                                <span *ngIf="item.status === 1"
                                    class="bg-green-200 text-green-800 px-3 py-1 rounded-full text-xs font-medium">เปิดการใช้งาน</span>
                                <span *ngIf="item.status === 0"
                                    class="bg-red-200 text-red-800 px-3 py-1 rounded-full text-xs font-medium">ปิดการใช้งาน</span>
                            </td>

                        </tr>
                    </tbody>
                    <tbody *ngIf="dataRow?.length == 0">
                        <tr>
                            <td colspan="7" class="no-data-available text-center">ไม่มีข้อมูล !</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>