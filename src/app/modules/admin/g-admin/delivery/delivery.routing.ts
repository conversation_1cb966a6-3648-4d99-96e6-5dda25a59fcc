import { Route, Routes } from '@angular/router';
import { DefaultDataPoint } from 'chart.js';

export default [
    // {
    //     path: '',
    //     pathMatch: 'full',
    //     redirectTo: 'brief-plan'
    // },
    {
        path: '',
        loadComponent: () => import('./delivery.component').then(m => m.DeliveryComponent),
        children: [
            {
                path: 'list',
                loadComponent: () => import('./list-delivery/list-delivery.component').then(m => m.ListDeliveryComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },
            {
                path: 'new-delivery',
                loadComponent: () => import('./new-delivery/new-delivery.component').then(m => m.NewDeliveryComponent),
                // resolve: {
                //     permission: PermissionProductsResolver,
                //     department: DepartmentResolver,
                //     resolveGet: PositionResolve,
                //     branch: BranchResolver,
                // }
            },
            {
                path: 'edit/:id',
                loadComponent: () => import('./edit-delivery/edit-delivery.component').then(m => m.EditDeliveryComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },

            {
                path: 'report/:start/:end',
                loadComponent: () => import('./report/compact.component').then(m => m.CompactComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },

            {
                path: 'report',
                loadComponent: () => import('./report-preview/page.component').then(m => m.DeliveryReportComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },




        ]
        /*children : [
            {
                path     : '',
                component: ContactsListComponent,
                resolve  : {
                    tasks    : ContactsResolver,
                    countries: ContactsCountriesResolver
                },
                children : [
                    {
                        path         : ':id',
                        component    : ContactsDetailsComponent,
                        resolve      : {
                            task     : ContactsContactResolver,
                            countries: ContactsCountriesResolver
                        },
                        canDeactivate: [CanDeactivateContactsDetails]
                    }
                ]
            }
        ]*/
    }
]  as Routes
