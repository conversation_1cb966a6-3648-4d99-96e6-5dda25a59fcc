<div class="grid grid-cols-1 xl:grid-cols-3 gap-2 w-full mt-8">
    <div class="flex-auto p-2 sm:p-10 col-span-3">
        <form [formGroup]="form">
            <div class="grid gap-4 w-full sm:grid-cols-1 md:grid-cols-1 ">
                <div class="bg-card rounded-2xl md:shadow overflow-hidden px-8 pt-6 pb-8 mb-4 flex flex-col my-2">
                    <div class="-mx-3 md:flex my-1">
                        <div class="md:w-full px-3">
                            <mat-form-field class="w-full">
                                <mat-label>วันที่เริ่ม</mat-label>
                                <input matInput [matDatepicker]="start" formControlName="date_start">
                                <mat-datepicker-toggle matIconSuffix [for]="start"></mat-datepicker-toggle>
                                <mat-datepicker #start></mat-datepicker>
                            </mat-form-field>
                        </div>
                        <div class="md:w-full px-3 mb-6 md:mb-0">
                            <mat-form-field class="w-full">
                                <mat-label>วันที่สิ้นสุด</mat-label>
                                <input matInput [matDatepicker]="end" formControlName="date_end">
                                <mat-datepicker-toggle matIconSuffix [for]="end"></mat-datepicker-toggle>
                                <mat-datepicker #end></mat-datepicker>
                            </mat-form-field>
                        </div>
                    </div>
                    <div mat-dialog-actions class="flex justify-end mt-2">
                        <button class="px-6 ml-3" mat-flat-button>
                            ยกเลิก
                        </button>
                        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="submit()">
                            ตกลง
                        </button>
                    </div>
                </div>
            </div>
        </form>
        <div class="xl:col-span-3 flex flex-col flex-auto bg-card shadow rounded-2xl overflow-hidden">
            <div class="overflow-x-auto mx-6">
                <div mat-dialog-actions class="flex justify-end mt-6">
                    <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="printReport()">
                        <mat-icon svgIcon="heroicons_solid:printer"></mat-icon>&nbsp; ปริ้น
                    </button>
                </div>
                <table class="w-full bg-transparent" mat-table matSort [dataSource]="recentTransactionsDataSource"
                    [trackBy]="trackByFn" #recentTransactionsTable>
                    <!-- Transaction ID -->
                    <!-- Date -->
                    <ng-container matColumnDef="order_id">
                        <th mat-header-cell mat-sort-header *matHeaderCellDef>
                            เลขออเดอร์
                        </th>
                        <td mat-cell *matCellDef="let transaction">
                            <span class="pr-6 font-medium text-sm text-secondary whitespace-nowrap">
                                {{transaction.sale_order.order_id}}
                            </span>
                        </td>
                    </ng-container>

                    <!-- Name -->
                    <ng-container matColumnDef="item_name">
                        <th mat-header-cell mat-sort-header *matHeaderCellDef>
                            สินค้า
                        </th>
                        <td mat-cell *matCellDef="let transaction">
                            <span class="pr-6 font-medium text-sm text-secondary whitespace-nowrap">
                                {{transaction.item_name}}
                            </span>
                        </td>
                    </ng-container>

                    <!-- Amount -->
                    <ng-container matColumnDef="qty">
                        <th mat-header-cell mat-sort-header *matHeaderCellDef>
                            จำนวน
                        </th>
                        <td mat-cell *matCellDef="let transaction">
                            <span class="pr-6 font-medium text-sm text-secondary whitespace-nowrap">
                                {{transaction.qty}}
                            </span>
                        </td>
                    </ng-container>

                    <!-- Status -->
                    <ng-container matColumnDef="name">
                        <th mat-header-cell mat-sort-header *matHeaderCellDef>
                            ชื่อลูกค้า
                        </th>
                        <td mat-cell *matCellDef="let transaction">
                            <span class="pr-6 font-medium text-sm text-secondary whitespace-nowrap">
                                {{transaction.sale_order.name}}
                            </span>
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="telephone">
                        <th mat-header-cell mat-sort-header *matHeaderCellDef>
                            เบอร์ติดต่อ
                        </th>
                        <td mat-cell *matCellDef="let transaction">
                            <span class="pr-6 font-medium text-sm text-secondary whitespace-nowrap">
                                {{transaction.sale_order.telephone}}
                            </span>
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="address">
                        <th mat-header-cell mat-sort-header *matHeaderCellDef>
                            ที่อยู่
                        </th>
                        <td mat-cell *matCellDef="let transaction">
                            <span class="pr-6 font-medium text-sm text-secondary whitespace-nowrap">
                                {{transaction.sale_order.address}}
                            </span>
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="payment_type">
                        <th mat-header-cell mat-sort-header *matHeaderCellDef>
                            ประเภทการจ่าย
                        </th>
                        <td mat-cell *matCellDef="let transaction">
                            <span class="pr-6 font-medium text-sm text-secondary whitespace-nowrap">
                                {{transaction.sale_order.payment_type}}
                            </span>
                        </td>
                    </ng-container>
                    <ng-container matColumnDef="track_no">
                        <th mat-header-cell mat-sort-header *matHeaderCellDef>
                            เลขที่ขนส่ง
                        </th>
                        <td mat-cell *matCellDef="let transaction">
                            <span class="pr-6 font-medium text-sm text-secondary whitespace-nowrap">
                                {{transaction.sale_order.track_no}}
                            </span>
                        </td>
                    </ng-container>


                    <!-- Footer -->


                    <tr mat-header-row *matHeaderRowDef="recentTransactionsTableColumns"></tr>
                    <tr class="order-row h-16" mat-row *matRowDef="let row; columns: recentTransactionsTableColumns;"></tr>
                    <!-- <tr
                        class="h-16 border-0"
                        mat-footer-row
                        *matFooterRowDef="['recentOrdersTableFooter']"></tr> -->
                </table>
            </div>
        </div>
    </div>
    <!-- Recent transactions table -->
</div>
