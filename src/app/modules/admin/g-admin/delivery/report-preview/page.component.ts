import { <PERSON><PERSON><PERSON>wInit, ChangeDetectorRef, Component, Inject, <PERSON><PERSON><PERSON>roy, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { Mat<PERSON>ort, MatSortHeader } from '@angular/material/sort';
import { fuseAnimations } from '@fuse/animations';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA, MatDialogActions } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { ItemTypeService } from '../../item-type/item-type.service';
import { LocationService } from '../../location/location.service';
import { VendorService } from '../../vendor/vendor.service';
import { MatTableDataSource, MatTable, MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatCellDef, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er<PERSON>owDef, <PERSON><PERSON><PERSON>er<PERSON>ow, MatRowDef, MatRow } from '@angular/material/table';
import { Subject } from 'rxjs';
import { DeliveryService } from '../delivery.service';
import moment from 'moment';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatFormField, MatLabel, MatSuffix } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { MatDatepickerInput, MatDatepickerToggle, MatDatepicker } from '@angular/material/datepicker';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';

@Component({
    selector: 'app-dialog-edit-customer',
    templateUrl: './page.component.html',
    styleUrls: ['./page.component.scss'],
    animations: fuseAnimations,
    imports: [FormsModule, ReactiveFormsModule, MatFormField, MatLabel, MatInput, MatDatepickerInput, MatDatepickerToggle, MatSuffix, MatDatepicker, MatDialogActions, MatButton, MatIcon, MatTable, MatSort, MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatSortHeader, MatCellDef, MatCell, MatHeaderRowDef, MatHeaderRow, MatRowDef, MatRow]
})

export class DeliveryReportComponent implements OnInit, AfterViewInit, OnDestroy {

    ACTION :string = 'HISTORY';
    @ViewChild('recentTransactionsTable', {read: MatSort}) recentTransactionsTableMatSort: MatSort;

    data: any;
    recentTransactionsDataSource: MatTableDataSource<any> = new MatTableDataSource();
    recentTransactionsTableColumns: string[] = [
        'order_id',
        'item_name',
        'qty',
        'name',
        'telephone',
        'address',
        'payment_type',
        'track_no'

    ];
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    isLoading: boolean = false;
    form: FormGroup
    filterData = [];
    dataRow: any;
    rawDataFilter: any[] = []
    /**
     * Constructor
     */
    constructor(
        // public dialogRef: MatDialogRef<DeliveryReportComponent>,
        private _formBuilder: FormBuilder,
        private  _service: DeliveryService,
        private _router: Router,
        private _fuseConfirmationService: FuseConfirmationService,

    ) {
        this.form = this._formBuilder.group({
            date_start: '',
            date_end: ''
        })
    }


    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {




    }

    trackByFn(index: number, item: any): any
    {
        return item.id || index;
    }


    submit(): void {
        this._service.getdeliveryReport(this.form.value).subscribe((resp: any)=> {
            this.recentTransactionsDataSource.data = resp.data;
        })
    }
    printReport(): void {
        if(!this.form.value.date_start || !this.form.value.date_end) {
            const confirmation = this._fuseConfirmationService.open(      {
                "title": "เกิดข้อผิดพลาด",
                "message": " <span class=\"font-medium\">กรุณาเลือกวันที่เริ่มและวันที่สิ้นสุด</span>",
                "icon": {
                  "show": true,
                  "name": "heroicons_outline:exclamation-triangle-triangle",
                  "color": "warn"
                },
                "actions": {
                  "confirm": {
                    "show": true,
                    "label": "ตกลง",
                    "color": "warn"
                  },
                  "cancel": {
                    "show": false,
                    "label": "Cancel"
                  }
                },
                "dismissible": true
              });
        } else {
            const start = moment(this.form.value.date_start).format('YYYY-MM-DD')
            const end = moment(this.form.value.date_end).format('YYYY-MM-DD')
            this._router.navigate(['delivery/report/' + start + '/' + end])
        }

    }

    onClose() {
        // this.dialogRef.close();
    }

    /**
     * After view init
     */
    ngAfterViewInit(): void {

    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        // this.addItem();
    }

}
