<div
    class="flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between p-1 sm:pb-4 sm:px-10 border-b bg-card dark:bg-transparent">
    <div class="flex-1 min-w-0">
        <!-- Title -->
        <div class="mt-2">
            <h1 class="text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                เพิ่มช่องทางการส่งของ
            </h1>
        </div>
    </div>
</div>
<div class="flex-auto p-3 sm:p-2">
    <form [formGroup]="formData">
        <div class="flex-wrap">
            <div class="w-full">
                <div class="flex mt-4 px-5">
                    <label for="name"
                        class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">ชื่อบริษัทขนส่ง</label>
                    <mat-form-field class="w-full pr-2">
                        <input matInput [formControlName]="'name'" placeholder="ชื่อบริษัทขนส่ง">
                    </mat-form-field>
                </div>
            </div>
            <div class="w-full">
                <div class="flex mt-2 px-5">
                    <label for="code"
                        class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">เลือกรูปบริษัทขนส่ง</label>
                    <div class="flex justify-start items-start w-full">
                        <ngx-dropzone style="width: 400px; height: 200px;" (change)="onSelect($event)">
                            <ngx-dropzone-label>เลือกรูปบริษัทขนส่ง </ngx-dropzone-label>
                            <ngx-dropzone-image-preview *ngFor="let f of files" [removable]="true" [file]="f"
                                (removed)="onRemove(f)">
                            </ngx-dropzone-image-preview>
                        </ngx-dropzone>
                    </div>
                </div>
            </div>
        </div>
        <div class="flex justify-end space-x-4 border-t border-gray-200 dark:border-gray-700 pt-4 mt-4">
            <button mat-flat-button class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4" (click)="onClose()">
                <mat-icon svgIcon="heroicons_solid:x-mark" class="mr-2"></mat-icon>
                ยกเลิก
            </button>
            <button mat-flat-button class="mat-primary rounded-lg py-2 px-4" (click)="New()">
                <mat-icon svgIcon="heroicons_solid:check" class="mr-2"></mat-icon>
                ยืนยัน
            </button>
        </div>
    </form>
</div>
