<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">
    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-4 md:px-4 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-xl md:text-4xl font-extrabold tracking-tight mb-2">รายงานลงเวลา</div>
        <div class="flex mt-0 sm:mt-6 md:mt-6 justify-end px-4 gap-2">
            <button mat-flat-button color="primary" class="rounded-lg py-2 px-4" (click)="openCheckinDialog()">
                <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                <span class="ml-2 mr-1">เพิ่มข้อมูล</span>
            </button>
            <button mat-flat-button
                class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                (click)="GetReport()">
                <mat-icon [svgIcon]="'heroicons_outline:magnifying-glass'"></mat-icon>
                <span class="ml-2 mr-1">ค้นหา</span>
            </button>
            <button mat-flat-button
                class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                type="reset">
                <mat-icon [svgIcon]="'heroicons_outline:arrow-path'"></mat-icon>
                <span class="ml-2 mr-1">ล้าง</span>
            </button>
        </div>
        <!-- Actions -->
    </div>
    <div class="flex-auto">
        <form class="flex flex-col pt-4 pb-4 bg-card shadow overflow-hidden ng-valid" [formGroup]="form">
            <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent ">
                <div class="flex flex-col p-3 sm:p-2 bg-card">
                    <div class="flex flex-col md:flex-row gap-4 w-full p-4">
                        <div class="flex flex-col w-full">
                            <ng-container *ngIf="itemtypeData.length > 0">
                                <mat-label class="font-semibold mb-[3px]">เลือกพนักงาน*</mat-label>
                                <app-personnel-autocomplete [itemtypeData]="itemtypeData"
                                    (personnelSelected)="onPersonnelSelected($event)">
                                </app-personnel-autocomplete>
                            </ng-container>
                        </div>
                        <div class="flex flex-col w-full">
                            <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
                                <mat-label>เลือกวันที่</mat-label>
                                <input readonly [formControlName]="'date'" matInput placeholder="วันที่เริ่มต้น"
                                    [matDatepicker]="picker_start_date">
                                <mat-datepicker-toggle matSuffix [for]="picker_start_date"></mat-datepicker-toggle>
                                <mat-datepicker #picker_start_date></mat-datepicker>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
            </div>
        </form>
        <div class="flex flex-col flex-auto p-5 sm:overflow-auto overflow-x-scroll">
            <div class="overflow-x-auto">
                <table datatable [dtOptions]="dtOptions"
                    class="min-w-[800px] w-full text-md text-left text-gray-700 border border-gray-200 bg-white shadow-md">
                    <thead class="bg-gray-300 text-black">
                        <tr>
                            <th class="px-4 py-2 text-center border w-1/12">ลำดับ</th>
                            <th class="px-4 py-2 text-center border w-1/12">รหัสพนักงาน</th>
                            <th class="px-4 py-2 text-center border w-5/12">ชื่อ-สกุล</th>
                            <th class="px-4 py-2 text-center border w-3/12">วันที่ - เวลา</th>
                            <th class="px-4 py-2 text-center border w-2/12">รูปภาพ</th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let item of dataRow; let i = index"
                            class="hover:bg-gray-50 transition-colors border-b">
                            <!-- ลำดับ -->
                            <td class="px-3 py-2 text-center border align-middle w-16">
                                {{ pages.begin + (i + 1) }}
                            </td>
                            <td class="px-4 py-2 border align-middle">
                                <div class="min-w-[200px] max-w-[320px] truncate">
                                    {{ item?.user?.user_id || '-' }}
                                </div>
                            </td>

                            <!-- ชื่อ-สกุล -->
                            <td class="px-4 py-2 border align-middle">
                                <div class="min-w-[200px] max-w-[320px] truncate" [matTooltip]="item?.user?.fullname">
                                    {{ item?.user?.fullname || '-' }}
                                </div>
                            </td>

                            <!-- วันที่ - เวลา -->
                            <td class="px-4 py-2 border align-middle">
                                <div class="whitespace-nowrap tabular-nums font-mono">
                                    {{ item?.time | date:'dd/MM/yyyy HH:mm:ss' }}
                                </div>
                            </td>

                            <!-- รูปภาพ -->
                            <td class="px-4 py-2 border align-middle">
                                <div class="flex items-center justify-center">
                                    <ng-container *ngIf="item?.pic; else noPic">
                                        <img [src]="item.pic" alt="รูปภาพ"
                                            class="w-20 h-20 object-cover rounded-lg shadow-sm border" />
                                    </ng-container>
                                    <ng-template #noPic>
                                        <div
                                            class="w-20 h-20 rounded-lg border bg-gray-100 flex items-center justify-center text-gray-400 text-xs">
                                            ไม่มีรูป
                                        </div>
                                    </ng-template>
                                </div>
                            </td>
                        </tr>
                        <tr *ngIf="!dataRow || dataRow?.length === 0">
                            <td class="py-6 px-4 text-center text-slate-500 italic" colspan="9">
                                ไม่พบข้อมูลการเข้างาน
                            </td>
                        </tr>
                    </tbody>


                </table>


            </div>

        </div>
    </div>
</div>