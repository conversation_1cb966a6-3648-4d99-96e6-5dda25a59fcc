import {
    After<PERSON><PERSON>wInit,
    ChangeDetectorRef,
    Component,
    On<PERSON><PERSON>roy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { FormGroup, FormBuilder, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MAT_DATE_FORMATS, MatNativeDateModule } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { Subject, lastValueFrom } from 'rxjs';
import { ItemTypeService } from '../../../item-type/item-type.service';
import { TimeAttendanceService } from '../time-attendance.service';
import { DatePipe, NgIf, NgFor, CommonModule } from '@angular/common';
import { PersonnelAutocompleteComponent } from 'app/shared/personnel-autocomplete/personnel-autocomplete.component';
import { MatProgressBar } from '@angular/material/progress-bar';
import { MatAnchor, MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { PersonnelAutocompleteComponent as PersonnelAutocompleteComponent_1 } from '../../../../../../shared/personnel-autocomplete/personnel-autocomplete.component';
import { MatFormField, MatLabel, MatSuffix } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { MatDatepickerInput, MatDatepickerToggle, MatDatepicker } from '@angular/material/datepicker';
import { DateTime } from 'luxon';
import { CheckinDialogComponent } from '../../../user/dialog-zk/checkin-dialog.component';
import { BuddhistDatePipe } from 'app/modules/admin/pipes/buddhist-date.pipe';

@Component({
    selector: 'app-list-zk',
    templateUrl: './list.component.html',
    styleUrls: ['./list.component.scss'],
    animations: fuseAnimations,
    imports: [
        CommonModule,
        NgIf,
        MatProgressBar,
        MatAnchor,
        MatIcon,
        FormsModule,
        ReactiveFormsModule,
        MatButton,
        PersonnelAutocompleteComponent_1,
        MatFormField,
        MatLabel,
        MatInput,
        MatDatepickerInput,
        MatDatepickerToggle,
        MatSuffix,
        MatDatepicker,
        MatNativeDateModule,
        NgFor,
        DatePipe,
        DataTablesModule,
        
    ]
})
export class ListZkComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild('personnelAutocomplete', { static: false }) personnelAutocomplete!: PersonnelAutocompleteComponent;
    @ViewChild(DataTableDirective)
    dtElement!: DataTableDirective;
    public dtOptions: DataTables.Settings = {};
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    public dataRow: any[] = [];
    private destroy$ = new Subject<any>();
    totalRowSummary: any;
    flashMessage: 'success' | 'error' | null = null;
    isLoading: boolean = false;
    selectedLocation = '';
    selectedItemType = '';
    selectedItem = '';
    itemtypeData: any[] = [];
    itemData: any = [];

    form: FormGroup;

    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
        private _Service: TimeAttendanceService,
        private _ServiceItemtemType: ItemTypeService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _dialog: MatDialog,
    ) {
        this.form = this._formBuilder.group({
            personnel_id: ['',],
            date: [
                DateTime.now().toFormat('yyyy-MM-dd'), // default วันนี้ในรูปแบบ yyyy-MM-dd
                Validators.required
            ],
        });
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    async ngOnInit(): Promise<void> {
        this.loadTable()
        const itemtype = await lastValueFrom(this._Service.getUser(''));
        this.itemtypeData = itemtype.data;
        this.form.get('date')!.valueChanges.subscribe(value => {
            if (value) {
                const formatted = DateTime.fromJSDate(new Date(value)).toFormat('yyyy-MM-dd');
                if (formatted !== value) {
                    this.form.patchValue({ date: formatted }, { emitEvent: false });
                }
            }
        });
        // this.getAddress()
    }
    onPersonnelSelected(selectedPersonnel: any): void {
        this.form.patchValue({
            personnel_id: selectedPersonnel.personnel_id,
        })
    }
    hiddenEdit() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 27);
        return menu.edit == 0;
    }
    hiddenDelete() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 27);
        return menu.delete == 0;
    }
    hiddenSave() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 27);
        return menu.save == 0;
    }
    /**
     * After view init
     */
    ngAfterViewInit(): void { }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        // this._unsubscribeAll.next(null);
        // this._unsubscribeAll.complete();
    }

    onChangeItemType(e): void {
        // this._Service.getByItemType(e).subscribe((resp: any) => {
        //     this.itemData = resp.data;
        //     // this.rawDataFilter = this.dataRow
        // });
    }

    showFlashMessage(type: 'success' | 'error'): void {
        // Show the message
        this.flashMessage = type;
        // Mark for check
        this._changeDetectorRef.markForCheck();

        // Hide it after 3 seconds
        setTimeout(() => {
            this.flashMessage = null;

            // Mark for check
            this._changeDetectorRef.markForCheck();
        }, 3000);
    }

    pages = { current_page: 1, last_page: 1, per_page: 10, begin: 0 };
    loadTable(): void {
        const that = this;
        this.dtOptions = {
            pagingType: 'full_numbers',
            pageLength: 50,
            serverSide: true,
            processing: true,
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.11.3/i18n/th.json',
            },
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.user_id = this.form.value.personnel_id
                dataTablesParameters.date = this.form.value.date
                that._Service
                    .getZkPage(dataTablesParameters)
                    .subscribe((resp) => {
                        this.dataRow = resp.data;
                        this.pages.current_page = resp.current_page;
                        this.pages.last_page = resp.last_page;
                        this.pages.per_page = resp.per_page;
                        if (resp.current_page > 1) {
                            this.pages.begin =
                                resp.per_page * resp.current_page - 1;
                        } else {
                            this.pages.begin = 0;
                        }
                        callback({
                            recordsTotal: resp.total,
                            recordsFiltered: resp.total,
                            data: [],
                        });
                        this._changeDetectorRef.markForCheck();
                    });
            },
            columns: [
                { data: 'id' },
                { data: 'personnel_id' },
                { data: 'time' },
                { data: 'pic' },

            ],
        };
    }


    rerender(): void {
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            dtInstance.ajax.reload();
        });
    }



    onFilter() {
        this.rerender();
    }




    showError(message: string): void {
        this._fuseConfirmationService.open({
            "title": "เกิดข้อผิดพลาด",
            "message": message,
            "icon": {
                "show": true,
                "name": "heroicons_outline:exclamation-triangle",
                "color": "warning"
            },
            "actions": {
                "confirm": {
                    "show": false,
                    "label": "ยืนยัน",
                    "color": "primary"
                },
                "cancel": {
                    "show": false,
                    "label": "ยกเลิก",

                }
            },
            "dismissible": true
        });
    }

    getAddress(event: any) {
        if (event.latitude && event.longitude) {
            const lat = event.latitude;
            const lng = event.longitude;

            fetch(`https://nominatim.openstreetmap.org/reverse?format=json&lat=${lat}&lon=${lng}`)
                .then(res => res.json())
                .then(data => {
                    return data.display_name
                });
        } else {
            return '-'
        }


    }

    GetReport() {
        this.rerender()
    }
    openCheckinDialog() {
        this._router.navigate(['/report/time-attendance/zk-add']);
    }
}
