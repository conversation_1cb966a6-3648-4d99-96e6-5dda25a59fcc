import { Route, Routes } from '@angular/router';

export default  [
    // {
    //     path: '',
    //     pathMatch: 'full',
    //     redirectTo: 'brief-plan'
    // },
    {
        path: '',
        loadComponent: () => import('./channel.component').then(m => m.ChannelComponent),
        children: [
            {
                path: 'list',
                loadComponent: () => import('./list-channel/list-channel.component').then(m => m.ListChannelComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },
            {
                path: 'new-channel',
                loadComponent: () => import('./new-channel/new-channel.component').then(m => m.NewChannelComponent),
                // resolve: {
                //     permission: PermissionProductsResolver,
                //     department: DepartmentResolver,
                //     resolveGet: PositionResolve,
                //     branch: BranchResolver,
                // }
            },
            {
                path: 'edit/:id',
                loadComponent: () => import('./edit-channel/edit-channel.component').then(m => m.EditChannelComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },



        ]
        /*children : [
            {
                path     : '',
                component: ContactsListComponent,
                resolve  : {
                    tasks    : ContactsResolver,
                    countries: ContactsCountriesResolver
                },
                children : [
                    {
                        path         : ':id',
                        component    : ContactsDetailsComponent,
                        resolve      : {
                            task     : ContactsContactResolver,
                            countries: ContactsCountriesResolver
                        },
                        canDeactivate: [CanDeactivateContactsDetails]
                    }
                ]
            }
        ]*/
    }
] as Routes
