<div class="flex flex-col flex-auto min-w-0 bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between p-6 sm:py-8 sm:px-10 border-b bg-card dark:bg-transparent">
        <div class="flex-1 min-w-0">
            <!-- Title -->
            <div class="mt-2">
                <h2 class="text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                    แก้ไขช่องทางการติดต่อ
                </h2>
            </div>

        </div>
    </div>

    <!-- Main -->
    <div class="flex-auto p-3 sm:p-10">
        <form class="flex flex-col mt-3 p-8 pb-4 bg-card shadow overflow-hidden ng-valid" [formGroup]="formData">
            <div class="flex flex-col sm:flex-row p-8">


                <div class="flex flex-auto flex-wrap">

                    <div class="flex flex-col w-full lg:w-4/4 sm:pl-8">
                        <!-- name -->

                        <div class="flex p-5">
                            <mat-form-field class="w-3/12 pr-2">
                                <mat-label>ชื่อช่องทางการติดต่อ</mat-label>
                                <input matInput [formControlName]="'name'">
                            </mat-form-field>
                        </div>
                        <div class="flex p-5">
                            <div class="flex justify-center items-center w-3/12">

                                <label>
                                    <mat-label> รูปโปรไฟล์</mat-label>
                                    <input (change)='onChange($event)' type="file" id="file" style="cursor: pointer;">
                                    <img class="img" [src]="url" style="width: 400px; height: 150px;">
                                    <!-- <input type="file" id="file"> -->
                                </label>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- button -->
            <div class="flex items-center justify-end w-full border-t px-8 py-4">
                <div class="flex items-center justify-end">
                    <a class="ml-4" mat-flat-button href="channel/list">
                        <mat-icon svgIcon="heroicons_solid:x-mark"></mat-icon>
                        ยกเลิก
                    </a>
                    <button class="px-6 ml-3 mat-primary" mat-flat-button (click)="updateChannel()">
                        <mat-icon svgIcon="heroicons_solid:check"></mat-icon>
                        ยืนยัน
                    </button>
                </div>
            </div>
        </form>
        <pre>
        </pre>
    </div>

</div>