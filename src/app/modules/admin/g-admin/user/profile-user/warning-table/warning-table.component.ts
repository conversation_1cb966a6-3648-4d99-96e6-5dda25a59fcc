import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import {
  debounceTime,
  map,
  merge,
  Observable,
  Subject,
  switchMap,
  takeUntil,
} from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, MatLabel } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { MatRadioGroup, MatRadioButton } from '@angular/material/radio';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { UserService } from '../../user.service';
import { MatAutocomplete } from '@angular/material/autocomplete';
import { NgClass, NgForOf, NgIf } from '@angular/common';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { CommonModule } from '@angular/common';
import { AcknowledgeWarningComponent } from '../acknowledge-warning/acknowledge-warning.component';


@Component({
  selector: 'app-warning-table',
  animations: fuseAnimations,
  imports: [FormsModule, ReactiveFormsModule, MatFormField, MatLabel, MatInput, MatRadioGroup, MatRadioButton, MatButton, MatIcon, MatAutocomplete, NgClass, RouterLink, DataTablesModule, NgIf, NgForOf, CommonModule],
  templateUrl: './warning-table.component.html',
  styleUrl: './warning-table.component.scss'
})
export class WarningTableComponent implements OnInit {
  formData: FormGroup;
  customerFilterCtrl: FormControl = new FormControl();
  @ViewChild(DataTableDirective)
  dtElement!: DataTableDirective;

  now_status: string = "";
  dtOptions: DataTables.Settings = {};
  dataRow: any[] = [];

  constructor(
    public dialogRef: MatDialogRef<WarningTableComponent>,
    private _changeDetectorRef: ChangeDetectorRef,
    private _fuseConfirmationService: FuseConfirmationService,
    private _formBuilder: FormBuilder,
    private _matDialog: MatDialog,
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
    private _authService: AuthService,
    private _Service: UserService,
  ) {
    this.formData = this._formBuilder.group({
      branch_id: JSON.parse(localStorage.getItem('user') || '{}')?.branch_id,

    });

    this.now_status = 'approved'
  }

  ngOnInit(): void {
    this.loadTable()
  }

  pages = { current_page: 1, last_page: 1, per_page: 10, begin: 0 };
  loadTable(): void {
    const that = this;
    this.dtOptions = {
      pagingType: 'full_numbers',
      pageLength: 10,
      serverSide: true,
      processing: true,
      order: [[0, 'desc']],
      language: {
        url: 'https://cdn.datatables.net/plug-ins/1.11.3/i18n/th.json',
      },
      ajax: (dataTablesParameters: any, callback) => {
        dataTablesParameters.status = this.now_status;
        dataTablesParameters.user_id = JSON.parse(localStorage.getItem('user') || '{}')?.id;
        dataTablesParameters.branch_id = JSON.parse(localStorage.getItem('user') || '{}')?.branch_id;
        that._Service
          .getWarningTable(dataTablesParameters)
          .subscribe((resp: any) => {
            this.dataRow = resp.data;
            this.pages.current_page = resp.current_page;
            this.pages.last_page = resp.last_page;
            this.pages.per_page = resp.per_page;
            if (resp.current_page > 1) {
              this.pages.begin =
                resp.per_page * resp.current_page - 1;
            } else {
              this.pages.begin = 0;
            }
            callback({
              recordsTotal: resp.total,
              recordsFiltered: resp.total,
              data: [],
            });
            this._changeDetectorRef.markForCheck();
          });
      },
      columns: [
        { data: 'active' },
        // { data: 'step' },
        // { data: 'amount' },
        // { data: 'updated_at' }
      ],
    };
  }

  rerender(): void {
    this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
      dtInstance.ajax.reload();
    });
  }


  onClose(): void {
    this.dialogRef.close();
  }

  changetable(data: string) {
    this.now_status = data
    this.loadTable()
    this.rerender()
  }

  View(id: any, status: string) {
    const dialogRef = this._matDialog.open(AcknowledgeWarningComponent, {
      // width: '50%',
      // minHeight: 'calc(100vh - 90px)',
      // height: 'auto'
      width: '1000px',
      height: '95%',
      data: {
        id: id,
        status: status
      },
    });

    dialogRef.afterClosed().subscribe((item) => {
      this.rerender();
      this._changeDetectorRef.markForCheck();
    });
  }
}
