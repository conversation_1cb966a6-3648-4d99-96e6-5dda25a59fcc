<div class="overflow-auto bg-white shadow sm:rounded-lg">
    <div class="flex">
        <div class="w-full mt-4">
            <!-- Card Container -->
            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5 gap-4 px-4">
                <div *ngFor="let item of dataRow?.data; let i = index;"
                    class="bg-white shadow-md rounded-lg p-4 hover:shadow-lg transition-shadow duration-200 border-2 border-gray-600">
                    <div class="flex flex-row justify-between">
                        <p class="md:text-lg text-md font-semibold text-gray-800 mb-2">
                            {{
                            item.No }}. {{ item.user?.first_name}} {{ item.user?.last_name}}
                        </p>
                        <button mat-icon-button [matMenuTriggerFor]="menu" aria-label="Example icon-button with a menu"
                            [disabled]="this.status !== 'open' || this.typePage === 'user'">
                            <mat-icon>more_vert</mat-icon>
                        </button>
                        <mat-menu #menu="matMenu">
                            <button mat-menu-item class="bg-green-200" (click)="fetchData(item, 'approved')">
                                <mat-icon>check</mat-icon>
                                <span>รับทราบ</span>
                            </button>
                        </mat-menu>
                    </div>
                    <p class="text-sm text-gray-600">วันที่แจ้ง: {{ item.date | date : 'dd/MM/yyyy' }}</p>
                    <p class="text-sm text-gray-600">หัวข้อเรื่อง: {{ item.title }} </p>
                    <p class="text-sm text-gray-600">ควมมผิด: {{ item.description ?? '-' }}</p>
                    <p class="text-sm text-gray-600">บทลงโทษ: {{ item.punishment ?? '-' }}</p>
                    <p class="text-sm text-gray-600">สถานะ:
                        <span
                            class=" text-blue-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                            *ngIf="item.status === 'open'">
                            รอหัวหน้าตรวจสอบ
                        </span>
                        <!-- <span
                            class=" text-green-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                            *ngIf="item.status === 'process'">
                            หัวหน้าอนุมัติ
                        </span> -->
                        <span
                            class=" text-green-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                            *ngIf="item.status === 'approved'">
                            รอรับทราบ
                        </span>
                        <span
                            class=" text-green-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                            *ngIf="item.status === 'finish'">
                            รับทราบ
                        </span>
                        <!-- <span
                            class=" text-red-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                            *ngIf="item.status === 'cancel'">
                            ไม่อนุมัติ
                        </span> -->
                        <!-- <span
                            class=" text-red-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                            *ngIf="item.status === 'head_cancel'">
                            หัวหน้าไม่อนุมัติ
                        </span> -->
                    </p>
                </div>
            </div>
            <!-- Pagination -->
            <!-- Pagination -->
            <div class="mt-6 flex flex-col md:flex-row md:item-end items-center gap-3 p-4 md:justify-between">
                <!-- Display Page Info -->
                <div class="">
                    <div class="md:text-left text-center text-sm text-gray-600">
                        หน้าที่ {{ pages.current_page }} จากทั้งหมด {{
                        pages.last_page
                        }} หน้า
                    </div>

                    <!-- Display Total Records -->
                    <div class=" text-left md:text-center text-sm text-gray-600">
                        แสดงข้อมูล {{ (pages.current_page - 1) * pages.per_page + 1
                        }} -
                        {{ getMinValue(pages.current_page * pages.per_page,
                        pages.total) }}
                        จากทั้งหมด {{ pages.total }} รายการ
                    </div>
                </div>

                <!-- Navigation Buttons -->
                <div class="flex gap-2">
                    <button (click)="changePage(pages.current_page - 1)"
                        class="bg-blue-500 text-white px-4 py-2 rounded-md shadow hover:bg-blue-600 transition-colors duration-200"
                        [disabled]="pages.current_page === 1">
                        ก่อนหน้า
                    </button>
                    <button (click)="changePage(pages.current_page + 1)"
                        class="bg-blue-500 text-white px-4 py-2 rounded-md shadow hover:bg-blue-600 transition-colors duration-200"
                        [disabled]="pages.current_page === pages.last_page">
                        ถัดไป
                    </button>
                </div>
            </div>
        </div>
    </div>
</div>