import {
    AfterViewInit,
    Component,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    Output,
    SimpleChanges,
} from '@angular/core';
import {
    FormBuilder,
    FormGroup,
} from '@angular/forms';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { UserService } from '../user.service';
import { NgFor, NgIf, DatePipe } from '@angular/common';
import { MatIconButton } from '@angular/material/button';
import { MatMenuTrigger, MatMenu, MatMenuItem } from '@angular/material/menu';
import { MatIcon } from '@angular/material/icon';

@Component({
    selector: 'app-warning-list-user',
    templateUrl: './warning-list-user.component.html',
    styleUrls: ['./warning-list-user.component.scss'],
    animations: fuseAnimations,
    standalone: true,
    imports: [NgFor, MatIconButton, MatMenuTrigger, MatIcon, MatMenu, MatMenuItem, NgIf, DatePipe]
})
export class WarningListUserComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {
    @Input() status: string = '';
    @Input() user_id: string = '';
    @Input() head_id: string = '';
    @Input() typePage: string = '';
    @Input() line_id: string = '';
    @Input() line_head_id: string = '';
    @Output() refresh = new EventEmitter<object>();
    dataRow: any = [];
    formData: FormGroup;
    flashMessage: 'success' | 'error' | null = null;
    isLoading: boolean = false;
    env_path = environment.API_URL;
    /**
     * Constructor
     */
    constructor(
        private _Service: UserService,
    ) 
    {

    }
    /**
     * On init
     */
    ngOnInit(): void {
        this.loadTable()
    }

    ngOnChanges(changes: SimpleChanges): void {
        // ตรวจสอบการเปลี่ยนแปลงของ Input Properties
        if (changes['status']) {
        }
        if (changes['user_id']) {
        }
        if (changes['head_id']) {
        }
    }

    fetchData(item: any, status: any): void {
        let formValue = {
            id: item.id,
            status: status
        }
        // Logic เรียก API ที่นี่

        this.refresh.emit(formValue); // แจ้งกลับไปยัง Parent Component หากต้องการ
    }

    pages = { current_page: 1, last_page: 1, per_page: 10, begin: 0, total: 0 };
    gotoPage: number = 1; // ตัวแปรสำหรับการไปหน้าที่กำหนด
    loadTable(): void {
        const user = JSON.parse(localStorage.getItem('user'));
        const dataTablesParameters = {
            draw: 1, // ระบุ draw เริ่มต้น
            user_id: this.user_id, // ใช้ user id จาก localStorage
            head_id: this.head_id,
            line_id: this.line_id,
            line_head_id: this.line_head_id,
            start: 0, // จุดเริ่มต้นของข้อมูล
            length: 10, // จำนวนข้อมูลต่อหน้า
            columns: [
                { data: 'No', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'user.first_name', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'description', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'date', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'title', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'punishment', name: '', searchable: true, orderable: true, search: { value: '', regex: false } }
            ],
            order: [
                { column: 0, dir: 'desc' } // จัดเรียงตามคอลัมน์แรก
            ],
            search: { value: '', regex: false }, // ค่า search
            status: this.status // สถานะ
        };

        // ส่ง API และเก็บผลลัพธ์
        this._Service.getWarnUserPage(dataTablesParameters,this.typePage).subscribe(
            (resp) => {
                this.dataRow = resp.data; // เก็บข้อมูลที่ได้
                this.pages.current_page = this.dataRow.current_page;
                this.pages.last_page = this.dataRow.last_page;
                this.pages.per_page = this.dataRow.per_page;
                this.pages.total = this.dataRow.total;
                if (this.dataRow.current_page > 1) {
                    this.pages.begin =
                        this.dataRow.per_page * this.dataRow.current_page - 1;
                } else {
                    this.pages.begin = 0;
                }
            },
            (error) => {
                console.error('API Error:', error);
            }
        );
    }
    getMinValue(a: number, b: number): number {
        return Math.min(a, b);
    }

    changePage(page: number): void {
        const user = JSON.parse(localStorage.getItem('user'));
        const dataTablesParameters = {
            draw: page, // ระบุ draw เริ่มต้น
            start: (page - 1) * this.pages.per_page,
            length: 10, // จำนวนข้อมูลต่อหน้า
            columns: [
                { data: 'No', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'user.first_name', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'description', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'date', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'title', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'punishment', name: '', searchable: true, orderable: true, search: { value: '', regex: false } }
            ],
            order: [
                { column: 0, dir: 'desc' } // จัดเรียงตามคอลัมน์แรก
            ],
            search: { value: '', regex: false }, // ค่า search
            status: this.status, // สถานะ
            user_id: this.user_id, // ใช้ user id จาก localStorage
            head_id: this.head_id,
        };
        // ส่ง API และเก็บผลลัพธ์
        this._Service.getWarnUserPage(dataTablesParameters, this.typePage).subscribe(
            (resp) => {
                this.dataRow = resp.data; // เก็บข้อมูลที่ได้
                this.pages.current_page = this.dataRow.current_page;
                this.pages.last_page = this.dataRow.last_page;
                this.pages.per_page = this.dataRow.per_page;
                this.pages.total = this.dataRow.total;
                if (this.dataRow.current_page > 1) {
                    this.pages.begin =
                        this.dataRow.per_page * this.dataRow.current_page - 1;
                } else {
                    this.pages.begin = 0;
                }
            },
            (error) => {
                console.error('API Error:', error);
            }
        );
    }


    /**
     * After view init
     */
    ngAfterViewInit(): void { }

    /**
     * On destroy
     */
    ngOnDestroy(): void {

    }

    /**
     * Show flash message
     */
    showFlashMessage(type: 'success' | 'error'): void {
        // Show the message
        this.flashMessage = type;

        // Mark for check
        // this._changeDetectorRef.markForCheck();

        // Hide it after 3 seconds
        setTimeout(() => {
            this.flashMessage = null;
            // Mark for check
            // this._changeDetectorRef.markForCheck();
        }, 3000);
    }
}
