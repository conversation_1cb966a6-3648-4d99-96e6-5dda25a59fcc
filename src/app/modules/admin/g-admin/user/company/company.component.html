<mat-tab-group mat-stretch-tabs="false" mat-align-tabs="start" class="w-full">
  <mat-tab label="ข้อมูลบริษัท">
    <form [formGroup]="formData"
      class="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-3 p-8 bg-white rounded-lg shadow-md w-full"
      enctype="multipart/form-data">

      <!-- Header Section -->
      <div class="col-span-full mb-6 border-b pb-4">
        <h2 class="text-2xl font-semibold text-gray-800">แบบฟอร์มข้อมูลบริษัท</h2>
        <p class="text-gray-600 mt-1">กรุณากรอกข้อมูลให้ครบถ้วนและถูกต้อง</p>
      </div>

      <!-- Profile Picture Section -->

      <div class="flex flex-col items-center mb-4 col-span-1 justify-center">
        <div class="relative group">
          <img *ngIf="previewImage" [src]="previewImage"
            class="w-60 h-60 rounded-full object-cover border-4 border-white shadow-lg" />
          <div *ngIf="!previewImage"
            class="w-60 h-60 rounded-full bg-gray-200 flex items-center justify-center border-4 border-white shadow-lg">
            <mat-icon class="text-gray-400 text-5xl">person</mat-icon>
          </div>
          <div
            class="absolute inset-0 bg-black bg-opacity-30 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <mat-icon class="text-white">edit</mat-icon>
          </div>
        </div>
        <input type="file" hidden (change)="onImageChange($event)" accept="image/*" #imageInput />
        <button mat-stroked-button color="primary" class="mt-4" (click)="imageInput.click()">
          <mat-icon>cloud_upload</mat-icon>
          <span>อัปโหลดรูปโลโก้</span>
        </button>
        <p class="text-xs text-gray-500 mt-2">รองรับไฟล์ JPG, PNG ขนาดไม่เกิน 5MB</p>
      </div>
      <div class="flex flex-col items-center mb-8">
        <!-- System Access Section -->
        <!-- code -->
        <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
          <mat-label>รหัสบริษัท</mat-label>
          <input matInput type="text" formControlName="code" />
        </mat-form-field>
        <!-- name -->
        <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
          <mat-label>ชื่อบริษัท</mat-label>
          <input matInput type="text" formControlName="name" />
          <mat-icon matSuffix>email</mat-icon>
        </mat-form-field>
        <!-- Email -->
        <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
          <mat-label>Email</mat-label>
          <input matInput type="email" formControlName="email" />
          <mat-icon matSuffix>email</mat-icon>
        </mat-form-field>

        <!-- Address -->
        <mat-form-field class="w-full" [ngClass]="formFieldHelpers" appearance="fill">
          <mat-label>ที่อยู่</mat-label>
          <textarea matInput formControlName="address" rows="3"></textarea>
          <mat-icon matSuffix>home</mat-icon>
        </mat-form-field>

        <div class="items-start">
          <label class="block font-medium text-gray-700 mb-2 flex items-center">
            คิด OT อัตโนมัติ
          </label>
          <mat-radio-group class="flex flex-row gap-6" formControlName="auto_ot">
            <mat-radio-button class="radio-option" [value]="1">
              <div class="flex items-center">
                ใช่
              </div>
            </mat-radio-button>
            <mat-radio-button class="radio-option" [value]="0">
              <div class="flex items-center">
                ไม่ใช่
              </div>
            </mat-radio-button>
          </mat-radio-group>
        </div>
      </div>
      <!-- Submit Section -->
      <div class="col-span-full flex justify-end mt-8 pt-6 border-t">
        <button mat-raised-button color="warn" class="mr-4" type="button">
          <mat-icon>cancel</mat-icon>
          ยกเลิก
        </button>
        <button mat-raised-button class="bg-green-600 hover:bg-green-700 text-white" (click)="onSubmit()">
          <mat-icon>save</mat-icon>
          บันทึกข้อมูล
        </button>
      </div>
    </form>
  </mat-tab>

</mat-tab-group>