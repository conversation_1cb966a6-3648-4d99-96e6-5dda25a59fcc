import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MasterDataService } from 'app/shared/master-data.service';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import moment from 'moment';
import { ActivatedRoute, Router } from '@angular/router';
import { UserService } from '../user.service';
import { environment } from 'environments/environment';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDialog } from '@angular/material/dialog';
import { DialogDeletemoneyComponent } from '../../deletemoney/dialog-add/dialog-add.component';
import { DialogPlusemoneyComponent } from '../../plusmoney/dialog-add/dialog-add.component';

export interface Permission { id: number; name: string; }
export interface Department { id: number; name: string; }
export interface Position { id: number; name: string; }
export interface Employee { id: number; first_name: string; last_name: string }
export interface WorkShift { id: number; name: string; }
export interface Branch { id: number; name: string; }




interface MasterData {
  permissions: Permission[];
  departments: Department[];
  positions: Position[];
  employees: Employee[];
  workShifts: WorkShift[];
  branchs: Branch[];
}

// export const MY_DATE_FORMATS = {
//   parse: {
//     dateInput: 'DD/MM/YYYY',
//   },
//   display: {
//     dateInput: 'DD/MM/YYYY',
//     monthYearLabel: 'MMMM YYYY',
//     dateA11yLabel: 'LL',
//     monthYearA11yLabel: 'MMMM YYYY',
//   },
// };


@Component({
  selector: 'app-company-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatRadioModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,

  ],

  templateUrl: './company.component.html',
})
export class CompanyFormComponent implements OnInit {
  formData!: FormGroup;


  banks = [
    { value: 'bbl', name: 'ธนาคารกรุงเทพ (BBL)' },
    { value: 'kbank', name: 'ธนาคารกสิกรไทย (KBank)' },
    { value: 'scb', name: 'ธนาคารไทยพาณิชย์ (SCB)' },
    { value: 'ktb', name: 'ธนาคารกรุงไทย (KTB)' },
    { value: 'bay', name: 'ธนาคารกรุงศรีอยุธยา (Krungsri/BAY)' },
    { value: 'ttb', name: 'ธนาคารทหารไทยธนชาต (TTB)' },
    { value: 'uob', name: 'ธนาคารยูโอบี (UOB)' },
    { value: 'cimb', name: 'ธนาคารซีไอเอ็มบีไทย (CIMB)' },
    { value: 'tisco', name: 'ธนาคารทิสโก้ (TISCO)' },
    { value: 'kkp', name: 'ธนาคารเกียรตินาคินภัทร (KKP)' },
    { value: 'icbc', name: 'ธนาคารไอซีบีซี (ICBC)' },
    { value: 'hsbc', name: 'ธนาคารเอชเอสบีซี (HSBC)' },
    { value: 'standard', name: 'ธนาคารสแตนดาร์ดชาร์เตอร์ด (Standard Chartered)' },
    { value: 'other', name: 'อื่น ๆ' }
  ];

  // Mock dropdown data
  incomeData = [];
  deductionData = [];
  positionList = [];
  departmentList = [];
  branchList = [];
  permissionList = [];
  nationalList = [
    { value: 'thai', name: 'ไทย' },
    { value: 'lao', name: 'ลาว' },
    { value: 'mymar', name: 'เมียนม่า' },
    { value: 'other', name: 'อื่น ๆ' },
  ];
  workTypeList = [
    { value: 'day', name: 'รายวัน' },
    { value: 'month', name: 'รายเดือน' },
  ];

  employeeList: any = []
  workShiftList: any = []
  brandhList: any = []
  url_doc: any = []

  previewImage: string | ArrayBuffer | null = null;
  previewSignature: string | ArrayBuffer | null = null;
  formFieldHelpers: string[] = ['fuse-mat-dense'];
  masterData!: MasterData;
  userId: any;
  constructor(
    private _formBuilder: FormBuilder,
    private masterDataService: MasterDataService,
    private _fuseConfirmationService: FuseConfirmationService,
    private _router: Router,
    private _service: UserService,
    private _activatedRoute: ActivatedRoute,
    private _changeDetectorRef: ChangeDetectorRef,
    private _matDialog: MatDialog
  ) {
    this._activatedRoute.params.subscribe((params) => {
      const id = params.id;
      this.userId = id
      // console.log(this.userId);
      this._service.getdeductUser(id).subscribe((resp: any) => {
        this.deductionData = resp.data
      })
      this._service.getIncomeUser(id).subscribe((resp: any) => {
        this.incomeData = resp.data
      })
    })
  }

  ngOnInit(): void {
    this.masterDataService.getMasterData().subscribe(data => {
      this.masterData = data;
      this.positionList = this.masterData.positions;
      this.departmentList = this.masterData.departments;
      this.employeeList = this.masterData.employees;
      this.permissionList = this.masterData.permissions;
      this.workShiftList = this.masterData.workShifts;
      this.branchList = this.masterData.branchs;


    });
    this.formData = this._formBuilder.group({
      id: [''],
      code: [''],
      name: [''],
      email: [''],
      tel: [''],
      image: [''],
      address: [''],
      auto_ot: [0],
      status: [1],
    });

    if (this.userId) {
      this._service.getCompanyById(this.userId).subscribe((resp: any) => {
        this.formData.patchValue({
          ...resp.data,
          image: '',
        })

        this.previewImage = resp.data.image
      })
    }
  }

  onImageChange(event: Event): void {
    const file = (event.target as HTMLInputElement)?.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => (this.previewImage = reader.result);
      reader.readAsDataURL(file);
      this.formData.patchValue({ image: file });
    }
  }






  onSubmit(): void {
    // console.log(this.formData.value);
    const confirmation = this._fuseConfirmationService.open({
      title: 'บันทึกข้อมูล',
      message: 'คุณต้องการบันทึกข้อมูลใช่หรือไม่ ?',
      icon: {
        show: true,
        name: 'heroicons_outline:question-mark-circle',
        color: 'info',
      },
      actions: {
        confirm: {
          show: true,
          label: 'ยืนยัน',
          color: 'primary',
        },
        cancel: {
          show: true,
          label: 'ยกเลิก',
        },
      },
      dismissible: true,
    });

    // Subscribe to the confirmation dialog closed action
    confirmation.afterClosed().subscribe((result) => {
      // If the confirm button pressed...
      if (result === 'confirmed') {

        let formValue = this.formData.value;
        const formDataToSend = new FormData();
        for (const key in formValue) {
          const value = formValue[key] === null ? '' : formValue[key];
          formDataToSend.append(key, value);
        }

        if (this.userId) {

          this._service.updateCompany(formDataToSend).subscribe({
            next: (resp: any) => {
              this._router.navigateByUrl('company/list');
            },
            error: (err: any) => {
              this._fuseConfirmationService.open({
                title: 'เกิดข้อผิดพลาด',
                message: err.error?.message || 'ไม่สามารถอัปเดตข้อมูลได้',
                icon: {
                  show: true,
                  name: 'heroicons_outline:exclamation-triangle',
                  color: 'warning',
                },
                actions: {
                  confirm: {
                    show: false,
                    label: 'ยืนยัน',
                    color: 'primary',
                  },
                  cancel: {
                    show: false,
                    label: 'ยกเลิก',
                  },
                },
                dismissible: true,
              });
            },
          });
        } else {
          this._service.createCompany(formDataToSend).subscribe({
            next: (resp: any) => {
              this._router.navigateByUrl('company/list').then(() => { });
            },
            error: (err: any) => {
              if (typeof err.error.message === 'object' && err.error.message !== null) {
                const errorMessages = Object.keys(err.error.message)
                  .map((key) => `${key}: ${err.error.message.errors[key]}`)
                  .join('\n');
                this._fuseConfirmationService.open({
                  title: 'เกิดข้อผิดพลาด',
                  message: errorMessages,
                  icon: {
                    show: true,
                    name: 'heroicons_outline:exclamation-triangle',
                    color: 'warning',
                  },
                  actions: {
                    confirm: {
                      show: false,
                      label: 'ยืนยัน',
                      color: 'primary',
                    },
                    cancel: {
                      show: false,
                      label: 'ยกเลิก',
                    },
                  },
                  dismissible: true,
                });
              } else {
                this._fuseConfirmationService.open({
                  title: 'เกิดข้อผิดพลาด',
                  message: err.error.message,
                  icon: {
                    show: true,
                    name: 'heroicons_outline:exclamation-triangle',
                    color: 'warning',
                  },
                  actions: {
                    confirm: {
                      show: false,
                      label: 'ยืนยัน',
                      color: 'primary',
                    },
                    cancel: {
                      show: false,
                      label: 'ยกเลิก',
                    },
                  },
                  dismissible: true,
                });
              }

            }
          });
        }
      }
    });
  }
}
