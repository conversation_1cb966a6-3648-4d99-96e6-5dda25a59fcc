.card {
    border: 1px solid #e2e8f0;
    transition: transform 0.2s ease-in-out;
}

.card:hover {
    transform: scale(1.05);
}

.btn {
    padding: 0.5rem 1rem;
    border: none;
    background-color: #3182ce;
    color: #fff;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.2s ease-in-out;
}

.btn:hover {
    background-color: #2b6cb0;
}

.btn:disabled {
    background-color: #cbd5e0;
    cursor: not-allowed;
}


.card-container {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
  }
  
  .card {
    border: 1px solid #ccc;
    border-radius: 8px;
    padding: 16px;
    width: 200px;
    background-color: #fff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  }
  