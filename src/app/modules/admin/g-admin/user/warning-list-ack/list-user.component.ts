import {
    AfterViewInit,
    Component,
    EventEmitter,
    Input,
    OnChanges,
    OnDestroy,
    OnInit,
    Output,
    SimpleChanges,
} from '@angular/core';
import {
    FormBuilder,
    FormGroup,
} from '@angular/forms';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { UserService } from '../user.service';
import { NgFor, NgIf, DatePipe } from '@angular/common';
import { MatIconButton } from '@angular/material/button';
import { MatMenuTrigger, MatMenu, MatMenuItem } from '@angular/material/menu';
import { MatIcon } from '@angular/material/icon';

@Component({
    selector: 'app-warning-list-ack',
    templateUrl: './list-user.component.html',
    styleUrls: ['./list-user.component.scss'],
    animations: fuseAnimations,
    imports: [NgFor, MatIconButton, MatMenuTrigger, MatIcon, MatMenu, MatMenuItem, NgIf, DatePipe]
})
export class WarningListAckComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {

    @Input() status: string = '';
    @Input() user_id: string = '';
    @Input() head_id: string = '';
    @Input() line_id: string = '';
    @Input() line_head_id: string = '';
    @Input() typePage: string = '';

    @Output() refresh = new EventEmitter<object>();
    dataRowOT: any = [];
    dataSort: any = [];
    formData: FormGroup;
    flashMessage: 'success' | 'error' | null = null;
    isLoading: boolean = false;
    env_path = environment.API_URL;

    /**
     * Constructor
     */
    constructor(
        private _Service: UserService,

    ) {

    }

    /**
     * On init
     */
    ngOnInit(): void {
        this.loadTableOT()

    }

    ngOnChanges(changes: SimpleChanges): void {
        console.log(changes);
        // ตรวจสอบการเปลี่ยนแปลงของ Input Properties
        if (changes['status']) {
        }
        if (changes['user_id']) {
        }
        if (changes['head_id']) {
        }
    }

    fetchData(item: any, status: any): void {
        let formValue = {
            id: item.id,
            status: status
        }
        const user = JSON.parse(localStorage.getItem('user'));
        const dataTablesParameters = {
            draw: 1, // ระบุ draw เริ่มต้น
            user_id: this.user_id, // ใช้ user id จาก localStorage
            head_id: this.head_id,
            line_id: this.line_id,
            line_head_id: this.line_head_id,
            start: 0, // จุดเริ่มต้นของข้อมูล
            length: 10, // จำนวนข้อมูลต่อหน้า
             columns: [
                { data: 'No', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'user.first_name', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'description', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'date', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'title', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'punishment', name: '', searchable: true, orderable: true, search: { value: '', regex: false } }
            ],
            order: [
                { column: 0, dir: 'desc' } // จัดเรียงตามคอลัมน์แรก
            ],
            search: { value: '', regex: false }, // ค่า search
            status: this.status // สถานะ
        };

        // ส่ง API และเก็บผลลัพธ์
        this._Service.getOtPageApprove(dataTablesParameters).subscribe(
            (resp) => {
                this.dataRowOT = resp.data; // เก็บข้อมูลที่ได้
                this.pagesOt.current_page = this.dataRowOT.current_page;
                this.pagesOt.last_page = this.dataRowOT.last_page;
                this.pagesOt.per_page = this.dataRowOT.per_page;
                this.pagesOt.total = this.dataRowOT.total;
                if (this.dataRowOT.current_page > 1) {
                    this.pagesOt.begin =
                        this.dataRowOT.per_page * this.dataRowOT.current_page - 1;
                } else {
                    this.pagesOt.begin = 0;
                }
            },
            (error) => {
                console.error('API Error:', error);
            }
        );

        // Logic เรียก API ที่นี่

        this.refresh.emit(formValue); // แจ้งกลับไปยัง Parent Component หากต้องการ
    }



    pagesOt = { current_page: 1, last_page: 1, per_page: 10, begin: 0, total: 0 };
    gotoPage: number = 1; // ตัวแปรสำหรับการไปหน้าที่กำหนด
    loadTableOT(): void {
        const user = JSON.parse(localStorage.getItem('user'));
        const dataTablesParameters = {
            draw: 1, // ระบุ draw เริ่มต้น
            user_id: this.user_id, // ใช้ user id จาก localStorage
            head_id: this.head_id,
            line_id: this.line_id,
            line_head_id: this.line_head_id,
            start: 0, // จุดเริ่มต้นของข้อมูล
            length: 10, // จำนวนข้อมูลต่อหน้า
            columns: [
                { data: 'No', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'user.first_name', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'description', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'date', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'title', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'punishment', name: '', searchable: true, orderable: true, search: { value: '', regex: false } }
            ],
            order: [
                { column: 0, dir: 'desc' } // จัดเรียงตามคอลัมน์แรก
            ],
            search: { value: '', regex: false }, // ค่า search
            status: this.status // สถานะ
        };

        // ส่ง API และเก็บผลลัพธ์
        this._Service.getOtPageApprove(dataTablesParameters).subscribe(
            (resp) => {
                this.dataRowOT = resp.data; // เก็บข้อมูลที่ได้
                this.pagesOt.current_page = this.dataRowOT.current_page;
                this.pagesOt.last_page = this.dataRowOT.last_page;
                this.pagesOt.per_page = this.dataRowOT.per_page;
                this.pagesOt.total = this.dataRowOT.total;
                if (this.dataRowOT.current_page > 1) {
                    this.pagesOt.begin =
                        this.dataRowOT.per_page * this.dataRowOT.current_page - 1;
                } else {
                    this.pagesOt.begin = 0;
                }
            },
            (error) => {
                console.error('API Error:', error);
            }
        );
    }
    getMinValue(a: number, b: number): number {
        return Math.min(a, b);
    }

    changePage(page: number): void {
        const user = JSON.parse(localStorage.getItem('user'));
        const dataTablesParameters = {
            draw: page, // ระบุ draw เริ่มต้น
            user_id: this.user_id, // ใช้ user id จาก localStorage
            head_id: this.head_id,
            start: (page - 1) * this.pagesOt.per_page,
            length: 10, // จำนวนข้อมูลต่อหน้า
            columns: [
                { data: 'no', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'name', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'type', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'date', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'time_start', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
                { data: 'time_out', name: '', searchable: true, orderable: true, search: { value: '', regex: false } }
            ],
            order: [
                { column: 0, dir: 'desc' } // จัดเรียงตามคอลัมน์แรก
            ],
            search: { value: '', regex: false }, // ค่า search
            status: this.status // สถานะ
        };
        // ส่ง API และเก็บผลลัพธ์
        this._Service.getOtPageApprove(dataTablesParameters).subscribe(
            (resp) => {
                this.dataRowOT = resp.data; // เก็บข้อมูลที่ได้
                // this.dataSort =  this.dataRowOT.data.sort((a, b) => new Date(b.date).getTime() - new Date(a.date).getTime());
                this.pagesOt.current_page = this.dataRowOT.current_page;
                this.pagesOt.last_page = this.dataRowOT.last_page;
                this.pagesOt.per_page = this.dataRowOT.per_page;
                this.pagesOt.total = this.dataRowOT.total;
                if (this.dataRowOT.current_page > 1) {
                    this.pagesOt.begin =
                        this.dataRowOT.per_page * this.dataRowOT.current_page - 1;
                } else {
                    this.pagesOt.begin = 0;
                }
            },
            (error) => {
                console.error('API Error:', error);
            }
        );
    }


    /**
     * After view init
     */
    ngAfterViewInit(): void { }

    /**
     * On destroy
     */
    ngOnDestroy(): void {

    }

    /**
     * Show flash message
     */
    showFlashMessage(type: 'success' | 'error'): void {
        // Show the message
        this.flashMessage = type;

        // Mark for check
        // this._changeDetectorRef.markForCheck();

        // Hide it after 3 seconds
        setTimeout(() => {
            this.flashMessage = null;
            // Mark for check
            // this._changeDetectorRef.markForCheck();
        }, 3000);
    }
}
