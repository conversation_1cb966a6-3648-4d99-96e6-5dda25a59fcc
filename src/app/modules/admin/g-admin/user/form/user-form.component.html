<mat-tab-group mat-stretch-tabs="false" mat-align-tabs="start" class="w-full">
  <mat-tab label="ข้อมูลพนักงาน">
    <form [formGroup]="formData"
      class="grid gap-6 grid-cols-full md:grid-cols-2 lg:grid-cols-3 p-8 bg-white rounded-lg shadow-md w-full"
      enctype="multipart/form-data">

      <!-- Header Section -->
      <div class="col-span-full mb-6 border-b pb-4">
        <h2 class="text-2xl font-semibold text-gray-800">แบบฟอร์มข้อมูลพนักงาน</h2>
        <p class="text-gray-600 mt-1">กรุณากรอกข้อมูลให้ครบถ้วนและถูกต้อง</p>
      </div>

      <!-- Profile Picture Section -->

      <div class="flex flex-col items-center mb-4 col-span-full md:col-span-2 justify-center">
        <div class="relative group">
          <img *ngIf="previewImage" [src]="previewImage"
            class="w-60 h-60 rounded-full object-cover border-4 border-white shadow-lg" />
          <div *ngIf="!previewImage"
            class="w-60 h-60 rounded-full bg-gray-200 flex items-center justify-center border-4 border-white shadow-lg">
            <mat-icon class="text-gray-400 text-5xl">person</mat-icon>
          </div>
          <div
            class="absolute inset-0 bg-black bg-opacity-30 rounded-full flex items-center justify-center opacity-0 group-hover:opacity-100 transition-opacity duration-300">
            <mat-icon class="text-white">edit</mat-icon>
          </div>
        </div>
        <input type="file" hidden (change)="onImageChange($event)" accept="image/*" #imageInput />
        <button mat-stroked-button color="primary" class="mt-4" (click)="imageInput.click()">
          <mat-icon>cloud_upload</mat-icon>
          <span>อัปโหลดรูปโปรไฟล์</span>
        </button>
        <p class="text-xs text-gray-500 mt-2">รองรับไฟล์ JPG, PNG ขนาดไม่เกิน 5MB</p>
      </div>
      <div class="flex flex-col items-center mb-8">
        <!-- System Access Section -->
        <div class="col-span-full mt-8">
          <h3 class="text-lg font-medium text-gray-700 mb-4 flex items-center">
            <mat-icon class="mr-2 text-blue-500">lock</mat-icon>
            การเข้าถึงระบบ
          </h3>
        </div>

        <!-- Email -->
        <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
          <mat-label>Email</mat-label>
          <input matInput type="email" formControlName="email" />
          <mat-icon matSuffix>email</mat-icon>
        </mat-form-field>

        <!-- Password -->
        <mat-form-field class="w-full" [ngClass]="formFieldHelpers" *ngIf="!userId">
          <mat-label>Password</mat-label>
          <input matInput [type]="hidePassword ? 'password' : 'text'" formControlName="password" />
          <button mat-icon-button matSuffix type="button" (click)="togglePasswordVisibility()"
            [attr.aria-label]="'Hide or show password'" [attr.aria-pressed]="!hidePassword">
            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
          </button>
        </mat-form-field>

        <!-- Permission -->
        <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
          <mat-label>สิทธิ์การใช้งาน</mat-label>
          <mat-select formControlName="permission_id">
            <mat-option *ngFor="let p of permissionList" [value]="p.id">{{ p.name }}</mat-option>
          </mat-select>
          <mat-icon matSuffix>admin_panel_settings</mat-icon>
        </mat-form-field>
      </div>

      <!-- Personal Information Section -->
      <div class="col-span-full mt-4">
        <h3 class="text-lg font-medium text-gray-700 mb-4 flex items-center">
          <mat-icon class="mr-2 text-blue-500">person</mat-icon>
          ข้อมูลส่วนตัว
        </h3>
      </div>

      <!-- User ID -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>รหัสพนักงาน</mat-label>
        <input matInput formControlName="personnel_id" />
        <mat-icon matSuffix>badge</mat-icon>
      </mat-form-field>

      <!-- Citizen No -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>เลขบัตรประชาชน / Passport</mat-label>
        <input matInput formControlName="citizen_no" />
        <mat-icon matSuffix>credit_card</mat-icon>
      </mat-form-field>

      <!-- Birthday -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>วันเดือนปีเกิด</mat-label>
        <input matInput [matDatepicker]="birthdayPicker" formControlName="birthday" />
        <mat-datepicker-toggle matSuffix [for]="birthdayPicker"></mat-datepicker-toggle>
        <mat-datepicker #birthdayPicker></mat-datepicker>
        <!-- <mat-icon matSuffix>cake</mat-icon> -->
      </mat-form-field>
      <div class="flex flex-row gap-2">
        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
          <mat-label>คำนำหน้า (ไทย)</mat-label>
          <mat-select formControlName="prefix">
            <mat-option *ngFor="let item of thaiPrefixes" [value]="item.label">
              {{ item.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field class="w-full" [ngClass]="formFieldHelpers" *ngIf="formData.get('prefix').value === 'อื่น ๆ'">
          <mat-label>ระบุ</mat-label>
          <input matInput formControlName="prefix_other" />
          <mat-icon matSuffix>edit</mat-icon>
        </mat-form-field>
      </div>
      <!-- First Name -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>ชื่อ (ไทย)</mat-label>
        <input matInput formControlName="first_name" />
        <mat-icon matSuffix>edit</mat-icon>
      </mat-form-field>

      <!-- Last Name -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>นามสกุล (ไทย)</mat-label>
        <input matInput formControlName="last_name" />
        <mat-icon matSuffix>edit</mat-icon>
      </mat-form-field>


      <div class="flex flex-row gap-2">
        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
          <mat-label>คำนำหน้า (อังกฤษ)</mat-label>
          <mat-select formControlName="prefix_en">
            <mat-option *ngFor="let item of englishPrefixes" [value]="item.label">
              {{ item.label }}
            </mat-option>
          </mat-select>
        </mat-form-field>
        <mat-form-field class="w-full" [ngClass]="formFieldHelpers" *ngIf="formData.get('prefix_en').value === 'Other'">
          <mat-label>ระบุ</mat-label>
          <input matInput formControlName="prefix_other_en" />
          <mat-icon matSuffix>edit</mat-icon>
        </mat-form-field>
      </div>

      <!-- First Name -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>ชื่อ (อังกฤษ)</mat-label>
        <input matInput formControlName="first_name_en" />
        <mat-icon matSuffix>edit</mat-icon>
      </mat-form-field>

      <!-- Last Name -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>นามสกุล (อังกฤษ)</mat-label>
        <input matInput formControlName="last_name_en" />
        <mat-icon matSuffix>edit</mat-icon>
      </mat-form-field>

      <!-- Phone No -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>เบอร์โทร</mat-label>
        <input matInput formControlName="phone_no" />
        <mat-icon matSuffix>phone</mat-icon>
      </mat-form-field>

      <!-- National -->
      <div class="flex flex-row gap-2">
        <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
          <mat-label>สัญชาติ</mat-label>
          <mat-select formControlName="nationality">
            <mat-option *ngFor="let item of nationalList" [value]="item.name">
              {{ item.name }}
            </mat-option>
          </mat-select>
          <mat-icon matSuffix>flag</mat-icon>
        </mat-form-field>
        <mat-form-field class="w-full" [ngClass]="formFieldHelpers"
          *ngIf="formData.get('nationality').value === 'อื่น ๆ'">
          <mat-label>ระบุ</mat-label>
          <input matInput formControlName="nationality_other" />
          <mat-icon matSuffix>edit</mat-icon>
        </mat-form-field>
      </div>

      <mat-radio-group class="flex flex-row gap-6 mt-5" formControlName="sex">
        <mat-radio-button class="radio-option" [value]="'M'">
          <div class="flex items-center">
            <mat-icon class="mr-1">male</mat-icon>
            ชาย
          </div>
        </mat-radio-button>
        <mat-radio-button class="radio-option" [value]="'F'">
          <div class="flex items-center">
            <mat-icon class="mr-1">female</mat-icon>
            หญิง
          </div>
        </mat-radio-button>
      </mat-radio-group>
      <!-- ที่อยู่ -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>ที่อยู่</mat-label>
        <input matInput formControlName="home_address" />
        <mat-icon class="text-slate-400" svgIcon="heroicons_solid:map-pin"></mat-icon>
      </mat-form-field>

      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>จังหวัด</mat-label>
        <input matInput formControlName="province" />
      </mat-form-field>

      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>อำเภอ</mat-label>
        <input matInput formControlName="district" />
      </mat-form-field>

      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>ตำบล</mat-label>
        <input matInput formControlName="subdistrict" />
      </mat-form-field>

       <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>รหัสไปรษณีย์</mat-label>
        <input matInput formControlName="zip_code" />
      </mat-form-field>

      <!-- Work Information Section -->
      <div class="col-span-full mt-8">
        <h3 class="text-lg font-medium text-gray-700 mb-4 flex items-center">
          <mat-icon class="mr-2 text-blue-500">work</mat-icon>
          ข้อมูลการทำงาน
        </h3>
      </div>

      <!-- Position -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>ตำแหน่ง</mat-label>
        <mat-select formControlName="position_id">
          <mat-option *ngFor="let pos of positionList" [value]="pos.id">{{ pos.name }}</mat-option>
        </mat-select>
        <mat-icon matSuffix>work_outline</mat-icon>
      </mat-form-field>

      <!-- Department -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>แผนก</mat-label>
        <mat-select formControlName="department_id">
          <mat-option *ngFor="let dept of departmentList" [value]="dept.id">{{ dept.name }}</mat-option>
        </mat-select>
        <mat-icon matSuffix>groups</mat-icon>
      </mat-form-field>

      <!-- Branch -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>สาขา</mat-label>
        <mat-select formControlName="branch_id">
          <mat-option *ngFor="let b of branchList" [value]="b.id">{{ b.name }}</mat-option>
        </mat-select>
        <mat-icon matSuffix>location_on</mat-icon>
      </mat-form-field>

      <!-- Work Type -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>ประเภทพนักงาน</mat-label>
        <mat-select formControlName="work_type">
          <mat-option *ngFor="let item of workTypeList" [value]="item.value">{{ item.name }}</mat-option>
        </mat-select>
        <mat-icon matSuffix>account_balance_wallet</mat-icon>
      </mat-form-field>

      <!-- Salary -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>เงินเดือน</mat-label>
        <span matPrefix class="mr-2 text-gray-500">฿</span>
        <input matInput type="number" formControlName="salary" />
        <mat-icon matSuffix>attach_money</mat-icon>
      </mat-form-field>

      <!-- Head -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>หัวหน้างาน</mat-label>
        <mat-select formControlName="head_id">
          <mat-option *ngFor="let item of headList" [value]="item.id">{{ item.first_name }} {{ item.last_name
            }}</mat-option>
        </mat-select>
        <mat-icon matSuffix>manage_accounts</mat-icon>
      </mat-form-field>

      <!-- "Work shift" -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>กะการทำงาน</mat-label>
        <mat-select formControlName="work_shift_id">
          <mat-option *ngFor="let item of workShiftList" [value]="item.id">{{ item.name }}</mat-option>
        </mat-select>
        <mat-icon matSuffix>calendar</mat-icon>
      </mat-form-field>

      <!-- Register Date -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>วันที่เริ่มงาน</mat-label>
        <input matInput [matDatepicker]="registerDatePicker" formControlName="register_date" />
        <mat-datepicker-toggle matSuffix [for]="registerDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #registerDatePicker></mat-datepicker>
        <!-- <mat-icon matSuffix>event_available</mat-icon> -->
      </mat-form-field>

      <!-- End Date -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>วันที่ลาออก</mat-label>
        <input matInput [matDatepicker]="endDatePicker" formControlName="end_date" />
        <mat-datepicker-toggle matSuffix [for]="endDatePicker"></mat-datepicker-toggle>
        <mat-datepicker #endDatePicker></mat-datepicker>
        <!-- <mat-icon matSuffix>event_busy</mat-icon> -->
      </mat-form-field>


      <!-- Designated Persons -->
      <div class="">
        <label class="block font-medium text-gray-700 mb-2 flex items-center">
          <mat-icon class="text-blue-500">verified_user</mat-icon>
          สถานะการทำงาน
        </label>
        <mat-radio-group class="flex flex-row gap-6" formControlName="work_status">
          <mat-radio-button class="radio-option" [value]="'ปกติ'">
            <div class="flex items-center">
              ปกติ
            </div>
          </mat-radio-button>
          <mat-radio-button class="radio-option" [value]="'พักงาน'">
            <div class="flex items-center">
              พักงาน
            </div>
          </mat-radio-button>
          <mat-radio-button class="radio-option" [value]="'ลาออก'">
            <div class="flex items-center">
              ลาออก
            </div>
          </mat-radio-button>
          <mat-radio-button class="radio-option" [value]="'ไล่ออก'">
            <div class="flex items-center">
              ไล่ออก
            </div>
          </mat-radio-button>
        </mat-radio-group>
      </div>
      <div class="">
        <label class="block font-medium text-gray-700 mb-2 flex items-center">
          <mat-icon class="mr-2 text-blue-500">wallet</mat-icon>
          สถานะจ่ายเงินเดือน
        </label>
        <mat-radio-group class="flex flex-row gap-6" formControlName="payroll_status">
          <mat-radio-button class="radio-option" [value]="1">
            <div class="flex items-center">
              <mat-icon class="mr-1 text-green-500">check_circle</mat-icon>
              จ่าย
            </div>
          </mat-radio-button>
          <mat-radio-button class="radio-option" [value]="0">
            <div class="flex items-center">
              <mat-icon class="mr-1 text-red-500">cancel</mat-icon>
              ไม่จ่าย
            </div>
          </mat-radio-button>
        </mat-radio-group>
      </div>
      <div class="">
        <label class="block font-medium text-gray-700 mb-2 flex items-center">
          <mat-icon class="mr-2 text-blue-500">gavel</mat-icon>
          เป็นบุคคลที่ถูกกำหนดหรือไม่ ?
        </label>
        <mat-radio-group class="flex flex-row gap-6" formControlName="designated_persons">
          <mat-radio-button class="radio-option" [value]="1">
            <div class="flex items-center">
              <mat-icon class="mr-1 text-green-500">check_circle</mat-icon>
              ใช่
            </div>
          </mat-radio-button>
          <mat-radio-button class="radio-option" [value]="0">
            <div class="flex items-center">
              <mat-icon class="mr-1 text-red-500">cancel</mat-icon>
              ไม่ใช่
            </div>
          </mat-radio-button>
        </mat-radio-group>
      </div>
      <div class="">
        <label class="block font-medium text-gray-700 mb-2 flex items-center">
          <mat-icon class="mr-2 text-blue-500">account_balance</mat-icon>
          สถานะการจ่ายประกันสังคม
        </label>
        <mat-radio-group class="flex flex-row gap-6" formControlName="sso_status">
          <mat-radio-button class="radio-option" [value]="1">
            <div class="flex items-center">
              <mat-icon class="mr-1 text-green-500">check_circle</mat-icon>
              จ่าย
            </div>
          </mat-radio-button>
          <mat-radio-button class="radio-option" [value]="0">
            <div class="flex items-center">
              <mat-icon class="mr-1 text-red-500">cancel</mat-icon>
              ไม่จ่าย
            </div>
          </mat-radio-button>
        </mat-radio-group>
      </div>
          <div class="">
        <label class="block font-medium text-gray-700 mb-2 flex items-center">
          <mat-icon class="mr-2 text-blue-500">payments</mat-icon>
          สถานะการจ่ายกองทุน
        </label>
        <mat-radio-group class="flex flex-row gap-6" formControlName="pvd_status">
          <mat-radio-button class="radio-option" [value]="1">
            <div class="flex items-center">
              <mat-icon class="mr-1 text-green-500">check_circle</mat-icon>
              จ่าย
            </div>
          </mat-radio-button>
          <mat-radio-button class="radio-option" [value]="0">
            <div class="flex items-center">
              <mat-icon class="mr-1 text-red-500">cancel</mat-icon>
              ไม่จ่าย
            </div>
          </mat-radio-button>
        </mat-radio-group>
      </div>

      <div class="">
        <label class="block font-medium text-gray-700 mb-2 flex items-center">
          <mat-icon svgIcon="heroicons_solid:user" class="mr-2 text-blue-500"></mat-icon>
          สถานะหัวหน้างาน
        </label>
        <mat-radio-group class="flex flex-row gap-6" formControlName="is_head">
          <mat-radio-button class="radio-option" [value]="1">
            <div class="flex items-center">
              ใช่
            </div>
          </mat-radio-button>
          <mat-radio-button class="radio-option" [value]="0">
            <div class="flex items-center">
              ไม่ใช่
            </div>
          </mat-radio-button>
        </mat-radio-group>
      </div>

      <!-- Account Information Section -->
      <div class="col-span-full mt-8">
        <h3 class="text-lg font-medium text-gray-700 mb-4 flex items-center">
          <mat-icon class="mr-2 text-blue-500">account_balance</mat-icon>
          ข้อมูลบัญชีธนาคาร
        </h3>
      </div>
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>เลือกธนาคาร</mat-label>
        <mat-select formControlName="bank_name">
          <mat-option *ngFor="let bank of banks" [value]="bank.name">
            {{ bank.name }}
          </mat-option>
        </mat-select>
        <mat-icon matSuffix>account_balance</mat-icon>
      </mat-form-field>
      <!-- Bank Info -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>ชื่อบัญชี</mat-label>
        <input matInput formControlName="account_name" />
        <mat-icon matSuffix>account_circle</mat-icon>
      </mat-form-field>

      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>เลขที่บัญชี</mat-label>
        <input matInput formControlName="account_no" />
        <!-- <mat-icon matSuffix">credit_card</mat-icon> -->
      </mat-form-field>

      <mat-form-field class="w-full" [ngClass]="formFieldHelpers" *ngIf="formData.get('bank_name').value === 'อื่น ๆ'">
        <mat-label>ระบุข้อมูลธนาคาร</mat-label>
        <input matInput formControlName="bank_name_other" />
        <!-- <mat-icon matSuffix">credit_card</mat-icon> -->
      </mat-form-field>

      <!-- Documents Section -->
      <div class="col-span-full mt-8">
        <h3 class="text-lg font-medium text-gray-700 mb-4 flex items-center">
          <mat-icon class="mr-2 text-blue-500">description</mat-icon>
          เอกสารประกอบ
        </h3>
      </div>

      <div class="col-span-full">
        <label class="block mb-2 font-medium text-gray-700 flex items-center">
          <mat-icon class="mr-2">attach_file</mat-icon>
          เอกสารแนบ
        </label>

        <div
          class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-blue-500 transition-colors duration-300">
          <!-- รองรับหลายไฟล์ -->
          <input type="file" (change)="onFileSelect($event)" hidden #fileInput multiple accept=".pdf,image/*" />

          <div class="flex flex-col items-center justify-center">
            <mat-icon class="text-4xl text-gray-400 mb-2">cloud_upload</mat-icon>
            <p class="text-gray-600 mb-2">ลากไฟล์มาวางที่นี่หรือคลิกเพื่อเลือกไฟล์</p>
            <button mat-stroked-button type="button" (click)="fileInput.click()">
              <mat-icon>folder_open</mat-icon>
              เลือกไฟล์
            </button>
            <p class="text-xs text-gray-500 mt-2">รองรับไฟล์ PDF, DOC, JPG ขนาดไม่เกิน 10MB</p>
          </div>

          <div *ngIf="selectedFiles.length > 0" class="mt-4">
            <p class="font-medium text-gray-700 mb-2">ไฟล์ที่เลือก:</p>

            <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2">
              <div *ngFor="let file of selectedFiles; let i = index"
                class="flex justify-between items-center p-2 bg-gray-100 rounded-lg shadow-sm">

                <!-- คลิกชื่อไฟล์เพื่อเปิดแท็บใหม่ (เฉพาะ image/pdf) -->
                <a *ngIf="file.type.includes('image') || file.type === 'application/pdf'" [href]="fileUrls[i]"
                  target="_blank" rel="noopener noreferrer" class="text-blue-600 hover:underline truncate">
                  {{ file.name }}
                </a>
                <!-- กรณีอื่นเป็นแค่ข้อความ -->
                <span *ngIf="!(file.type.includes('image') || file.type === 'application/pdf')" class="truncate">{{
                  file.name }}</span>
                <button mat-icon-button color="warn" (click)="removeFile(i)">
                  <mat-icon>close</mat-icon>
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>


      <!-- Signature Upload -->
      <div class="col-span-full mt-6 hidden">
        <label class="block mb-2 font-medium text-gray-700 flex items-center">
          <mat-icon class="mr-2">draw</mat-icon>
          ลายเซ็น
        </label>
        <div class="flex flex-col md:flex-row gap-6 items-center">
          <div
            class="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center w-full md:w-1/2 hover:border-blue-500 transition-colors duration-300">
            <input type="file" (change)="onSignatureChange($event)" hidden accept="image/*" #signatureInput />
            <div class="flex flex-col items-center justify-center">
              <mat-icon class="text-4xl text-gray-400 mb-2">create</mat-icon>
              <p class="text-gray-600 mb-2">อัปโหลดไฟล์ลายเซ็น</p>
              <button mat-stroked-button type="button" (click)="signatureInput.click()">
                <mat-icon>image</mat-icon>
                เลือกไฟล์ภาพ
              </button>
              <p class="text-xs text-gray-500 mt-2">รองรับไฟล์ PNG ขนาดไม่เกิน 2MB</p>
            </div>
          </div>
          <div class="w-full md:w-1/2 flex justify-center">
            <div class="border rounded-lg p-4 bg-gray-50 w-64 h-40 flex items-center justify-center">
              <img *ngIf="previewSignature" [src]="previewSignature" class="max-w-full max-h-full" />
              <div *ngIf="!previewSignature" class="text-gray-400 text-center">
                <mat-icon class="text-4xl">image</mat-icon>
                <p class="mt-2">ยังไม่มีลายเซ็น</p>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="overflow-x-auto mt-4 col-span-full" *ngIf="url_doc.length > 0">
        <table class="min-w-full divide-y divide-gray-200 border rounded-lg shadow-sm">
          <thead class="bg-gray-100 text-gray-700 text-left text-sm font-semibold">
            <tr>
              <th class="px-4 py-3">ลำดับ</th>
              <th class="px-4 py-3">ชื่อไฟล์</th>
              <th class="px-4 py-3">ดาวน์โหลด</th>
              <th class="px-4 py-3 text-center">ลบ</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-200 text-sm">
            <tr *ngFor="let file of url_doc; let i = index" class="hover:bg-gray-50">
              <td class="px-4 py-2">{{ i + 1 }}</td>
              <td class="px-4 py-2">{{ file.name }}</td>
              <td class="px-4 py-2">
                <a class="text-blue-600 hover:underline flex items-center space-x-1" (click)="openFile(file)"
                  target="_blank" rel="noopener noreferrer">
                  <span>เปิด / ดาวน์โหลด</span>
                </a>
              </td>
              <td class="px-4 py-2 text-center">
                <button mat-icon-button color="warn" (click)="onRemoveDoc(file.id)">
                  <mat-icon>delete</mat-icon>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      <!-- Submit Section -->
      <div class="col-span-full flex justify-end mt-8 pt-6 border-t">
        <button mat-raised-button color="warn" class="mr-4" type="button">
          <mat-icon>cancel</mat-icon>
          ยกเลิก
        </button>
        <button mat-raised-button class="bg-green-600 hover:bg-green-700 text-white" (click)="onSubmit()">
          <mat-icon>save</mat-icon>
          บันทึกข้อมูล
        </button>
      </div>
    </form>
  </mat-tab>
  <mat-tab label="ข้อมูลเงินได้ / เงินหัก" *ngIf="userId">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6 w-full">
      <!-- ฝั่งซ้าย: เงินได้ -->
      <div class="bg-white rounded-lg shadow p-4 w-full">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold text-green-600">📥 รายการเงินได้</h2>
          <button mat-flat-button class="bg-green-600 hover:bg-green-700 text-white" (click)="addIncome()">
            <mat-icon>add</mat-icon>
            เพิ่มเงินได้</button>
        </div>
        <table class="min-w-full divide-y divide-gray-200 text-sm">
          <thead class="bg-gray-50 text-gray-700">
            <tr>
              <th class="px-4 py-2 text-left">#</th>
              <th class="px-4 py-2 text-left">ประเภท</th>
              <th class="px-4 py-2 text-right">จำนวนเงิน</th>
              <th class="px-4 py-2 text-center">ลบ</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-100">
            <tr *ngFor="let item of incomeData; let i = index">
              <td class="px-4 py-2">{{ i + 1 }}</td>
              <td class="px-4 py-2">{{ item.income_type?.name }}</td>
              <td class="px-4 py-2 text-right text-green-600">{{ item.price | number:'1.2-2' }}</td>
              <td class="px-4 py-2 text-center">
                <button mat-icon-button color="warn" (click)="deleteIncome(item.id)" matTooltip="ลบรายการ">
                  <mat-icon>delete</mat-icon>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- ฝั่งขวา: เงินหัก -->
      <div class="bg-white rounded-lg shadow p-4 w-full">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-lg font-semibold text-red-600">📤 รายการเงินหัก</h2>
          <button mat-flat-button class="bg-red-600 hover:bg-red-700 text-white" (click)="addDeduct()">
            <mat-icon>add</mat-icon>
            เพิ่มเงินหัก
          </button>
        </div>
        <table class="min-w-full divide-y divide-gray-200 text-sm">
          <thead class="bg-gray-50 text-gray-700">
            <tr>
              <th class="px-4 py-2 text-left">#</th>
              <th class="px-4 py-2 text-left">ประเภท</th>
              <th class="px-4 py-2 text-right">จำนวนเงิน</th>
              <th class="px-4 py-2 text-center">ลบ</th>
            </tr>
          </thead>
          <tbody class="divide-y divide-gray-100">
            <tr *ngFor="let item of deductionData; let i = index">
              <td class="px-4 py-2">{{ i + 1 }}</td>
              <td class="px-4 py-2">{{ item.deduct_type?.name }}</td>
              <td class="px-4 py-2 text-right text-red-600">{{ item.price | number:'1.2-2' }}</td>
              <td class="px-4 py-2 text-center">
                <button mat-icon-button color="warn" (click)="removeDeduction(item.id)" matTooltip="ลบรายการ">
                  <mat-icon>delete</mat-icon>
                </button>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

  </mat-tab>
</mat-tab-group>