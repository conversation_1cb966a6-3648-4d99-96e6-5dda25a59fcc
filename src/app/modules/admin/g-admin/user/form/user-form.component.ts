import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MasterDataService } from 'app/shared/master-data.service';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import moment from 'moment';
import { ActivatedRoute, Router } from '@angular/router';
import { UserService } from '../user.service';
import { environment } from 'environments/environment';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDialog } from '@angular/material/dialog';
import { DialogDeletemoneyComponent } from '../../deletemoney/dialog-add/dialog-add.component';
import { DialogPlusemoneyComponent } from '../../plusmoney/dialog-add/dialog-add.component';

export interface Permission { id: number; name: string; }
export interface Department { id: number; name: string; }
export interface Position { id: number; name: string; }
export interface Employee { id: number; first_name: string; last_name: string }
export interface WorkShift { id: number; name: string; }
export interface Branch { id: number; name: string; }
export interface Head { id: number; first_name: string; last_name: string }

export interface Prefix {
  code: string;
  label: string;
  language: 'th' | 'en';
  category?: string;
}

export const PREFIX_LIST: Prefix[] = [
  // --- ภาษาไทย: บุคคลทั่วไป ---
  { code: 'mr_th', label: 'นาย', language: 'th', category: 'บุคคลทั่วไป' },
  { code: 'mrs_th', label: 'นาง', language: 'th', category: 'บุคคลทั่วไป' },
  { code: 'ms_th', label: 'นางสาว', language: 'th', category: 'บุคคลทั่วไป' },
  { code: 'other_th', label: 'อื่น ๆ', language: 'th', category: 'บุคคลทั่วไป' },



  // --- ภาษาอังกฤษ: General ---
  { code: 'mr_en', label: 'Mr.', language: 'en', category: 'General' },
  { code: 'mrs_en', label: 'Mrs.', language: 'en', category: 'General' },
  { code: 'ms_en', label: 'Ms.', language: 'en', category: 'General' },
  { code: 'miss_en', label: 'Miss', language: 'en', category: 'General' },
  { code: 'other_en', label: 'Other', language: 'en', category: 'General' },


];



interface MasterData {
  permissions: Permission[];
  departments: Department[];
  positions: Position[];
  employees: Employee[];
  workShifts: WorkShift[];
  branchs: Branch[];
  heads: Head[];
}

@Component({
  selector: 'app-user-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatRadioModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,

  ],
  templateUrl: './user-form.component.html',
})
export class UserFormComponent implements OnInit {
  formData!: FormGroup;

  thaiPrefixes = PREFIX_LIST.filter(p => p.language === 'th');
  englishPrefixes = PREFIX_LIST.filter(p => p.language === 'en');

  banks = [
    { value: 'bbl', name: 'ธนาคารกรุงเทพ (BBL)' },
    { value: 'kbank', name: 'ธนาคารกสิกรไทย (KBank)' },
    { value: 'scb', name: 'ธนาคารไทยพาณิชย์ (SCB)' },
    { value: 'ktb', name: 'ธนาคารกรุงไทย (KTB)' },
    { value: 'bay', name: 'ธนาคารกรุงศรีอยุธยา (Krungsri/BAY)' },
    { value: 'ttb', name: 'ธนาคารทหารไทยธนชาต (TTB)' },
    { value: 'uob', name: 'ธนาคารยูโอบี (UOB)' },
    { value: 'cimb', name: 'ธนาคารซีไอเอ็มบีไทย (CIMB)' },
    { value: 'tisco', name: 'ธนาคารทิสโก้ (TISCO)' },
    { value: 'kkp', name: 'ธนาคารเกียรตินาคินภัทร (KKP)' },
    { value: 'icbc', name: 'ธนาคารไอซีบีซี (ICBC)' },
    { value: 'hsbc', name: 'ธนาคารเอชเอสบีซี (HSBC)' },
    { value: 'standard', name: 'ธนาคารสแตนดาร์ดชาร์เตอร์ด (Standard Chartered)' },
    { value: 'other', name: 'อื่น ๆ' }
  ];

  // Mock dropdown data
  incomeData = [];
  deductionData = [];
  positionList = [];
  departmentList = [];
  branchList = [];
  permissionList = [];
  nationalList = [
    { value: 'thai', name: 'ไทย' },
    { value: 'lao', name: 'ลาว' },
    { value: 'mymar', name: 'เมียนม่า' },
    { value: 'other', name: 'อื่น ๆ' },
  ];
  workTypeList = [
    { value: 'day', name: 'รายวัน' },
    { value: 'month', name: 'รายเดือน' },
  ];

  employeeList: any = []
  headList: any = []
  workShiftList: any = []
  brandhList: any = []
  url_doc: any = []

  previewImage: string | ArrayBuffer | null = null;
  previewSignature: string | ArrayBuffer | null = null;
  formFieldHelpers: string[] = ['fuse-mat-dense'];
  masterData!: MasterData;

  userId: any;
  constructor(
    private _formBuilder: FormBuilder,
    private masterDataService: MasterDataService,
    private _fuseConfirmationService: FuseConfirmationService,
    private _router: Router,
    private _service: UserService,
    private _activatedRoute: ActivatedRoute,
    private _changeDetectorRef: ChangeDetectorRef,
    private _matDialog: MatDialog
  ) {
    this._activatedRoute.params.subscribe((params) => {
      const id = params.id;
      this.userId = id
      // console.log(this.userId);
      if (id) {
        this._service.getdeductUser(id).subscribe((resp: any) => {
          this.deductionData = resp.data
        })
        this._service.getIncomeUser(id).subscribe((resp: any) => {
          this.incomeData = resp.data
        })
      }
    })
  }

  ngOnInit(): void {
    this.masterDataService.getMasterData().subscribe(data => {
      this.masterData = data;
      this.positionList = this.masterData.positions;
      this.departmentList = this.masterData.departments;
      this.employeeList = this.masterData.employees;
      this.permissionList = this.masterData.permissions;
      this.workShiftList = this.masterData.workShifts;
      this.branchList = this.masterData.branchs;
      this.headList = this.masterData.heads;


    });
    this.formData = this._formBuilder.group({
      id: [''],
      user_id: ['', Validators.required],
      prefix: ['', Validators.required],
      prefix_other: [''],
      first_name: ['', Validators.required],
      last_name: ['', Validators.required],
      prefix_en: [''],
      prefix_other_en: [''],
      first_name_en: [''],
      last_name_en: [''],
      permission_id: [''],
      department_id: [''],
      position_id: ['', Validators.required],
      branch_id: ['', Validators.required],
      email: ['', Validators.required],
      password: ['', Validators.required],
      salary: [0, Validators.required],
      image: [''],
      image_signature: [''],
      personnel_id: [''],
      sex: [''],
      register_date: [''],
      account_name: [''],
      account_no: [''],
      bank_name: [''],
      bank_name_other: [''],
      files: [''],
      work_type: [''],
      work_shift_id: [1],
      head_id: [''],
      citizen_no: ['', Validators.required],
      phone_no: ['', Validators.required],
      birthday: [''],
      end_date: [''],
      nationality: [''],
      designated_persons: 0,
      sync_olaf_status: 1,
      status: [1],
      work_status: ['ปกติ'],
      is_head: [0],
      home_address: [''],
      province: [''],
      district: [''],
      subdistrict: [''],
      zip_code: [''],
      sso_status: [1], //สถานะการจ่ายประกันสังคม
      pvd_status: [1], //สถานะการจ่ายกองทุน
      payroll_status: [1],
    });

    if (this.userId) {
      this._service.getUserbyId(this.userId).subscribe((resp: any) => {
        this.formData.patchValue({
          ...resp.data,
          image: '',
          image_signature: '',
        })
        console.log(this.formData.value, 'formValue');
        this.previewImage = resp.data.image
      })
      this._service.getFilesbyId(this.userId).subscribe((resp: any) => {
        this.url_doc = resp.data;
      });
    }
  }

  onImageChange(event: Event): void {
    const file = (event.target as HTMLInputElement)?.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => (this.previewImage = reader.result);
      reader.readAsDataURL(file);
      this.formData.patchValue({ image: file });
    }
  }

  onSignatureChange(event: Event): void {
    const file = (event.target as HTMLInputElement)?.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = () => (this.previewSignature = reader.result);
      reader.readAsDataURL(file);
      this.formData.patchValue({ image_signature: file });
    }
  }

  selectedFiles: File[] = [];
  fileUrls: string[] = [];

  onFileSelect(event: Event): void {
    const input = event.target as HTMLInputElement;
    if (input.files && input.files.length > 0) {
      const newFiles = Array.from(input.files);

      newFiles.forEach(file => {
        if (!this.selectedFiles.find(f => f.name === file.name)) {
          this.selectedFiles.push(file);
          this.fileUrls.push(URL.createObjectURL(file)); // สร้าง URL ชั่วคราว
        }
      });

      const fileNames = this.selectedFiles.map(file => file.name).join(', ');
      this.formData.patchValue({ files: fileNames });
    }
  }

  removeFile(index: number): void {
    this.selectedFiles.splice(index, 1);
    const url = this.fileUrls.splice(index, 1)[0];

    // cleanup URL object
    if (url) {
      URL.revokeObjectURL(url);
    }

    const fileNames = this.selectedFiles.map(file => file.name).join(', ');
    this.formData.patchValue({ files: fileNames });
  }


  onSubmit(): void {
    // console.log(this.formData.value);
    const confirmation = this._fuseConfirmationService.open({
      title: 'บันทึกข้อมูล',
      message: 'คุณต้องการบันทึกข้อมูลผู้ใช้งานใช่หรือไม่ ?',
      icon: {
        show: true,
        name: 'heroicons_outline:question-mark-circle',
        color: 'info',
      },
      actions: {
        confirm: {
          show: true,
          label: 'ยืนยัน',
          color: 'primary',
        },
        cancel: {
          show: true,
          label: 'ยกเลิก',
        },
      },
      dismissible: true,
    });

    // Subscribe to the confirmation dialog closed action
    confirmation.afterClosed().subscribe((result) => {
      // If the confirm button pressed...
      if (result === 'confirmed') {
        let regis_date = moment(
          this.formData.value.register_date
        ).format('YYYY-MM-DD');

        let formatbirthday = moment(
          this.formData.value.birthday
        ).format('YYYY-MM-DD');
        this.formData.patchValue({
          register_date: regis_date,
          birthday: formatbirthday,
          user_id: this.formData.value.personnel_id
        });

        if (this.formData.value.end_date) {
          let enddateformat = moment(
            this.formData.value.end_date,
          ).format('YYYY-MM-DD');
          this.formData.patchValue({
            register_date: regis_date,
            user_id: this.formData.value.personnel_id,
            birthday: formatbirthday,
            end_date: enddateformat,
          });
        } else {
          this.formData.patchValue({
            register_date: regis_date,
            user_id: this.formData.value.personnel_id,
            birthday: formatbirthday,
            end_date: '',
          });
        }

        if (this.formData.value.prefix_other) {
          this.formData.value.prefix = this.formData.value.prefix_other
        }

        if (this.formData.value.prefix_other_en) {
          this.formData.value.prefix_en = this.formData.value.prefix_other_en
        }

        let formValue = this.formData.value;
        const formDataToSend = new FormData();
        // const formData = new FormData();
        // เพิ่มข้อมูลอื่นๆ ใน formData (ยกเว้นไฟล์)
        for (const key in formValue) {
          if (key !== 'files') {
            const value = formValue[key] === null ? '' : formValue[key];
            formDataToSend.append(key, value);
          }
        }
        // เพิ่มไฟล์แบบมีทั้ง name และ data ตามโครงสร้าง
        this.selectedFiles.forEach((file, index) => {
          formDataToSend.append(`files[${index}][name]`, file.name);
          formDataToSend.append(`files[${index}][data]`, file);
        });
        if (this.userId) {
          let formvalueAddFile = {
            user_id: this.userId
          }
          const formDataAddFile = new FormData();
          for (const key in formvalueAddFile) {
            if (key !== 'files') {
              const value = formvalueAddFile[key] === null ? '' : formvalueAddFile[key];
              formDataAddFile.append(key, value);
            }
          }
          this.selectedFiles.forEach((file, index) => {
            formDataAddFile.append(`files[${index}][name]`, file.name);
            formDataAddFile.append(`files[${index}][data]`, file);
          });
          this._service.addFile(formDataAddFile).subscribe({
            next: (resp: any) => {
              // โหลดไฟล์ใหม่หลังอัปโหลดสำเร็จ
              this._service.getFilesbyId(this.userId).subscribe({
                next: (resp: any) => {
                  this.url_doc = resp.data;
                },
                error: (err: any) => {
                  console.error('โหลดไฟล์ไม่สำเร็จ', err);
                }
              });

              // หลังจากอัปโหลดไฟล์แล้วค่อยอัปเดตผู้ใช้งาน
              this._service.updateUser(formDataToSend).subscribe({
                next: (resp: any) => {
                  this._router.navigateByUrl('user/list');
                },
                error: (err: any) => {
                  this._fuseConfirmationService.open({
                    title: 'เกิดข้อผิดพลาด',
                    message: err.error?.message || 'ไม่สามารถอัปเดตข้อมูลได้',
                    icon: {
                      show: true,
                      name: 'heroicons_outline:exclamation-triangle',
                      color: 'warning',
                    },
                    actions: {
                      confirm: {
                        show: false,
                        label: 'ยืนยัน',
                        color: 'primary',
                      },
                      cancel: {
                        show: false,
                        label: 'ยกเลิก',
                      },
                    },
                    dismissible: true,
                  });
                },
              });
            },
            error: (err: any) => {
              console.error('อัปโหลดไฟล์ไม่สำเร็จ', err);
              this._fuseConfirmationService.open({
                title: 'เกิดข้อผิดพลาด',
                message: err.error?.message || 'ไม่สามารถอัปโหลดไฟล์ได้',
                icon: {
                  show: true,
                  name: 'heroicons_outline:exclamation-triangle',
                  color: 'warning',
                },
                actions: {
                  confirm: {
                    show: false,
                    label: 'ยืนยัน',
                    color: 'primary',
                  },
                  cancel: {
                    show: false,
                    label: 'ยกเลิก',
                  },
                },
                dismissible: true,
              });
            },
          });
        } else {

          this._service.createUser(formDataToSend).subscribe({
            next: (resp: any) => {
              this._router.navigateByUrl('user/list').then(() => { });
            },
            error: (err: any) => {
              if (typeof err.error.message === 'object' && err.error.message !== null) {
                const errorMessages = Object.keys(err.error.message)
                  .map((key) => `${key}: ${err.error.message.errors[key]}`)
                  .join('\n');
                this._fuseConfirmationService.open({
                  title: 'เกิดข้อผิดพลาด',
                  message: errorMessages,
                  icon: {
                    show: true,
                    name: 'heroicons_outline:exclamation-triangle',
                    color: 'warning',
                  },
                  actions: {
                    confirm: {
                      show: false,
                      label: 'ยืนยัน',
                      color: 'primary',
                    },
                    cancel: {
                      show: false,
                      label: 'ยกเลิก',
                    },
                  },
                  dismissible: true,
                });
              } else {
                this._fuseConfirmationService.open({
                  title: 'เกิดข้อผิดพลาด',
                  message: err.error.message,
                  icon: {
                    show: true,
                    name: 'heroicons_outline:exclamation-triangle',
                    color: 'warning',
                  },
                  actions: {
                    confirm: {
                      show: false,
                      label: 'ยืนยัน',
                      color: 'primary',
                    },
                    cancel: {
                      show: false,
                      label: 'ยกเลิก',
                    },
                  },
                  dismissible: true,
                });
              }

            }
          });
        }
      }
    });
  }

  openFile(file: { data: string; name: string }) {
    const url = environment.API_URL + file.data;
    if (url) {
      window.open(url, '_blank');
    } else {
      console.warn('ไม่พบ URL ของไฟล์');
    }
  }

  hidePassword = true;

  togglePasswordVisibility(): void {
    this.hidePassword = !this.hidePassword;
  }

  onRemoveDoc(id: any) {
    const confirmation = this._fuseConfirmationService.open({
      title: 'ยืนยันการลบไฟล์',
      message: 'คุณต้องการลบไฟล์ ใช่หรือไม่ ?',
      icon: {
        show: true,
        name: 'heroicons_outline:information-circle',
        color: 'info',
      },
      actions: {
        confirm: {
          show: true,
          label: 'ยืนยัน',
          color: 'primary',
        },
        cancel: {
          show: true,
          label: 'ยกเลิก',
        },
      },
      dismissible: true,
    });
    confirmation.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this._service.deleteFile(id).subscribe({
          next: (resp: any) => {
            this._service.getFilesbyId(this.userId).subscribe((resp: any) => {
              this.url_doc = resp.data;
            });
          },
          error: (err: any) => {
            this._fuseConfirmationService.open({
              title: 'เกิดข้อผิดพลาด',
              message: err.error.message,
              icon: {
                show: true,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
              },
              actions: {
                confirm: {
                  show: false,
                  label: 'ยืนยัน',
                  color: 'primary',
                },
                cancel: {
                  show: false,
                  label: 'ยกเลิก',
                },
              },
              dismissible: true,
            });
          },
        });
      }
    });
  }

  addDeduct() {
    const dialogRef = this._matDialog.open(DialogDeletemoneyComponent, {
      width: '800px',
      height: 'auto',
      data: this.userId
    });

    dialogRef.afterClosed().subscribe((item) => {

      this._service.getdeductUser(this.userId).subscribe((resp: any) => {
        this.deductionData = resp.data;
        this._changeDetectorRef.markForCheck();
      });
    });
  }

  addIncome() {
    const dialogRef = this._matDialog.open(DialogPlusemoneyComponent, {
      width: '800px',
      height: 'auto',
      data: this.userId
    });

    dialogRef.afterClosed().subscribe((item) => {
      this._service.getIncomeUser(this.userId).subscribe((resp: any) => {
        this.incomeData = resp.data;
        this._changeDetectorRef.markForCheck();
      });
    });
  }

  deleteIncome(id: any) {
    const confirmation = this._fuseConfirmationService.open({
      title: 'ยืนยันการลบข้อมูล',
      message: 'คุณต้องการลบเงินเพิ่ม ใช่หรือไม่ ?',
      icon: {
        show: true,
        name: 'heroicons_outline:information-circle',
        color: 'info',
      },
      actions: {
        confirm: {
          show: true,
          label: 'ยืนยัน',
          color: 'primary',
        },
        cancel: {
          show: true,
          label: 'ยกเลิก',
        },
      },
      dismissible: true,
    });
    confirmation.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this._service.deleteIncome(id).subscribe({
          next: (resp: any) => {
            this._service.getIncomeUser(this.userId).subscribe((resp: any) => {
              this.incomeData = resp.data;
            });
          },
          error: (err: any) => {
            this._fuseConfirmationService.open({
              title: 'มีข้อผิดพลาด',
              message: err.error.message,
              icon: {
                show: true,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
              },
              actions: {
                confirm: {
                  show: false,
                  label: 'ยืนยัน',
                  color: 'primary',
                },
                cancel: {
                  show: false,
                  label: 'ยกเลิก',
                },
              },
              dismissible: true,
            });
          },
        });
      }
    });
  }

  removeDeduction(id: any) {
    const confirmation = this._fuseConfirmationService.open({
      title: 'ยืนยันการลบข้อมูล',
      message: 'คุณต้องการลบเงินหัก ใช่หรือไม่ ?',
      icon: {
        show: true,
        name: 'heroicons_outline:information-circle',
        color: 'info',
      },
      actions: {
        confirm: {
          show: true,
          label: 'ยืนยัน',
          color: 'primary',
        },
        cancel: {
          show: true,
          label: 'ยกเลิก',
        },
      },
      dismissible: true,
    });
    confirmation.afterClosed().subscribe((result) => {
      if (result === 'confirmed') {
        this._service.deleteDeduct(id).subscribe({
          next: (resp: any) => {
            this._service.getdeductUser(this.userId).subscribe((resp: any) => {
              this.deductionData = resp.data;
            });
          },
          error: (err: any) => {
            this._fuseConfirmationService.open({
              title: 'มีข้อผิดพลาด',
              message: err.error.message,
              icon: {
                show: true,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
              },
              actions: {
                confirm: {
                  show: false,
                  label: 'ยืนยัน',
                  color: 'primary',
                },
                cancel: {
                  show: false,
                  label: 'ยกเลิก',
                },
              },
              dismissible: true,
            });
          },
        });
      }
    });
  }


}
