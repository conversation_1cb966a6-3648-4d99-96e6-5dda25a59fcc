import {
  AfterViewInit,
  Component,
  EventEmitter,
  Input,
  OnChanges,
  OnDestroy,
  OnInit,
  Output,
  SimpleChanges,
} from '@angular/core';
import {
  FormBuilder,
  FormGroup,
} from '@angular/forms';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { UserService } from '../user.service';
import { NgIf, NgFor, DecimalPipe, DatePipe } from '@angular/common';
import { ViewWarningComponent } from '../../manage-warning/view-warning/view-warning.component';
import { ChangeDetectorRef } from '@angular/core';

@Component({
  selector: 'app-approve-warning',
  templateUrl: './approve-warning.component.html',
  styleUrl: './approve-warning.component.scss',
  animations: fuseAnimations,
  imports: [NgIf, <PERSON>For, DecimalPipe, DatePipe],
  standalone: true,
})
export class ApproveWarningComponent implements OnInit, AfterViewInit, OnDestroy, OnChanges {
  @Input() status: string = '';
  @Input() user_id: string = '';
  @Input() head_id: string = '';

  @Output() refresh = new EventEmitter<object>();
  dataRow: any = [];
  dataSort: any = [];
  formData: FormGroup;
  flashMessage: 'success' | 'error' | null = null;
  isLoading: boolean = false;
  env_path = environment.API_URL;

  /**
   * Constructor
   */
  constructor(
    private _Service: UserService,
    private _matDialog: MatDialog,
    private _changeDetectorRef: ChangeDetectorRef,

  ) {

  }

  /**
   * On init
   */
  ngOnInit(): void {
    this.loadTableOT()
  }

  trackByIndex(index: number, item: any): number {
    return index;
  }

  ngOnChanges(changes: SimpleChanges): void {
    // ตรวจสอบการเปลี่ยนแปลงของ Input Properties
    if (changes['status']) {
    }
    if (changes['user_id']) {
    }
  }

  fetchData(item: any, status: any): void {
    let formValue = {
      id: item.id,
      status: status
    }
    // Logic เรียก API ที่นี่

    this.refresh.emit(formValue); // แจ้งกลับไปยัง Parent Component หากต้องการ
  }



  pagesOt = { current_page: 1, last_page: 1, per_page: 10, begin: 0, total: 0 };
  gotoPage: number = 1; // ตัวแปรสำหรับการไปหน้าที่กำหนด
  loadTableOT(): void {
    const user = JSON.parse(localStorage.getItem('user'));
    const dataTablesParameters = {
      draw: 1, // ระบุ draw เริ่มต้น
      head_id: this.head_id, // ใช้ user id จาก localStorage
      start: 0, // จุดเริ่มต้นของข้อมูล
      length: 10, // จำนวนข้อมูลต่อหน้า
      columns: [
        { data: 'no', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
        { data: 'income_type.name', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
        { data: 'description', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
        { data: 'date', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
        { data: 'price', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
      ],
      order: [
        { column: 0, dir: 'desc' } // จัดเรียงตามคอลัมน์แรก
      ],
      search: { value: '', regex: false }, // ค่า search
      status: null // สถานะ
    };

    // ส่ง API และเก็บผลลัพธ์
    this._Service.getApproveWarning(dataTablesParameters).subscribe(
      (resp) => {
        this.dataRow = resp.data; // เก็บข้อมูลที่ได้
        this.pagesOt.current_page = this.dataRow.current_page;
        this.pagesOt.last_page = this.dataRow.last_page;
        this.pagesOt.per_page = this.dataRow.per_page;
        this.pagesOt.total = this.dataRow.total;
        if (this.dataRow.current_page > 1) {
          this.pagesOt.begin =
            this.dataRow.per_page * this.dataRow.current_page - 1;
        } else {
          this.pagesOt.begin = 0;
        }
      },
      (error) => {
        console.error('API Error:', error);
      }
    );
  }
  getMinValue(a: number, b: number): number {
    return Math.min(a, b);
  }

  changePage(page: number): void {
    const user = JSON.parse(localStorage.getItem('user'));
    const dataTablesParameters = {
      draw: page, // ระบุ draw เริ่มต้น
      head_id: this.head_id, // ใช้ user id จาก localStorage
      start: (page - 1) * this.pagesOt.per_page,
      length: 10, // จำนวนข้อมูลต่อหน้า
      columns: [
        { data: 'no', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
        { data: 'income_type.name', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
        { data: 'description', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
        { data: 'date', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
        { data: 'price', name: '', searchable: true, orderable: true, search: { value: '', regex: false } },
      ],
      order: [
        { column: 0, dir: 'desc' } // จัดเรียงตามคอลัมน์แรก
      ],
      search: { value: '', regex: false }, // ค่า search
      status: null // สถานะ
    };
    // ส่ง API และเก็บผลลัพธ์
    this._Service.getApproveWarning(dataTablesParameters).subscribe(
      (resp) => {
        this.dataRow = resp.data; // เก็บข้อมูลที่ได้
        this.pagesOt.current_page = this.dataRow.current_page;
        this.pagesOt.last_page = this.dataRow.last_page;
        this.pagesOt.per_page = this.dataRow.per_page;
        this.pagesOt.total = this.dataRow.total;
        if (this.dataRow.current_page > 1) {
          this.pagesOt.begin =
            this.dataRow.per_page * this.dataRow.current_page - 1;
        } else {
          this.pagesOt.begin = 0;
        }
      },
      (error) => {
        console.error('API Error:', error);
      }
    );
  }


  /**
   * After view init
   */
  ngAfterViewInit(): void { }

  /**
   * On destroy
   */
  ngOnDestroy(): void {

  }

  /**
   * Show flash message
   */
  showFlashMessage(type: 'success' | 'error'): void {
    // Show the message
    this.flashMessage = type;

    // Mark for check
    // this._changeDetectorRef.markForCheck();

    // Hide it after 3 seconds
    setTimeout(() => {
      this.flashMessage = null;
      // Mark for check
      // this._changeDetectorRef.markForCheck();
    }, 3000);
  }
  View(id: any) {
    const dialogRef = this._matDialog.open(ViewWarningComponent, {
      // width: '50%',
      // minHeight: 'calc(100vh - 90px)',
      // height: 'auto'
      width: '1000px',
      height: '95%',
      data: { id: id },
    });

    dialogRef.afterClosed().subscribe((item) => {
      this._changeDetectorRef.markForCheck();
      this.loadTableOT()
    });
  }


}
