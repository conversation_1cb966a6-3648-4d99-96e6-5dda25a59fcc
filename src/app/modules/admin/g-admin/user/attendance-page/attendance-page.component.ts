import { Component, inject, OnInit, computed, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormControl, ReactiveFormsModule, Validators } from '@angular/forms';
import { Router } from '@angular/router';

import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatOptionModule } from '@angular/material/core';
import { MatIconModule } from '@angular/material/icon';
import { MatButtonModule } from '@angular/material/button';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';

import { debounceTime, map, startWith } from 'rxjs/operators';
import { Observable, of } from 'rxjs';
import { TimeAttendanceService } from '../../report/time-attendance/time-attendance.service';
import { DateTime } from 'luxon';
import { FuseConfirmationService } from '@fuse/services/confirmation';

type Mode = 'IN' | 'OUT' | 'BOTH';

interface LineItem {
    line_id: string;
    fullname: string;
}

@Component({
    selector: 'attendance-page',
    standalone: true,
    imports: [
        CommonModule,
        ReactiveFormsModule,

        // Material
        MatFormFieldModule,
        MatInputModule,
        MatSelectModule,
        MatOptionModule,
        MatIconModule,
        MatButtonModule,
        MatDatepickerModule,
        MatNativeDateModule,

        // 3rd party
        NgxMatSelectSearchModule,
    ],
    templateUrl: './attendance-page.component.html'
})
export class AttendancePageComponent implements OnInit {
    private fb = inject(FormBuilder);
    private router = inject(Router);
    private _service = inject(TimeAttendanceService)
    private _fuseConfirmationService = inject(FuseConfirmationService)

    // เวลาเข้า/ออกแบบ fix
    readonly IN_TIME = '08:00:00';
    readonly OUT_TIME = '17:01:00';

    // ฟอร์มหลักบนเพจ
    form = this.fb.group({
        line_id: [''],
        user_id: [''],
        time: [new Date(), Validators.required],
        mode: ['BOTH' as Mode, Validators.required],
        // ฟิลด์ที่ไม่แสดง แต่ต้องมีเพื่อ validation ผ่าน
        latitude: 13.80667107691927,
        longitude: 100.14242130654252,
        address: 'GS สำนักงานใหญ่',
        pic: [''],
        inTime: '',
        outTime:'',
    });

    // เอาไว้ใช้ในเทมเพลต (แทน mode.value จาก dialog เดิม)
    get modeCtrl() { return this.form.get('mode') as FormControl<Mode>; }

    // รายการ LINE + ช่องค้นหา
    lineSearch = new FormControl<string>('', { nonNullable: true });
    allLines: LineItem[] = [];
    filteredLines$!: Observable<LineItem[]>;

    submitting = false;

    ngOnInit(): void {
        // 1) โหลดรายการไลน์พนักงาน (ตัวอย่างจำลอง; เปลี่ยนเป็น service จริงของคุณ)
        this._service.getUser('').subscribe((resp: any) => {
            this.allLines = resp.data;
            console.log(this.allLines, 'allLine');

            this.filteredLines$ = this.lineSearch.valueChanges.pipe(
                startWith(this.lineSearch.value ?? ''),
                debounceTime(150),
                map(txt => {
                    const t = (txt ?? '').toLowerCase();
                    return this.allLines.filter(l =>
                        l.fullname.toLowerCase().includes(t)
                    );
                })
            );

        })
    }

    // ปุ่ม "ยกเลิก" บนเพจ -> กลับหน้าก่อนหน้า
    onCancel(): void {
        // ไป path อะไรก็ได้ที่เหมาะกับระบบคุณ เช่น '/attendance'
        this.router.navigate(['/report/time-attendance/zk']);
    }

    async onSave(): Promise<void> {
        this.submitting = true;
        try {
            const { user_id, time, mode, latitude, longitude, address, pic , inTime, outTime} = this.form.value;
            if (!user_id || !time || !mode) { return; }

            const dateObj = time as Date;
            const inTimeConvert = this.formatDate(dateObj, inTime);   // "YYYY-MM-DD HH:mm:ss"
            const outTimeConvert = this.formatDate(dateObj, outTime); // "YYYY-MM-DD HH:mm:ss"

            type Punch = { user_id: string; time: string; type: 'IN' | 'OUT' };
            const punches: Punch[] = [];
            if (mode !== 'OUT') { punches.push({ user_id, time: inTimeConvert, type: 'IN' }); }
            if (mode !== 'IN') { punches.push({ user_id, time: outTimeConvert, type: 'OUT' }); }

            const buildPayload = (p: Punch) => ({
                user_id: p.user_id,
                time: p.time,
                type: p.type,
                latitude,
                longitude,
                address,
                pic,
            });

            for (const p of punches) {
                const payload = buildPayload(p);
                await this._service.saveAttendance(payload).toPromise();
            }

            // ✅ แสดง Dialog สำเร็จ
            this._fuseConfirmationService.open({
                title: 'สำเร็จ',
                message: 'บันทึกเวลาเข้างานเรียบร้อยแล้ว',
                icon: {
                    show: true,
                    name: 'heroicons_outline:check-circle',
                    color: 'success',
                },
                actions: {
                    confirm: { show: false },
                    cancel: { show: false },
                },
                dismissible: true,
            });
            this.router.navigate(['/report/time-attendance/zk']);

        } catch (err: any) {
            console.error(err);

            // ❌ แสดง Dialog ไม่สำเร็จ
            this._fuseConfirmationService.open({
                title: 'เกิดข้อผิดพลาด',
                message: err.error?.message || 'ไม่สามารถอัปเดตข้อมูลได้',
                icon: {
                    show: true,
                    name: 'heroicons_outline:exclamation-triangle',
                    color: 'warning',
                },
                actions: {
                    confirm: { show: false },
                    cancel: { show: false },
                },
                dismissible: true,
            });

        } finally {
            this.submitting = false;
        }
    }

    private ensureHHMMSS(t: string): string {
        // รับได้ทั้ง "08:00" หรือ "08:00:00" -> คืน "08:00:00"
        const parts = t.split(':');
        if (parts.length === 2) return `${parts[0].padStart(2, '0')}:${parts[1].padStart(2, '0')}:00`;
        if (parts.length === 3) return `${parts[0].padStart(2, '0')}:${parts[1].padStart(2, '0')}:${parts[2].padStart(2, '0')}`;
        // ถ้าใส่มาเป็น "8" อื่น ๆ
        return `${t.padStart(2, '0')}:00:00`;
    }

    private formatDate(date: Date, timeHHmmss: string): string {
        const d = DateTime.fromJSDate(date).setZone('Asia/Bangkok');
        const ymd = d.toFormat('yyyy-LL-dd');
        return `${ymd} ${this.ensureHHMMSS(timeHHmmss)}`; // -> "2025-08-24 08:00:00"
    }

    /** ปุ่ม “ตอนนี้” สำหรับกรอกเวลาปัจจุบันอย่างรวดเร็ว */
    setNow(which: 'in' | 'out') {
        const now = new Date();
        const pad = (n: number) => `${n}`.padStart(2, '0');
        const t = `${pad(now.getHours())}:${pad(now.getMinutes())}:${pad(now.getSeconds())}`;
        this.form.patchValue({ [which === 'in' ? 'inTime' : 'outTime']: t });
    }
}
