<div class="w-full p-8 bg-white m-10 rounded-xl">
    <!-- Header แทน mat-dialog-title -->
    <div class="flex items-center justify-between mb-4">
        <div class="flex items-center gap-2">
            <mat-icon>schedule</mat-icon>
            <h2 class="text-xl font-semibold">บันทึกเวลาเข้างาน</h2>
        </div>

        <!-- ปุ่มย้อนกลับ -->
        <button mat-stroked-button (click)="onCancel()">
            <mat-icon>arrow_back</mat-icon>
            <span class="ml-2">กลับ</span>
        </button>
    </div>

    <!-- เนื้อหาเพจ แทน mat-dialog-content -->
    <form class="flex flex-col gap-4 pt-2" [formGroup]="form">

        <!-- LINE: ngx-mat-select-search -->
        <mat-form-field class="w-full">
            <mat-label>เลือกพนักงาน</mat-label>
            <mat-select formControlName="user_id">
                <mat-option>
                    <ngx-mat-select-search [formControl]="lineSearch"
                        placeholderLabel="ค้นหา..."></ngx-mat-select-search>
                </mat-option>
                <mat-option *ngFor="let l of filteredLines$ | async" [value]="l.id">{{ l.fullname }}</mat-option>
            </mat-select>
        </mat-form-field>

        <!-- วันที่ + โหมด -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <mat-form-field class="w-full">
                <mat-label>เลือกวันที่</mat-label>
                <input formControlName="time" matInput [matDatepicker]="picker_start_date">
                <mat-datepicker-toggle matSuffix [for]="picker_start_date"></mat-datepicker-toggle>
                <mat-datepicker #picker_start_date></mat-datepicker>
            </mat-form-field>

            <mat-form-field class="w-full">
                <mat-label>โหมดการบันทึก</mat-label>
                <mat-select formControlName="mode">
                    <mat-option value="IN">เข้าอย่างเดียว</mat-option>
                    <mat-option value="OUT">ออกอย่างเดียว</mat-option>
                    <mat-option value="BOTH">ทั้ง เข้า-ออก</mat-option>
                </mat-select>
            </mat-form-field>
        </div>

        <!-- เวลาแบบ fix แสดงให้เห็นชัดเจน -->
        <!-- เวลาแบบแก้ไขได้ -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <!-- เวลาเข้า -->
            <mat-form-field class="w-full" *ngIf="modeCtrl.value !== 'OUT'">
                <mat-label>เวลาเข้า</mat-label>
                <input matInput type="time" formControlName="inTime" step="1" />
                <button mat-icon-button matSuffix type="button" (click)="setNow('in')" matTooltip="ใช้เวลาปัจจุบัน">
                    <mat-icon>schedule</mat-icon>
                </button>
                <mat-hint>รูปแบบ HH:mm:ss</mat-hint>
            </mat-form-field>

            <!-- เวลาออก -->
            <mat-form-field class="w-full" *ngIf="modeCtrl.value !== 'IN'">
                <mat-label>เวลาออก</mat-label>
                <input matInput type="time" formControlName="outTime" step="1" />
                <button mat-icon-button matSuffix type="button" (click)="setNow('out')" matTooltip="ใช้เวลาปัจจุบัน">
                    <mat-icon>schedule</mat-icon>
                </button>
                <mat-hint>รูปแบบ HH:mm:ss</mat-hint>
            </mat-form-field>
        </div>
        <!-- แทน mat-dialog-actions: ปุ่ม action ด้านล่าง -->
        <div class="flex justify-center gap-2 pt-3">
            <button type="button" mat-flat-button
                class="bg-gray-700 text-gray-100 hover:bg-gray-600 rounded-lg py-2 px-4" (click)="onCancel()">
                <mat-icon svgIcon="heroicons_solid:x-mark"></mat-icon>
                <span class="ml-2 mr-1">ยกเลิก</span>
            </button>
            <button mat-flat-button (click)="onSave()" class="bg-green-600 hover:bg-green-700 text-white">
                <mat-icon class="mr-1">save</mat-icon> บันทึก
            </button>
        </div>
    </form>
</div>