<h2 mat-dialog-title class="text-xl font-semibold flex items-center gap-2">
    <mat-icon>schedule</mat-icon>
    บันทึกเวลาเข้างาน
</h2>

<div mat-dialog-content class="flex flex-col gap-4 pt-2" [formGroup]="form">
    <!-- LINE: ngx-mat-select-search -->
    <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
        <mat-label>เลือกไลน์พนักงาน</mat-label>
        <mat-select [formControlName]="'line_id'">
            <mat-option>
                <ngx-mat-select-search [formControl]="lineSearch"
                    placeholderLabel="ค้นหาไลน์..."></ngx-mat-select-search>
            </mat-option>
            <mat-option *ngFor="let l of filteredLines$ | async" [value]="l.line_id">{{ l.fullname }}</mat-option>
        </mat-select>
    </mat-form-field>
    <!-- วันที่ -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
            <mat-label>เลือกวันที่</mat-label>
            <input [formControlName]="'time'" matInput [matDatepicker]="picker_start_date">
            <mat-datepicker-toggle matSuffix [for]="picker_start_date"></mat-datepicker-toggle>
            <mat-datepicker #picker_start_date></mat-datepicker>
        </mat-form-field>
        <!-- โหมดการบันทึก -->
        <div class="flex flex-col">
            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                <mat-label>โหมดการบันทึก</mat-label>
                <mat-select [formControlName]="'mode'">
                    <mat-option value="IN">เข้าอย่างเดียว</mat-option>
                    <mat-option value="OUT">ออกอย่างเดียว</mat-option>
                    <mat-option value="BOTH">ทั้ง เข้า-ออก</mat-option>
                </mat-select>
            </mat-form-field>
        </div>

    </div>

    <!-- เวลาแบบ fix แสดงให้เห็นชัดเจน -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <mat-form-field [ngClass]="formFieldHelpers" class="w-full" *ngIf="mode.value !== 'OUT'">
            <mat-label>เวลาเข้า (fix)</mat-label>
            <input matInput [value]="IN_TIME" disabled>
            <mat-hint>ระบบจะบันทึกเป็น 08:00:00</mat-hint>
        </mat-form-field>

        <mat-form-field [ngClass]="formFieldHelpers" class="w-full" *ngIf="mode.value !== 'IN'">
            <mat-label>เวลาออก (fix)</mat-label>
            <input matInput [value]="OUT_TIME" disabled>
            <mat-hint>ระบบจะบันทึกเป็น 17:01:00</mat-hint>
        </mat-form-field>
    </div>

    <!-- NOTE: ฟิลด์ latitude/longitude/address/pic ไม่แสดง แต่ต้องถูก patch เข้ามาตอนเปิด dialog -->
    <div class="text-xs text-slate-500">
        * ระบบจะใช้ตำแหน่งปัจจุบัน/ที่อยู่/รูปภาพจากข้อมูลที่ส่งมาให้ Dialog ตอนเปิด (เพื่อผ่าน validation)
    </div>
</div>

<div mat-dialog-actions class="flex justify-end gap-2 pt-3">
    <button mat-flat-button
        class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
        (click)="onCancel()">
        <mat-icon svgIcon="heroicons_solid:x-mark"></mat-icon>
        <span class="ml-2 mr-1">ยกเลิก</span>
    </button>
    <button mat-flat-button class="bg-green-600 hover:bg-green-700 text-white" (click)="onSave()"
        [disabled]="submitting || form.invalid">
        <mat-icon class="mr-1">save</mat-icon> บันทึก
    </button>
</div>