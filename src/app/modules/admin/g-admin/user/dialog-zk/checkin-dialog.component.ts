// checkin-dialog.component.ts
import { Component, Inject, OnInit, signal } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule, Validators } from '@angular/forms';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatSelectModule } from '@angular/material/select';
import { MatInputModule } from '@angular/material/input';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { NgxMatSelectSearchModule } from 'ngx-mat-select-search';
import { Observable, map, startWith } from 'rxjs';
import { DateTime } from 'luxon';
import { TimeAttendanceService } from '../../report/time-attendance/time-attendance.service';

type CheckMode = 'IN' | 'OUT' | 'BOTH';

export interface CheckinDialogData {
    // ค่าที่ต้องการให้พร้อมในฟอร์ม เพื่อผ่าน validation
    latitude: number;
    longitude: number;
    address: string;
    pic: any; // base64 หรือไฟล์ ตามระบบจริง
    // รายการ line ให้เลือก
    lines: Array<{ line_id: string | string; code?: string; name?: string; fullname?: string }>;
    // default pre-select
    line_id?: number | string;
    date?: Date;
    mode?: CheckMode;
}
@Component({
    selector: 'app-checkin-dialog',
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        MatDialogModule,
        MatFormFieldModule,
        MatSelectModule,
        MatInputModule,
        MatButtonModule,
        MatIconModule,
        NgxMatSelectSearchModule,
        MatInputModule,
        MatDatepickerModule,
        MatNativeDateModule
    ],
    templateUrl: './checkin-dialog.component.html',
})
export class CheckinDialogComponent implements OnInit {
    
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    // ฟอร์มเต็มตามสเปกของคุณ
    form: FormGroup

    // โหมดการบันทึก
    mode = new FormControl<CheckMode>('IN', { nonNullable: true });

    // สำหรับค้นหาใน ngx-mat-select-search
    lineSearch = new FormControl<string>('');
    lines = signal<Array<{ line_id: string | string; fullname: string }>>([]);
    filteredLines$!: Observable<Array<{ line_id: string | string; fullname: string }>>;

    // เวลา fix
    readonly IN_TIME = '08:00:00';
    readonly OUT_TIME = '17:01:00';

    submitting = false;

    constructor(
        private fb: FormBuilder,
        private svc: TimeAttendanceService,
        private dialogRef: MatDialogRef<CheckinDialogComponent>,
        @Inject(MAT_DIALOG_DATA) public data: CheckinDialogData
    ) {
        // เตรียมรายการ line (ทำ label ให้สวยงาม)
        const mapped = (data?.lines ?? []).map(l => ({
            line_id: l.line_id,
            fullname: l.fullname ?? String(l.line_id),
        }));
        this.lines.set(mapped);
        this.form = this.fb.group({
            line_id: '',
            latitude: '',
            longitude: '',
            address: '',
            pic: '',
            time: '',
            mode: '',
        })
        // ตั้งโหมดเริ่มต้น
        this.mode.setValue(data?.mode ?? 'IN');

        // stream ค้นหา line
        this.filteredLines$ = this.lineSearch.valueChanges.pipe(
            startWith(''),
            map((kw) => {
                const k = (kw || '').toLowerCase().trim();
                const all = this.lines();
                if (!k) return all;
                return all.filter(x => x.fullname.toLowerCase().includes(k));
            })
        );
    }

    ngOnInit(): void {
        // patch ค่าที่เหลือเพื่อผ่าน validation
        this.form.patchValue({
            line_id: '',
            latitude: '',
            longitude: '',
            address: '',
            pic: '',
            time: DateTime.now().toFormat('yyyy-MM-dd'),
            mode : 'BOTH'
        });
    }

    // แปลงวันที่ + เวลาเป็น ISO/รูปแบบที่ API ต้องการ
    private buildDateTimeISO(baseDate: Date, timeHHmmss: string) {
        const [hh, mm, ss] = timeHHmmss.split(':').map(n => parseInt(n, 10));
        const dt = DateTime.fromJSDate(baseDate).set({ hour: hh, minute: mm, second: ss, millisecond: 0 });
        return {
            iso: dt.toISO(),                    // ISO String
            date: dt.toISODate(),               // YYYY-MM-DD
            time: dt.toFormat('HH:mm:ss'),      // HH:mm:ss
            js: dt.toJSDate(),
        };
        // ปรับให้เข้ากับรูปแบบ payload ของ API จริง
    }

    async onSave() {
        this.form.markAllAsTouched();
        if (this.form.invalid) return;

        this.submitting = true;

        const { line_id, latitude, longitude, address, pic, time } = this.form.value as {
            line_id: string | number;
            latitude: number;
            longitude: number;
            address: string;
            pic: any;
            time: Date;
        };

        const mode = this.mode.value;

        // เตรียม payload builder
        const buildPayload = (date: Date, type: 'IN' | 'OUT') => {
            const fixed = type === 'IN' ? this.IN_TIME : this.OUT_TIME;
            const dt = this.buildDateTimeISO(date, fixed);
            // ตัวอย่าง payload (แก้ให้เข้าระบบจริง)
            return {
                line_id,
                latitude,
                longitude,
                address,
                pic,
                // สมมติ API ต้องการ field เหล่านี้
                date: dt.date,
                time: dt.time,
                datetime_iso: dt.iso,
                type, // 'IN' หรือ 'OUT'
            };
        };

        try {
            if (mode === 'BOTH') {
                // ยิง 2 ครั้ง: เข้า + ออก
                const inPayload = buildPayload(time, 'IN');
                const outPayload = buildPayload(time, 'OUT');
                await this.svc.saveAttendance(inPayload).toPromise();
                await this.svc.saveAttendance(outPayload).toPromise();
            } else {
                // ยิงครั้งเดียว
                const payload = buildPayload(time, mode);
                await this.svc.saveAttendance(payload).toPromise();
            }

            this.dialogRef.close(true);
        } catch (e) {
            console.error(e);
            // ถ้าต้องการแจ้ง error ให้ใส่ MatSnackBar หรือ dialog error ได้
        } finally {
            this.submitting = false;
        }
    }

    onCancel() {
        this.dialogRef.close(false);
    }
}
