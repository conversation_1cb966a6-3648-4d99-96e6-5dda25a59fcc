import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import {
  debounceTime,
  map,
  merge,
  Observable,
  Subject,
  switchMap,
  takeUntil,
} from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { <PERSON><PERSON>orm<PERSON><PERSON>, MatLabel } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { MatRadioGroup, MatRadioButton } from '@angular/material/radio';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { BonusStepService } from '../bonus-step.services';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Inject } from '@angular/core';

@Component({
  selector: 'app-edit-bonus',
  templateUrl: './edit-bonus.component.html',
  styleUrl: './edit-bonus.component.scss',
  animations: fuseAnimations,
  imports: [FormsModule, ReactiveFormsModule, MatFormField, MatLabel, MatInput, MatRadioGroup, MatRadioButton, MatButton, MatIcon]
})
export class EditBonusComponent implements OnInit {
  formData: FormGroup;

  constructor(
    public dialogRef: MatDialogRef<EditBonusComponent>,
    private _changeDetectorRef: ChangeDetectorRef,
    private _fuseConfirmationService: FuseConfirmationService,
    private _formBuilder: FormBuilder,
    private _matDialog: MatDialog,
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
    private _authService: AuthService,
    private _Service: BonusStepService,
    @Inject(MAT_DIALOG_DATA) public data: any
  ) {
    this.formData = this._formBuilder.group({
      step: [null, Validators.required],
      amount: [null, Validators.required],
    });
  }

  ngOnInit(): void {
    this._Service.getBonusStepById(this.data.id)
      .subscribe((resp) => {
        this.formData.patchValue({
          step: resp.data.step,
          amount: resp.data.amount,
        })
      })
  }

  putBonusStep() {
    if (this.formData.invalid) {
      this._fuseConfirmationService.open({
        title: 'ข้อมูลไม่ครบถ้วน',
        message: 'กรุณากรอกข้อมูลให้ครบทุกช่องก่อนดำเนินการ',
        icon: {
          show: true,
          name: 'heroicons_outline:exclamation-circle',
          color: 'warn',
        },
        actions: {
          confirm: {
            show: true,
            label: 'ตกลง',
            color: 'warn',
          },
          cancel: {
            show: false,
            label: '',
          },
        },
        dismissible: true,
      });
      return; // หยุดไม่ให้ทำต่อ
    }
    const confirmation = this._fuseConfirmationService.open({
      title: 'แก้ไขเบี้ยขยันเก่า',
      message: 'คุณต้องการแก้ไขเบี้ยขยันเก่าใช่หรือไม่ ?',
      icon: {
        show: true,
        name: 'heroicons_outline:information-circle',
        color: 'info',
      },
      actions: {
        confirm: {
          show: true,
          label: 'ยืนยัน',
          color: 'primary',
        },
        cancel: {
          show: true,
          label: 'ยกเลิก',
        },
      },
      dismissible: true,
    });

    // Subscribe to the confirmation dialog closed action
    confirmation.afterClosed().subscribe((result) => {
      // If the confirm button pressed...
      if (result === 'confirmed') {
        this._Service.putBonusStep(this.formData.value, this.data.id).subscribe({
          next: (resp: any) => {
            this.dialogRef.close();
          },
          error: (err: any) => {
            this._fuseConfirmationService.open({
              title: 'เกิดข้อผิดพลาด',
              message: err.error.message,
              icon: {
                show: true,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
              },
              actions: {
                confirm: {
                  show: false,
                  label: 'ยืนยัน',
                  color: 'primary',
                },
                cancel: {
                  show: false,
                  label: 'ยกเลิก',
                },
              },
              dismissible: true,
            });
          },
        });
      }
    });
  }

  onClose(): void {
    this.dialogRef.close();
  }
}
