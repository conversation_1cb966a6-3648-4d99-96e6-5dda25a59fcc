import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import {
  debounceTime,
  map,
  merge,
  Observable,
  Subject,
  switchMap,
  takeUntil,
} from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { sortBy, startCase } from 'lodash-es';
// import { NewDepartmentComponent } from '../new-department/new-department.component';
// import { ImportOSMComponent } from '../card/import-osm/import-osm.component';
import { MatTableDataSource } from '@angular/material/table';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { NgIf, NgFor, DatePipe } from '@angular/common';
import { MatProgressBar } from '@angular/material/progress-bar';
import { MatAnchor, MatIconButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { NewBonusComponent } from './new-bonus/new-bonus.component';
import { BonusStepService } from './bonus-step.services';
import { EditBonusComponent } from './edit-bonus/edit-bonus.component';

@Component({
  selector: 'app-bonus-step',
  imports: [NgIf, MatProgressBar, MatAnchor, MatIcon, DataTablesModule, NgFor, MatIconButton, DatePipe],
  changeDetection: ChangeDetectionStrategy.OnPush,
  animations: fuseAnimations,
  templateUrl: './bonus-step.component.html',
  styleUrl: './bonus-step.component.scss'
})
export class BonusStepComponent implements OnInit {
  isLoading: boolean = false;
  dtOptions: DataTables.Settings = {};
  dataRow: any[] = [];

  @ViewChild(DataTableDirective)
  dtElement!: DataTableDirective;
  private destroy$ = new Subject<any>();
  private _unsubscribeAll: Subject<any> = new Subject<any>();

  constructor(
    private _changeDetectorRef: ChangeDetectorRef,
    private _fuseConfirmationService: FuseConfirmationService,
    private _formBuilder: FormBuilder,
    // private _Service: PermissionService,
    private _matDialog: MatDialog,
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
    private _authService: AuthService,
    private _Service: BonusStepService,
  ) { }

  ngOnInit(): void {
    this.loadTable();
  }

  ngAfterViewInit(): void { }

  ngOnDestroy(): void {
    // Unsubscribe from all subscriptions
    this._unsubscribeAll.next(null);
    this._unsubscribeAll.complete();
  }


  pages = { current_page: 1, last_page: 1, per_page: 10, begin: 0 };
  loadTable(): void {
    const that = this;
    this.dtOptions = {
      pagingType: 'full_numbers',
      pageLength: 10,
      serverSide: true,
      processing: true,
      language: {
        url: 'https://cdn.datatables.net/plug-ins/1.11.3/i18n/th.json',
      },
      ajax: (dataTablesParameters: any, callback) => {
        dataTablesParameters.item_type_id = 1;
        dataTablesParameters.branch_id = JSON.parse(localStorage.getItem('user') || '{}')?.branch_id;
        that._Service
          .getBonusSteps(dataTablesParameters)
          .subscribe((resp: any) => {
            this.dataRow = resp.data;
            this.pages.current_page = resp.current_page;
            this.pages.last_page = resp.last_page;
            this.pages.per_page = resp.per_page;
            if (resp.current_page > 1) {
              this.pages.begin =
                resp.per_page * resp.current_page - 1;
            } else {
              this.pages.begin = 0;
            }
            callback({
              recordsTotal: resp.total,
              recordsFiltered: resp.total,
              data: [],
            });
            this._changeDetectorRef.markForCheck();
          });
      },
      columns: [
        { data: 'actice', orderable: false },
        // { data: 'id' },
        // { data: 'name' },
        // { data: 'status' },
        // { data: 'create_by' },
        // { data: 'created_at' },
      ],
    };
  }

  rerender(): void {
    this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
      dtInstance.ajax.reload();
    });
  }

  New() {
    const dialogRef = this._matDialog.open(NewBonusComponent, {
      // width: '50%',
      // minHeight: 'calc(100vh - 90px)',
      // height: 'auto'
      width: '700px',
      height: '400px',
    });

    dialogRef.afterClosed().subscribe((item) => {
      this.rerender();
      this._changeDetectorRef.markForCheck();
    });
  }

  Edit(id: any) {
    const dialogRef = this._matDialog.open(EditBonusComponent, {
      // width: '50%',
      // minHeight: 'calc(100vh - 90px)',
      // height: 'auto'
      width: '700px',
      height: '400px',
      data: { id: id },
    });

    dialogRef.afterClosed().subscribe((item) => {
      this.rerender();
      this._changeDetectorRef.markForCheck();
    });
  }

  deleteBonusStep(id: any) {
    const confirmation = this._fuseConfirmationService.open({
      title: 'ยืนยันลบเบี้ยขยัน',
      message: 'คุณต้องการลบเบี้ยขยันนี้ใช่หรือไม่ ?',
      icon: {
        show: true,
        name: 'heroicons_outline:exclamation-triangle',
        color: 'warning',
      },
      actions: {
        confirm: {
          show: true,
          label: 'ยืนยัน',
          color: 'primary',
        },
        cancel: {
          show: true,
          label: 'ยกเลิก',
        },
      },
      dismissible: true,
    });

    // Subscribe to the confirmation dialog closed action
    confirmation.afterClosed().subscribe((result) => {
      // If the confirm button pressed...
      if (result === 'confirmed') {
        this._Service
          .deleteBonusStep(id)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: (resp: any) => {
              this.rerender();
            },
            error: (err: any) => {
              this._fuseConfirmationService.open({
                title: 'เกิดข้อผิดพลาด',
                message: err.error.message,
                icon: {
                  show: true,
                  name: 'heroicons_outline:exclamation-triangle',
                  color: 'warning',
                },
                actions: {
                  confirm: {
                    show: false,
                    label: 'ยืนยัน',
                    color: 'primary',
                  },
                  cancel: {
                    show: false,
                    label: 'ยกเลิก',
                  },
                },
                dismissible: true,
              });
            },
          });
      }
    });
  }

  putBonusStep() {

  }
}
