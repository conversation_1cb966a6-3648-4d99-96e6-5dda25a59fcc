import { ChangeDetectionStrategy, Component, ViewEncapsulation } from '@angular/core';
import { RouterOutlet } from '@angular/router';

@Component({
    selector: 'deletemoney-type',
    templateUrl: './deletemoney-type.component.html',
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [RouterOutlet]
})
export class DeletemoneyComponent {
  /**
   * Constructor
   */
  constructor() {
  }
}
