<div
    class="flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between p-1 sm:py-8 sm:px-10 border-b bg-card dark:bg-transparent">
    <div class="flex-1 min-w-0">
        <!-- Title -->
        <div class="mt-2">
            <h1 class="text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate"
                *ngIf="this.data?.type === 'NEW'">
                สร้างประเภทเงินหัก
            </h1>
            <h1 class="text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate"
                *ngIf="this.data?.type === 'EDIT'">
                แก้ไขประเภทเงินหัก
            </h1>
        </div>
    </div>
</div>
<div class="flex-auto p-3 sm:p-2">
    <form [formGroup]="formData">
        <div class="flex px-5 pt-5">
            <label for="name"
                class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4 text-center">ชื่อประเภทเงินหัก</label>
            <div class="mb-5 mt-2 w-2/3">
                <input id="name" type="text"
                    class=" text-gray-600 focus:outline-none focus:border focus:border-yellow-600 font-normal w-full h-10 flex items-center pl-3 text-sm border-gray-300 rounded border"
                    placeholder="ดวงใจ ใจดี" formControlName="name" />
            </div>
        </div>
        <div class="flex pr-5 pl-24 pb-5 items-center">
            <label for="name"
                class="text-gray-500 text-md leading-tight tracking-normal w-[25%] text-center">ประเภทที่แนะนำ</label>
            <div class="flex gap-1">
                <span *ngFor="let item of recommendName" class="text-md bg-gray-200 rounded-lg p-2 hover:cursor-pointer hover:bg-gray-300"
                    (click)="this.formData.get('name').setValue(item)">
                    {{ item }}
                </span>
            </div>
        </div>
        <div class="flex p-5">
            <label for="name"
                class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4 text-center">ประเภท</label>
            <mat-form-field appearance="fill" class="w-2/3 pr-2 ">
                <mat-select [formControlName]="'type'">
                    <!-- <mat-option  [value]="">
                            เลือกประเภท
                        </mat-option> -->
                    <mat-option value="Once">
                        รายครั้ง
                    </mat-option>
                    <mat-option value="All Month">
                        เงินหักประจำ
                    </mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        <div class="flex p-5">
            <label for="name"
                class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4  text-center">รายละเอียด</label>
            <textarea id="name" rows="4"
                class=" p-2.5 mb-5 mt-2 text-gray-600
                focus:outline-none focus:border focus:border-yellow-600 font-normal w-2/3  flex items-center pl-3 text-sm border-gray-300 rounded border"
                placeholder="รายละเอียด" formControlName="description"></textarea>
        </div>
        <div class="flex p-5">
            <label for="name"
                class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4  text-center">สถานะ</label>
            <mat-radio-group [formControlName]="'view_in_slip'">
                <mat-radio-button [value]="1">
                    <p class="text-lg font-semibold">เปิด</p>
                </mat-radio-button>
                <mat-radio-button [value]="0" class="pl-2">
                    <p class="text-lg font-semibold">ปิด</p>
                </mat-radio-button>
            </mat-radio-group>
        </div>
        <div class="flex items-center justify-end w-full border-t px-8 py-4">
            <div class="flex items-center justify-end">
                <button mat-flat-button
                    class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                    (click)="onClose()">
                    <mat-icon svgIcon="heroicons_solid:x-mark" class="mr-2"></mat-icon>
                    ยกเลิก
                </button>
                <button class="px-6 ml-3 mat-primary" mat-flat-button (click)="newItemType()">
                    <mat-icon svgIcon="heroicons_solid:check"></mat-icon>
                    ยืนยัน
                </button>
            </div>
        </div>
    </form>
</div>