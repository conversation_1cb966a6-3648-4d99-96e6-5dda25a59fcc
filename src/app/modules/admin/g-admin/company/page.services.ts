import { Http<PERSON>lient, HttpRequest, HttpHandler, HttpEvent, HttpHeaders, HttpInterceptor } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import {
    BehaviorSubject,
    filter,
    map,
    Observable,
    of,
    switchMap,
    take,
    tap,
    throwError,
} from 'rxjs';
import {
    AssetItem,
    Store,
    AssetType,
    Chat,
    // PermissionProductDetailOSM,
    CustomerPagination,
    CustomerProduct,
    StoreType,
    AssetSize,
    Supplier,
    Division,
    DataCustomer,
} from './page.types';
import { environment } from 'environments/environment';
import { AssetCategory } from 'app/shared/asset-category';
import { DataTablesResponse } from 'app/shared/datatable.types';
import { material } from 'app/mock-api/ui/icons/data';
// import { UserDetail } from '../user/user.types';
const token = localStorage.getItem('accessToken') || null;
@Injectable({
    providedIn: 'root',
})

export class BonusStepService {
    // Private
    private _pagination: BehaviorSubject<CustomerPagination | null> =
        new BehaviorSubject(null);
    private _product: BehaviorSubject<CustomerProduct | null> =
        new BehaviorSubject(null);
    private _products: BehaviorSubject<CustomerProduct[] | null> =
        new BehaviorSubject(null);
    private _product_osm: BehaviorSubject<CustomerProduct | null> =
        new BehaviorSubject(null);
    private _products_osm: BehaviorSubject<CustomerProduct[] | null> =
        new BehaviorSubject(null);
    private _chat: BehaviorSubject<Chat> = new BehaviorSubject(null);
    private _chats: BehaviorSubject<Chat[]> = new BehaviorSubject(null);
    private _asset_types: BehaviorSubject<AssetType[] | null> =
        new BehaviorSubject(null);
    // private _suppliers: BehaviorSubject<UserDetail[] | null> = new BehaviorSubject(null);
    // private _two_approvers: BehaviorSubject<UserDetail[] | null> = new BehaviorSubject(null);
    private _store_types: BehaviorSubject<StoreType[] | null> =
        new BehaviorSubject(null);
    private _stores: BehaviorSubject<Store[] | null> = new BehaviorSubject(
        null
    );
    private _seasons: BehaviorSubject<any[] | null> = new BehaviorSubject(null);
    private _asset_sizes: BehaviorSubject<any[] | null> = new BehaviorSubject(
        null
    );
    private _divisions: BehaviorSubject<any[] | null> = new BehaviorSubject(
        null
    );
    private _materials: BehaviorSubject<any[] | null> = new BehaviorSubject(
        null
    );
    /**
     * Constructor
     */
    constructor(private _httpClient: HttpClient) { }

    httpOptionsFormdata = {
        headers: new HttpHeaders({ Authorization: `Bearer ${token}` }),
    };

    // -----------------------------------------------------------------------------------------------------
    // @ Accessors
    // -----------------------------------------------------------------------------------------------------

    /**
     * Getter for pagination
     */
    get pagination$(): Observable<CustomerPagination> {
        return this._pagination.asObservable();
    }

    /**
     * Getter for product
     */
    get product$(): Observable<CustomerProduct> {
        return this._product.asObservable();
    }

    /**
     * Getter for products
     */
    get products$(): Observable<CustomerProduct[]> {
        return this._products.asObservable();
    }

    /**
     * Getter for product
     */
    get product_osm$(): Observable<CustomerProduct> {
        return this._product_osm.asObservable();
    }

    /**
     * Getter for products
     */
    get products_osm$(): Observable<any[]> {
        return this._products_osm.asObservable();
    }

    /**
     * Getter for chat
     */
    get chat$(): Observable<Chat> {
        return this._chat.asObservable();
    }

    /**
     * Getter for chats
     */
    get chats$(): Observable<Chat[]> {
        return this._chats.asObservable();
    }

    /**
     * Getter for tags
     */
    // get suppliers$(): Observable<UserDetail[]> {
    //     return this._suppliers.asObservable();
    // }

    // /**
    //     * Getter for tags
    //     */
    // get two_approvers$(): Observable<UserDetail[]> {
    //     return this._two_approvers.asObservable();
    // }

    /**
     * Getter for asset type
     */
    get asset_types$(): Observable<AssetType[]> {
        return this._asset_types.asObservable();
    }

    /**
     * Getter for store type
     */
    get store_types$(): Observable<StoreType[]> {
        return this._store_types.asObservable();
    }

    /**
     * Getter for store type
     */
    get stores$(): Observable<Store[]> {
        return this._stores.asObservable();
    }

    /**
     * Getter for season
     */
    get seasons$(): Observable<any[]> {
        return this._seasons.asObservable();
    }

    /**
     * Getter for division
     */
    get divisions$(): Observable<any[]> {
        return this._divisions.asObservable();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------


    handlerError(error): Observable<never> {
        let errorMessage = 'Error unknown';
        if (error) {
            errorMessage = `${error.error.message}`;
        }
        // window.alert(errorMessage);
        return throwError(errorMessage);
    }


    getBonusSteps(dataTablesParameters: any): Observable<any[]> {
        return this._httpClient
            .post(
                environment.API_URL + 'api/company_page',
                dataTablesParameters,
                this.httpOptionsFormdata
            )
            .pipe(
                switchMap((response: any) => {
                    return of(response.data);
                })
            );
    }

    postBonusStep(data: any): Observable<any[]> {
        return this._httpClient
            .post(
                environment.API_URL + 'api/company',
                data,
                this.httpOptionsFormdata
            )
            .pipe(
                switchMap((response: any) => {
                    return of(response);
                })
            );
    }

    deleteBonusStep(itemId: any): Observable<any[]> {
        return this._httpClient
            .delete<any>(
                environment.API_URL + `api/company/${itemId}`,
                this.httpOptionsFormdata
            )
            .pipe(
                map((mtplan) => {
                    return mtplan;
                }),
                catchError((err) => this.handlerError(err))
            );
    }
    putBonusStep(data: any, itemId: any): Observable<any[]> {
        return this._httpClient
            .put(
                environment.API_URL + `api/company/${itemId}`,
                data,
                this.httpOptionsFormdata
            )
            .pipe(
                switchMap((response: any) => {
                    // Return a new observable with the response
                    return of(response);
                })
            );
    }

    getBonusStepById(itemId: any): Observable<any> {
        return this._httpClient
            .get(environment.API_URL + `api/company/${itemId}`)
            .pipe(
                map((resp: any) => {
                    return resp;
                })
            );
    }
}
