<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">

    <!-- Header -->
    <div class="relative flex flex-col flex-0 py-8 px-6 md:px-8">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <div class="flex md:flex-row flex-col justify-between">
            <!-- Title -->
            <div class="text-4xl font-extrabold tracking-tight">เงินหัก</div>
            <!-- Actions -->
            <div class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4 gap-2">
                <!-- Add product button -->
                <button mat-flat-button
                    class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                    (click)="this.rerender()">
                    <mat-icon [svgIcon]="'heroicons_outline:magnifying-glass'"></mat-icon>
                    <span class="ml-2 mr-1">ค้นหา</span>
                </button>
                <button mat-flat-button
                    class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                    (click)="clearData()">
                    <mat-icon [svgIcon]="'heroicons_outline:arrow-path'"></mat-icon>
                    <span class="ml-2 mr-1">ล้าง</span>
                </button>
                <a (click)="New()" class="" mat-flat-button [color]="'primary'" *ngIf="!hiddenSave()">
                    <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                    <span class="ml-2 mr-1">สร้างเงินหัก</span>
                </a>
            </div>
        </div>
        <form [formGroup]="form">
            <div class="border-b flex flex-col md:flex-row gap-4 w-full p-4">
                <div class="flex flex-col w-full">
                    <mat-label for="name"
                        class="text-gray-800 text-lg font-bold leading-tight tracking-normal mb-1">เลือกพนักงาน</mat-label>
                    <ng-container *ngIf="UserList">
                        <app-personnel-autocomplete [itemtypeData]="UserList"
                            (personnelSelected)="onPersonnelSelected($event)">
                        </app-personnel-autocomplete>
                    </ng-container>
                </div>
                <div class="flex flex-col w-full">
                    <mat-form-field class="w-full">
                        <mat-label for="name"
                            class="text-gray-800 text-lg font-bold leading-tight tracking-normal">เลือกปี</mat-label>
                        <mat-select [formControlName]="'year'">
                            <mat-option *ngFor="let item of years" [value]="item">
                                {{item}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                <div class="flex flex-col w-full">
                    <mat-form-field class="w-full">
                        <mat-label for="name"
                            class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">เลือกเดือน</mat-label>
                        <mat-select [formControlName]="'month'">
                            <mat-option *ngFor="let item of months" [value]="item.key">
                                {{item.name}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>

            </div>
        </form>
    </div>

    <!-- Main -->
    <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

        <div class="flex flex-col p-3 sm:p-6">
            <div class="overflow-auto  bg-white shadow sm:rounded-lg">
                <!-- Products list -->

                <div class="flex flex-col flex-auto p-5 sm:overflow-auto overflow-x-scroll">
                    <table datatable [dtOptions]="dtOptions"
                        class="table w-full text-left text-gray-500 overflow-hidden">
                        <thead class="bg-gray-300 text-black">
                            <tr>
                                <th>จัดการ</th>
                                <th>ลำดับ</th>
                                <th>ชื่อ-สกุล</th>
                                <th>ประเภทเงินหัก</th>
                                <th>สาเหตุ</th>
                                <th>จำนวนเงิน</th>
                                <th>สร้างโดย</th>
                                <th>วันที่สร้าง</th>
                            </tr>
                        </thead>
                        <tbody *ngIf="dataRow?.length != 0">
                            <tr *ngFor="let item of dataRow; let i = index"
                                class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-md">
                                <td>
                                    <button mat-icon-button (click)="Delete(item.id)" title="ลบข้อมูล"
                                        [disabled]="hiddenDelete()">
                                        <mat-icon svgIcon="mat_solid:delete"></mat-icon>
                                    </button>
                                </td>
                                <td>{{ pages.begin + (i + 1) }}</td>
                                <td style="min-width: 150px;">{{ item.user?.first_name }} {{ item.user?.last_name }}
                                </td>
                                <td style="min-width: 150px;">{{ item.income_type[0].name }}</td>
                                <td>{{ item.description}}</td>
                                <td>
                                    <div class="text-red-700">{{ item.price}}</div>
                                </td>
                                <td style="min-width: 150px;">{{ item.create?.first_name}} {{ item.create?.last_name}}
                                </td>
                                <td style="min-width: 120px;">{{ item.created_at | date: 'dd/MM/yyyy HH:mm' }}</td>
                            </tr>
                        </tbody>
                        <tbody *ngIf="dataRow?.length == 0">
                            <tr>
                                <td colspan="8" class="no-data-available text-center">ไม่มีข้อมูล !</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

</div>