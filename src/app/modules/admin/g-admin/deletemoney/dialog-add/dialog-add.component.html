<div class="bg-white dark:bg-gray-900  overflow-hidden">
    <!-- Header Section -->
    <div
        class="flex flex-col sm:flex-row items-start sm:items-center justify-between px-6 py-4 sm:px-8 sm:py-6 border-b border-gray-200 dark:border-gray-700">
        <div class="flex-1 min-w-0">
            <h1 class="text-2xl sm:text-3xl font-bold text-gray-800 dark:text-gray-100 tracking-tight">
                เพิ่มเงินหัก
            </h1>
        </div>
    </div>

    <!-- Form Section -->
    <div class="p-6 sm:p-8">
        <form [formGroup]="formData" class="space-y-6">
            <!-- Deduction Type Field -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-start">
                <div class="md:col-span-1">
                    <label for="deduct_type_id" class="block text-lg font-medium text-gray-700 dark:text-gray-300 mb-1">
                        ประเภทเงินหัก
                    </label>
                    <p class="text-sm text-gray-500 dark:text-gray-400 ">
                        เลือกประเภทเงินหักจากรายการ
                    </p>
                </div>
                <div class="md:col-span-2">
                    <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                        <!-- <mat-label>เลือกประเภทเงินหัก</mat-label> -->
                        <mat-select [formControlName]="'deduct_type_id'">
                            <mat-option *ngFor="let item of TypeList" [value]="item.id">
                                {{item.name}}
                            </mat-option>
                        </mat-select>
                        <mat-error *ngIf="formData.get('deduct_type_id').invalid">
                            กรุณาเลือกประเภทเงินหัก
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>

            <!-- Amount Field -->
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4 items-start">
                <div class="md:col-span-1">
                    <label for="price" class="block text-lg font-medium text-gray-700 dark:text-gray-300 mb-1">
                        จำนวนเงิน
                    </label>
                    <p class="text-sm text-gray-500 dark:text-gray-400">
                        กรอกจำนวนเงินที่ต้องการหัก
                    </p>
                </div>
                <div class="md:col-span-2">
                    <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                        <!-- <mat-label>จำนวนเงิน</mat-label> -->
                        <input matInput id="price" type="number" min="0" placeholder="0.00" formControlName="price"
                            class="text-right">
                        <mat-error *ngIf="formData.get('price').invalid">
                            กรุณากรอกจำนวนเงินที่ถูกต้อง
                        </mat-error>
                    </mat-form-field>
                </div>
            </div>

            <!-- Action Buttons -->
            <div class="flex justify-end space-x-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                <button mat-flat-button
                    class="bg-gray-600 hover:bg-gray-700 dark:bg-gray-700 dark:hover:bg-gray-800"
                    (click)="onClose()">
                    <mat-icon svgIcon="heroicons_solid:x-mark" class="mr-2"></mat-icon>
                    ยกเลิก
                </button>
                <button type="submit" mat-flat-button
                    class="bg-blue-600 hover:bg-blue-700 dark:bg-blue-700 dark:hover:bg-blue-800"
                    (click)="newItemType()">
                    <mat-icon svgIcon="heroicons_solid:check" class="mr-2"></mat-icon>
                    ยืนยัน
                </button>
              
            </div>
        </form>
    </div>
</div>