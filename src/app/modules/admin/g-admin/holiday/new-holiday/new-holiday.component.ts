import { AfterViewInit, ChangeDetectionStrategy, ChangeDetectorRef, Component, Inject, OnDestroy, OnInit, ViewChild, ViewEncapsulation } from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { debounceTime, map, merge, Observable, Subject, switchMap, takeUntil } from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MAT_DIALOG_DATA, MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { sortBy, startCase } from 'lodash-es';
import { AssetType, DeliveryPagination } from '../holiday.types';
import { DeliveryService } from '../holiday.service';
import moment from 'moment';
import { CommonModule, NgIf } from '@angular/common';
import { MatFormField, MatFormFieldModule, MatSuffix } from '@angular/material/form-field';
import { MatInput, MatInputModule } from '@angular/material/input';
import { MatDatepickerInput, MatDatepickerToggle, MatDatepicker } from '@angular/material/datepicker';
import { MatButton, MatButtonModule } from '@angular/material/button';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { sharedImports } from 'app/shared-imports';
// import { ImportOSMComponent } from '../card/import-osm/import-osm.component';

@Component({
    selector: 'app-new-holiday',
    templateUrl: './new-holiday.component.html',
    styleUrls: ['./new-holiday.component.scss'],
    animations: fuseAnimations,
    standalone: true,
    imports: [
        CommonModule,
        FormsModule,
        ReactiveFormsModule,
        ...sharedImports
    ]
})

export class NewHolidaymasterComponent implements OnInit, AfterViewInit, OnDestroy {

    vacationDays = Array.from({ length: 20 }, (_, i) => ({
        value: i + 1,
        name: `วันพักร้อนที่ ${i + 1}`
    }));
    formGroup: FormGroup;
    editid: any
    type: any[] = [
        { value: 'holiday', name: 'วันหยุดตามประเพณี' },
        { value: 'off', name: 'วันหยุดพักร้อน' },
    ]

    /**
     * Constructor
     */
    constructor(
        @Inject(MAT_DIALOG_DATA) private _data,
        public dailogRef: MatDialogRef<NewHolidaymasterComponent>,
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
        private _Service: DeliveryService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService,
    ) {
        this.editid = _data?.id
        this.formGroup = this._formBuilder.group({
            name: '',
            date: '',
            type: 'holiday',
            status: true,
        })

    }


    ngOnInit(): void {
        if (this.editid) {
            this._Service.getholidaybyid(this.editid).subscribe((resp: any) => {
                this.formGroup.patchValue({
                    ...resp.data,
                })
            })
        }

    }

    onClose() {
        this.dailogRef.close();
    }



    ngAfterViewInit(): void {
    }


    ngOnDestroy(): void {
    }


    Submit() {
        if (this.editid) {
            const confirmation = this._fuseConfirmationService.open({
                title: 'ยืนยันการแก้ไขข้อมูลวันหยุด',
                message: 'คุณต้องการแก้ไขข้อมูลใช่หรือไม่ ?',
                icon: {
                    show: true,
                    name: 'heroicons_outline:exclamation-triangle',
                    color: 'warning',
                },
                actions: {
                    confirm: {
                        show: true,
                        label: 'ยืนยัน',
                        color: 'primary',
                    },
                    cancel: {
                        show: true,
                        label: 'ยกเลิก',
                    },
                },
                dismissible: true,
            });
            confirmation.afterClosed().subscribe((result) => {
                if (result === 'confirmed') {
                    let formValue = this.formGroup.value;
                    const date = moment(formValue.date);
                    formValue.date = date.format('YYYY-MM-DD');
                    this._Service.updateholiday(this.editid, this.formGroup.value).subscribe((resp: any) => {
                        this.dailogRef.close()
                    })
                }
            });
        }
        else {
            const confirmation = this._fuseConfirmationService.open({
                title: 'ยืนยันการเพิ่มข้อมูลวันหยุด',
                message: 'คุณต้องการเพิ่มข้อมูลใช่หรือไม่ ?',
                icon: {
                    show: true,
                    name: 'heroicons_outline:exclamation-triangle',
                    color: 'warning',
                },
                actions: {
                    confirm: {
                        show: true,
                        label: 'ยืนยัน',
                        color: 'primary',
                    },
                    cancel: {
                        show: true,
                        label: 'ยกเลิก',
                    },
                },
                dismissible: true,
            });
            confirmation.afterClosed().subscribe((result) => {
                // If the confirm button pressed...
                if (result === 'confirmed') {
                    let formValue = this.formGroup.value;
                    const date = moment(formValue.date);
                    formValue.date = date.format('YYYY-MM-DD');
                    this._Service.newholiday(this.formGroup.value).subscribe((resp: any) => {
                        this.dailogRef.close()
                    })
                }
            });
        }

    }





}
