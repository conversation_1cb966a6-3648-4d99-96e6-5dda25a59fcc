/* ปรับปรุงการจัดรูปแบบตาราง */
table {
  border-collapse: collapse;
  width: 100%;
}

th,
td {
  padding: 12px;
  text-align: center;
}

th {
  font-weight: bold;
  border-bottom: 2px solid #ddd;
}

td {
  border-bottom: 1px solid #ddd;
}

.table td {
  vertical-align: middle;
  /* จัดตำแหน่งในแนวตั้งให้กลาง */
}

.table img {
  display: block;
  /* ทำให้รูปภาพเป็น block element */
  margin: 0 auto;
  /* จัดตำแหน่งแนวนอนให้กลาง */
}

::ng-deep .full-screen-dialog .mat-dialog-container {
  padding: 0 !important;
  border-radius: 0;
}

::ng-deep .mat-tab-group .mat-tab-header .mat-tab-label-container .mat-tab-list .mat-tab-labels .mat-tab-label.mat-tab-label-active {
  background-color: #ffffff !important;
  border-top: 2px solid red !important;
  color: red !important
}

::ng-deep .mat-tab-group .mat-tab-header .mat-tab-label-container .mat-tab-list .mat-tab-labels .mat-tab-label {
  background-color: #ccc !important;
  border-radius: 0px !important;
}