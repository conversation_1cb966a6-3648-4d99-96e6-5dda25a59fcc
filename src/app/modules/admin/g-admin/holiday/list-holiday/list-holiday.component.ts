import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    OnDestroy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import {
    Observable,
    Subject,
    takeUntil,
} from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { startCase } from 'lodash-es';
import { AssetType, DeliveryPagination, DataDelivery } from '../holiday.types';
import { DeliveryService } from '../holiday.service';
import { MatTableDataSource } from '@angular/material/table';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { NewHolidaymasterComponent } from '../new-holiday/new-holiday.component';
import { CommonModule } from '@angular/common';
import { MatTabChangeEvent, MatTabsModule } from '@angular/material/tabs';
import { BreakpointObserver, Breakpoints } from '@angular/cdk/layout';
import { BuddhistDatePipe } from 'app/modules/admin/pipes/buddhist-date.pipe';
import { sharedImports } from 'app/shared-imports';
@Component({
    selector: 'app-list-holiday',
    templateUrl: './list-holiday.component.html',
    styleUrls: ['./list-holiday.component.scss'],
    animations: fuseAnimations,
    standalone: true,
    imports: [
        CommonModule,
        MatTabsModule,
        FormsModule,
        ReactiveFormsModule,
        DataTablesModule,
        ...sharedImports,
        BuddhistDatePipe
    ]
})
export class ListHolidayComponent implements OnInit, AfterViewInit, OnDestroy {
    private destroy$ = new Subject<any>();
    @ViewChild(DataTableDirective)
    dtElement!: DataTableDirective;
    public dtOptions: DataTables.Settings = {};
    public dataRow: any[];


    // dataRow: any = []
    @ViewChild(MatPaginator) _paginator: MatPaginator;
    @ViewChild(MatSort) private _sort: MatSort;
    displayedColumns: string[] = [
        'id',
        'name',
        'status',
        'create_by',
        'created_at',
        'actions',
    ];
    dataSource: MatTableDataSource<DataDelivery>;

    products$: Observable<any>;
    asset_types: AssetType[];
    flashMessage: 'success' | 'error' | null = null;
    isLoading: boolean = false;
    searchInputControl: FormControl = new FormControl();
    selectedProduct: any | null = null;
    filterForm: FormGroup;
    tagsEditMode: boolean = false;
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    env_path = environment.API_URL;
    searchQuery: string = '';
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    me: any | null;
    get roleType(): string {
        return 'marketing';
    }

    status: any[] = [
        { value: 'holiday', name: 'วันหยุดตามประเพณี' },
        { value: 'off', name: 'วันหยุดพักร้อน' },
    ]

    supplierId: string | null;
    pagination: DeliveryPagination;
    years: any[] = [];
    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
        private _Service: DeliveryService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService,
        private _breakpointObserver: BreakpointObserver
    ) {

        this.filterForm = this._formBuilder.group({
            year: null,
            status: 'holiday'
        })
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        this.loadTable()
        const currentYear = new Date().getFullYear(); // ดึงปีปัจจุบัน
        const startYear = currentYear - 1; // ปีเริ่มต้น
        const endYear = currentYear + 1; // ปีสิ้นสุด

        this.years = [];

        for (let year = startYear; year <= endYear; year++) {
            this.years.push({
                value: year,         // ปี ค.ศ.
                year: year + 543     // ปี พ.ศ.
            });
        }


        // ตั้งค่าเริ่มต้นเป็นปีปัจจุบัน
        this.filterForm.patchValue({
            year: currentYear
        })
    }
    applySearch() {
        // You may need to modify this based on your DataTables structure
        this.rerender()
    }
    /**
     * After view init
     */
    ngAfterViewInit(): void { }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    resetForm(): void {
        this.filterForm.reset();
        this.filterForm.get('asset_type').setValue('default');
        this._changeDetectorRef.markForCheck();
    }

    /**
     * Close the details
     */
    closeDetails(): void {
        this.selectedProduct = null;
    }
    hiddenEdit() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 1);
        return menu.edit == 0;
    }
    hiddenDelete() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 1);
        return menu.delete == 0;
    }
    hiddenSave() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 1);
        return menu.save == 0;
    }
    /**
     * Show flash message
     */
    showFlashMessage(type: 'success' | 'error'): void {
        // Show the message
        this.flashMessage = type;

        // Mark for check
        this._changeDetectorRef.markForCheck();

        // Hide it after 3 seconds
        setTimeout(() => {
            this.flashMessage = null;

            // Mark for check
            this._changeDetectorRef.markForCheck();
        }, 3000);
    }

    callDetail(productId: string): void {
        // alert(this.selectedProduct.id);
        // // If the product is already selected...
        // if (this.selectedProduct && this.selectedProduct.id === productId) {
        //     // Close the details
        //     // this.closeDetails();
        //     return;
        // }

        this._router.navigate(['marketing/brief-plan/' + productId]);
    }

    edit(deliveryId: string): void {
        this._router.navigate(['delivery/edit/' + deliveryId]);
    }

    openNewBrief(): void {
        this._router.navigateByUrl('marketing/brief-plan/brief/create');
    }

    openNewOrder(productId: string): void {
        // If the product is already selected...
        // if (this.selectedProduct && this.selectedProduct.id === productId) {
        //     // Close the details
        //     // this.closeDetails();
        //     return;
        // }

        this._router.navigate([
            'marketing/data/assets-list/new-order/' + productId,
        ]);
    }

    textStatus(status: string): string {
        return startCase(status);
    }

    // openImportOsm(): void {
    //     this._matDialog.open(ImportOSMComponent)
    // }

    pages = { current_page: 1, last_page: 1, per_page: 10, begin: 0 };

    loadTable(): void {
        const that = this;
        this.dtOptions = {
            pagingType: 'full_numbers',
            pageLength: 50,
            serverSide: true,
            processing: true,
            searching: false,
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.11.3/i18n/th.json',
            },
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.search = { value: this.searchQuery };
                dataTablesParameters.year = this.filterForm.value.year;
                dataTablesParameters.type = this.filterForm.value.status;
                that._Service
                    .getholidayPage(dataTablesParameters)
                    .subscribe((resp) => {
                        this.dataRow = resp.data.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime());

                        this.pages.current_page = resp.current_page;
                        this.pages.last_page = resp.last_page;
                        this.pages.per_page = resp.per_page;
                        if (resp.current_page > 1) {
                            this.pages.begin =
                                resp.per_page * resp.current_page - 1;
                        } else {
                            this.pages.begin = 0;
                        }
                        callback({
                            recordsTotal: resp.total,
                            recordsFiltered: resp.total,
                            data: [],
                        });

                        this._changeDetectorRef.markForCheck();
                    });
            },
            columns: [
                { data: 'actice', orderable: false },
                { data: 'id' },
                { data: 'name' },
                { data: 'date' },
                { data: 'status' },
                { data: 'create_by' },
                { data: 'created_at' },
            ],
        };
        $('tr').even().removeClass('odd');
    }

    New() {
        let dialogConfig: any = {
            width: '800px',
            height: '530px',
        };

        if (this._breakpointObserver.isMatched(Breakpoints.Handset)) {
            dialogConfig = {
                width: '95vw',
                height: '95vh',
                maxWidth: '100vw',
                panelClass: 'full-screen-dialog'
            };
        }

        const dialogRef = this._matDialog.open(NewHolidaymasterComponent, dialogConfig);

        dialogRef.afterClosed().subscribe(() => {
            this.rerender();
            this._changeDetectorRef.markForCheck();
        });
    }


    Edit(id: any) {
        const dialogRef = this._matDialog.open(NewHolidaymasterComponent, {
            width: '800px',
            height: '530px',
            data: {
                id: id,
            },
        });
        dialogRef.afterClosed().subscribe((item) => {
            this.rerender();
            this._changeDetectorRef.markForCheck();
        });
    }
    rerender(): void {
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            dtInstance.ajax.reload();
        });
    }

    Delete(id) {
        const confirmation = this._fuseConfirmationService.open({
            title: 'ยืนยันลบข้อมูลวันหยุด',
            message: 'คุณต้องการลบข้อมูลวันหยุดใช่หรือไม่ ?',
            icon: {
                show: true,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
            },
            actions: {
                confirm: {
                    show: true,
                    label: 'ยืนยัน',
                    color: 'primary',
                },
                cancel: {
                    show: true,
                    label: 'ยกเลิก',
                },
            },
            dismissible: true,
        });

        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            // If the confirm button pressed...
            if (result === 'confirmed') {
                this._Service
                    .delete(id)
                    .pipe(takeUntil(this.destroy$))
                    .subscribe((res: any) => {
                        if (res.code == 201) {
                            this._fuseConfirmationService
                                .open({
                                    title: 'ลบข้อมูลวันหยุด',
                                    message: 'บันทึกเรียบร้อย',
                                    icon: {
                                        show: true,
                                        name: 'heroicons_outline:check-circle',
                                        color: 'success',
                                    },
                                    actions: {
                                        confirm: {
                                            show: false,
                                            label: 'ตกลง',
                                            color: 'primary',
                                        },
                                        cancel: {
                                            show: false,
                                            label: 'ยกเลิก',
                                        },
                                    },
                                    dismissible: true,
                                })
                                .afterClosed()
                                .subscribe((res) => {
                                    this.rerender();
                                });
                        }
                    });
            }
        });
    }

    onTabChange(event: MatTabChangeEvent): void {
        const selectedStatus = this.status[event.index].value;
        this.filterForm.patchValue({ status: selectedStatus });
        this.rerender();
    }
}
