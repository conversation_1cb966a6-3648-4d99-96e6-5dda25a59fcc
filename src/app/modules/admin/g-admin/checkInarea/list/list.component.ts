import { data } from 'jquery';
import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    OnDestroy,
    OnInit,
    ViewChild,
    ViewEncapsulation,
} from '@angular/core';
import {
    FormBuilder,
    FormControl,
    FormGroup,
    Validators,
} from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import {
    debounceTime,
    map,
    merge,
    Observable,
    Subject,
    switchMap,
    takeUntil,
} from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { sortBy, startCase } from 'lodash-es';
import { AssetType, DataPosition, PositionPagination } from '../area.types';
import { AreaService } from '../area.service';
// import { NewDepartmentComponent } from '../new-department/new-department.component';
// import { ImportOSMComponent } from '../card/import-osm/import-osm.component';
import { MatTableDataSource } from '@angular/material/table';
import { EditAreaComponent } from '../edit-area/edit-area.component';
import { NewAreaComponent } from '../new-area/new-area.component';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { NgIf, NgFor, DatePipe } from '@angular/common';
import { MatProgressBar } from '@angular/material/progress-bar';
import { MatAnchor, MatIconButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
@Component({
    selector: 'area-list',
    templateUrl: './list.component.html',
    styleUrls: ['./list.component.scss'],
    changeDetection: ChangeDetectionStrategy.OnPush,
    animations: fuseAnimations,
    imports: [NgIf, MatProgressBar, MatAnchor, MatIcon, DataTablesModule]
})
export class AreaListComponent implements OnInit, AfterViewInit, OnDestroy {
    private destroy$ = new Subject<any>();
    @ViewChild(DataTableDirective)
    dtElement!: DataTableDirective;
    dtOptions: DataTables.Settings = {};
    dataRow: any[] = [];
    @ViewChild(MatPaginator) _paginator: MatPaginator;
    @ViewChild(MatSort) private _sort: MatSort;
    flashMessage: 'success' | 'error' | null = null;
    isLoading: boolean = false;
    private _unsubscribeAll: Subject<any> = new Subject<any>();

    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
        // private _Service: PermissionService,
        private _Service: AreaService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService
    ) {}

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        this.loadTable();
    }
    hiddenEdit() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 24);
        return menu.edit == 0;
    }
    hiddenDelete() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 24);
        return menu.delete == 0;
    }
    hiddenSave() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 24);
        return menu.save == 0;
    }

    loadTable(): void {
        const that = this;
        this.dtOptions = {
            pagingType: 'full_numbers',
            pageLength: 10,
            serverSide: true,
            processing: true,
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.11.3/i18n/th.json',
            },
            ajax: (dataTablesParameters: any, callback) => {
                that._Service
                    .getDatatable(dataTablesParameters)
                    .subscribe((resp) => {
                        callback({
                            recordsTotal: resp?.data['total'] ?? 0,
                            recordsFiltered: resp?.data['total'] ?? 0,
                            data: resp.data['data'],
                        });
                        this._changeDetectorRef.markForCheck();
                    });
            },
        columns: [
            { title: 'จัดการ',data:null , orderable: false,
                render: function (data, type, row) {
                return `<div class="flex justify-evenly">
                    <button class="btn btn-sm btn-primary edit-button" data-id="${data.id}">
                        <span class="material-icons">edit</span>
                    </button>
                    <button class="btn btn-sm btn-primary delete-button text-red-500" data-id="${data.id} ">
                        <span class="material-icons">delete</span>
                    </button>
                </div>`;
                }
            },
            { title: 'ลำดับ', data: 'id', className:'whitespace-nowrap' },
            { title: 'ชื่อสถานที่', data: 'name' , className:'whitespace-nowrap'},
            { title: 'ละติจูด', data: 'latitude',className:'whitespace-nowrap' },
            { title: 'ลองจิจูด', data: 'longitude',className:'whitespace-nowrap' },
            {
                title: 'รัศมี (เมตร)', data: 'radius', className: 'whitespace-nowrap',
                render: function (data: number) {
                return new Intl.NumberFormat('en-US').format(data);
            }},
            {
                title: 'สถานะ', data: 'active',className:'whitespace-nowrap',
                render: function (data, type, row) {
                    if (data) return `<span class='bg-green-100 text-green-800 font-extrabold
                    mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900'>เปิดการใช้งาน<span>`;
                    else return `<span class='bg-red-100 text-red-800 font-extrabold
                    mr-2 px-2.5 py-0.5 rounded dark:bg-red-200 dark:text-red-900'>ปิดการใช้งาน<span>`
                }
             },
            ],
          initComplete: () => {
            const thead = document.querySelector('table thead');
            if (thead) {
            thead.classList.add('bg-gray-300');
            }
        }
    };
}


    /**
     * After view init
     */
    ngAfterViewInit(): void {
    $('table').on('click', '.edit-button', (event) => {
        const data = $(event.currentTarget).data('id');
        this.Edit(data);
    });
    $('table').on('click', '.delete-button', (event) => {
        const data = $(event.currentTarget).data('id');
        this.Delete(data);
    });
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    Edit(id: any) {
        this._Service.getLocation(id).subscribe((locationData) => {
            const dialogRef = this._matDialog.open(EditAreaComponent, {
                width: '700px',
                height: 'auto',
                data: locationData['data']
            });
            dialogRef.afterClosed().subscribe((item) => {
                this.rerender();
                this._changeDetectorRef.markForCheck();
            });
        });
    }

    New() {
        const dialogRef = this._matDialog.open(NewAreaComponent, {
            width: '700px',
            height: '700px',
        });

        dialogRef.afterClosed().subscribe((item) => {
            this.rerender();
            this._changeDetectorRef.markForCheck();
        });
    }

    rerender(): void {
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            dtInstance.ajax.reload();
        });
    }

    Delete(id) {
        const confirmation = this._fuseConfirmationService.open({
            title: 'ยืนยันลบสถานที่',
            message: 'คุณต้องการลบสถานที่ใช่หรือไม่ ?',
            icon: {
                show: true,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
            },
            actions: {
                confirm: {
                    show: true,
                    label: 'ยืนยัน',
                    color: 'primary',
                },
                cancel: {
                    show: true,
                    label: 'ยกเลิก',
                },
            },
            dismissible: true,
        });

        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            // If the confirm button pressed...
            if (result === 'confirmed') {
                this._Service
                    .deleteLocation(id)
                    .pipe(takeUntil(this.destroy$))
                    .subscribe((res: any) => {
                        if (res.code == 201) {
                            this._fuseConfirmationService
                                .open({
                                    title: 'ลบข้อมูลสถานที่',
                                    message: 'บันทึกเรียบร้อย',
                                    icon: {
                                        show: true,
                                        name: 'heroicons_outline:check-circle',
                                        color: 'success',
                                    },
                                    actions: {
                                        confirm: {
                                            show: false,
                                            label: 'ตกลง',
                                            color: 'primary',
                                        },
                                        cancel: {
                                            show: false,
                                            label: 'ยกเลิก',
                                        },
                                    },
                                    dismissible: true,
                                })
                                .afterClosed()
                                .subscribe((res) => {
                                });
                            }
                        });
                this.rerender();
                this._changeDetectorRef.markForCheck();
            }
        });
    }
}
