<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">รายการพื้นที่</div>
        <!-- Actions -->
        <div class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4">

            <!-- Add product button -->
            <a (click)="New()" class="ml-4" mat-flat-button [color]="'primary'" *ngIf="!hiddenSave()">
                <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                <span class="ml-2 mr-1">เพิ่มพื้นที่</span>
            </a>

        </div>
    </div>



    <!-- Main -->
    <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

        <div class="flex flex-col p-3 sm:p-6">
            <div class="overflow-auto  bg-white shadow sm:rounded-lg">
                <!-- Products list -->

                <div class="flex flex-col flex-auto p-5 sm:overflow-auto overflow-x-scroll w-full ">
                    <table datatable [dtOptions]="dtOptions" class="row-border hover w-full"></table>
                </div>
            </div>
        </div>
    </div>

</div>
