import { data } from 'jquery';
import { HttpClient, HttpRequest, HttpHand<PERSON>, HttpEvent, HttpHeaders, HttpInterceptor } from '@angular/common/http';
import { catchError } from 'rxjs/operators';
import { Injectable } from '@angular/core';
import {
    BehaviorSubject,
    filter,
    map,
    Observable,
    of,
    switchMap,
    take,
    tap,
    throwError,
} from 'rxjs';
import {
    AssetItem,
    Store,
    AssetType,
    Chat,
    // PermissionProductDetailOSM,
    PositionPagination,
    PositionProduct,
    StoreType,
    AssetSize,
    Supplier,
    Division,
} from './area.types';
import { environment } from 'environments/environment';
import { DataTablesResponse } from 'app/shared/datatable.types';
// import { UserDetail } from '../user/user.types';
const token = localStorage.getItem('accessToken') || null;
@Injectable({
    providedIn: 'root',
})
export class AreaService {
    /**
     * Constructor
     */
    constructor(private _httpClient: HttpClient) { }

    httpOptionsFormdata = {
    headers: new HttpHeaders({
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${token}`
    }),
    };

    // -----------------------------------------------------------------------------------------------------
    // @ Accessors
    // -----------------------------------------------------------------------------------------------------

    createLocation(data: any): Observable<any> {
        return this._httpClient
            .post(
                environment.API_URL + 'api/location-checkin',
                data,
                this.httpOptionsFormdata
            )
            .pipe(
                switchMap((response: any) => {
                    // Return a new observable with the response
                    return of(response);
                })
            );
    }
    getLocation(itemid: any): Observable<{}> {
        return this._httpClient
            .get<any>(
                `${environment.API_URL}api/location-checkin/${itemid}`,
                this.httpOptionsFormdata,
            )
            .pipe(
                map((mtplan) => {
                    return mtplan;
                }),
            );

    }

    updateLocation(formData: any, itemid: any): Observable<{}>  {
        return this._httpClient
            .put<any>(
                `${environment.API_URL}api/location-checkin/${itemid}`,
                formData
            )
    }

    deleteLocation(itemId: number): Observable<{}> {
        return this._httpClient
            .delete<any>(
                `${environment.API_URL}api/location-checkin/${itemId}`,
                this.httpOptionsFormdata
            )
            .pipe(
                map((mtplan) => {
                    return mtplan;
                }),
            );
    }

    getDatatable(dataTablesParameters: any): Observable<DataTablesResponse> {
        dataTablesParameters['columns'][0] = dataTablesParameters['columns'][1]
        return this._httpClient
            .post(
                `${environment.API_URL}api/location-checkin/datatable`,
                dataTablesParameters,
                this.httpOptionsFormdata
            )
            .pipe(
                map((response: any) => response)
            );
    }
}
