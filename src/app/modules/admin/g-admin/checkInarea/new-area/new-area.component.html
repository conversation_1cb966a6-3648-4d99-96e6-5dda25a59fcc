<div class="flex flex-col flex-auto min-w-0 bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between p-1 sm:pb-4 sm:px-10 border-b bg-card dark:bg-transparent">
        <div class="flex-1 min-w-0">
            <!-- Title -->
            <div class="mt-2">
                <h2 class="text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                    สร้างสถานที่เช็คอินใหม่
                </h2>
            </div>

        </div>
    </div>

    <!-- Main -->
    <div class="flex-auto">
        <form class="flex flex-col mt-3 p-2 pb-4 overflow-hidden ng-valid" [formGroup]="formData">
            <div class="flex flex-col sm:flex-row px-8">


                <div class="flex flex-auto flex-wrap">

                    <div class="flex flex-col w-full">
                        <!-- name -->
                        <div class="flex flex-col mt-4">
                            <mat-label class="text-xl">ชื่อสถานที่</mat-label>
                            <mat-form-field class="w-full">
                                <input matInput [formControlName]="'name'">
                            </mat-form-field>
                        </div>
                        <div class="flex flex-col mt-4">
                            <mat-label class="text-xl">ละติจูด</mat-label>
                            <mat-form-field class="w-full">
                                <input matInput formControlName="latitude" mask="separator.2" onkeypress="return /[0-9.]$/.test(event.key)" inputmode="decimal" />
                            </mat-form-field>
                        </div>
                        <div class="flex flex-col mt-4">
                            <label class="text-xl mb-1">ลองจิจูด</label>
                            <mat-form-field class="w-full" appearance="outline">
                                <input matInput  formControlName="longitude" mask="separator.2" onkeypress="return /[0-9.]$/.test(event.key)" inputmode="decimal" />
                            </mat-form-field>
                        </div>
                        <div class="flex flex-col mt-4">
                            <mat-label class="text-xl">รัศมี</mat-label>
                            <mat-form-field class="w-full">
                                <input matInput [formControlName]="'radius'" type="number">
                            </mat-form-field>
                        </div>
                        <mat-label class="text-xl">สถานะการใช้งาน</mat-label>
                        <div class="flex my-4">
                            <mat-radio-group [formControlName]="'active'" >
                                <mat-radio-button [value]="1">เปิดใช้งาน</mat-radio-button>
                                <mat-radio-button [value]="0" class="pl-2">ปิดการใช้งาน</mat-radio-button>
                            </mat-radio-group>
                        </div>
                    </div>
                </div>
            </div>
            <!-- button -->
            <div class="flex items-center justify-end w-full border-t px-8 pt-4">
                <div class="flex items-center justify-end">
                    <button mat-flat-button class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4" (click)="onClose()">
                        <mat-icon svgIcon="heroicons_solid:x-mark" class="mr-2"></mat-icon>
                        ยกเลิก
                    </button>
                    <button class="px-6 ml-3 mat-primary" mat-flat-button (click)="createNewPosition()">
                        <mat-icon svgIcon="heroicons_solid:check"></mat-icon>
                        ยืนยัน
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
