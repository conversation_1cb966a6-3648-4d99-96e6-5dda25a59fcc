<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">รายการเงินฝากพนักงาน</div>
        <!-- Actions -->
        <div class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4">

            <!-- Add button -->
            <a (click)="NewWithdraw()" class="ml-4 bg-red-500" mat-flat-button>
                <mat-icon [svgIcon]="'heroicons_outline:minus'"></mat-icon>
                <span class="ml-2 mr-1">ถอนเงินฝาก</span>
            </a>
            <a (click)="NewDeposit()" class="ml-4 bg-green-500" mat-flat-button>
                <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                <span class="ml-2 mr-1">เพิ่มเงินฝาก</span>
            </a>

        </div>
    </div>



    <!-- Main -->
    <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

        <div class="flex flex-col p-3 sm:p-6">
            <div class="overflow-auto  bg-white shadow sm:rounded-lg">
                <!-- Products list -->

                <div class="flex flex-col flex-auto p-5 sm:overflow-auto overflow-x-scroll">
                    <form [formGroup]="formData">
                        <div class="flex flex-col md:flex-row justify-between">
                            <div class="flex flex-col md:flex-row gap-2 w-full md:w-2/3 px-4">
                                <mat-form-field appearance="fill" class="w-full">
                                    <mat-label
                                        class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/4 mt-4">เลือกปี</mat-label>
                                    <mat-select [formControlName]="'year'">
                                        <mat-option *ngFor="let year of years" [value]="year.value">
                                            {{ year.label }}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                                <mat-form-field class="w-full">
                                    <mat-label
                                        class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/4 mt-4">
                                        เลือกเดือน
                                    </mat-label>
                                    <mat-select [formControlName]="'month'">
                                        <mat-option [value]="1">มกราคม</mat-option>
                                        <mat-option [value]="2">กุมภาพันธ์</mat-option>
                                        <mat-option [value]="3">มีนาคม</mat-option>
                                        <mat-option [value]="4">เมษายน</mat-option>
                                        <mat-option [value]="5">พฤษภาคม</mat-option>
                                        <mat-option [value]="6">มิถุนายน</mat-option>
                                        <mat-option [value]="7">กรกฎาคม</mat-option>
                                        <mat-option [value]="8">สิงหาคม</mat-option>
                                        <mat-option [value]="9">กันยายน</mat-option>
                                        <mat-option [value]="10">ตุลาคม</mat-option>
                                        <mat-option [value]="11">พฤศจิกายน</mat-option>
                                        <mat-option [value]="12">ธันวาคม</mat-option>
                                    </mat-select>
                                </mat-form-field>
                                <mat-form-field class="w-full">
                                    <mat-label
                                        class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/4 mt-4">
                                        เลือกสถานะ
                                    </mat-label>
                                    <mat-select [formControlName]="'status'">
                                        <mat-option [value]="'deposit'">เงินฝาก</mat-option>
                                        <mat-option [value]="'withdraw'">เงินถอน</mat-option>
                                    </mat-select>
                                </mat-form-field>

                                <!-- <mat-form-field class="w-full">
                                    <mat-label for="name"
                                        class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/4 mt-4">ชื่อพนักงาน</mat-label>
                                    <mat-select [formControlName]="'user_id'">
                                        <mat-option *ngFor="let item of UserList" [value]="item.id">
                                            {{item.first_name}} {{item.last_name}}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field> -->
                                <!-- <div class="w-full pt-1">
                                    <mat-label
                                        class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/4 mt-4">เลือกพนักงาน</mat-label>
                                    <ng-container *ngIf="UserList">
                                        <app-personnel-autocomplete [itemtypeData]="UserList"
                                            (personnelSelected)="onPersonnelSelected($event)">
                                        </app-personnel-autocomplete>
                                    </ng-container>
                                </div> -->
                            </div>
                            <div class="flex mt-0 sm:mt-6 md:mt-6 justify-end px-4 gap-2">
                                <button mat-flat-button
                                    class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                                    (click)="Search()">
                                    <mat-icon [svgIcon]="'heroicons_outline:magnifying-glass'"></mat-icon>
                                    <span class="ml-2 mr-1">ค้นหา</span>
                                </button>
                                <button mat-flat-button
                                    class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                                    type="reset">
                                    <mat-icon [svgIcon]="'heroicons_outline:arrow-path'"></mat-icon>
                                    <span class="ml-2 mr-1">ล้าง</span>
                                </button>
                            </div>
                        </div>
                    </form>
                    <table datatable [dtOptions]="dtOptions"
                        class="table w-full text-left text-gray-500 overflow-hidden">
                        <thead class="bg-gray-300 text-black">
                            <tr>
                                <th class="w-[10%]">จัดการ</th>
                                <th class="w-[5%] text-center">ลำดับ</th>
                                <th class="w-[15%]">ชื่อพนักงาน</th>
                                <th class="w-[30%]">รายละเอียด</th>
                                <th class="w-[9%] text-center">ประเภท</th>
                                <th class="w-[7%] text-center">สถานะ</th>
                                <th class="w-[10%]">จำนวนที่ฝาก</th>
                                <th class="w-[10%]">เงินสะสม</th>
                                <th class="w-[10%]">เพื่มเมื่อวันที่</th>
                            </tr>
                        </thead>
                        <tbody *ngIf="dataRow?.length != 0">
                            <tr *ngFor="let item of dataRow; let i = index">
                                <td>
                                    <button mat-icon-button (click)="Edit(item.id)"
                                        [disabled]="!isCurrentMonth(item.date)">
                                        <mat-icon>edit</mat-icon>
                                    </button>
                                    <button mat-icon-button (click)="Delete(item.id)"
                                        [disabled]="!isCurrentMonth(item.date)">
                                        <mat-icon>delete</mat-icon>
                                    </button>
                                </td>
                                <td class="text-center">{{ i + 1 }}</td>
                                <td>{{ item.user.fullname }}</td>
                                <td title="{{ item.description }}"
                                    class="truncate whitespace-nowrap overflow-hidden max-w-[150px]">{{ item.description
                                    ?? '-'}}</td>
                                <td>
                                    <div class="text-center flex items-center justify-center gap-1"
                                        [ngClass]="{'text-green-800 font-semibold ': item.type === 'deposit','text-red-600 font-semibold ': item.type === 'withdraw'}">
                                        <mat-icon [svgIcon]="'heroicons_solid:currency-dollar'"
                                            [ngClass]="{'text-green-600': item.type === 'deposit','text-red-600': item.type === 'withdraw'}"
                                            class="w-8 h-8">
                                        </mat-icon>
                                        {{ item.type === 'deposit' ? 'ฝากเงิน' : item.type === 'withdraw' ? 'ถอนเงิน' :''}}
                                    </div>
                                </td>
                                <td>
                                    <div class="rounded-lg p-2 text-center"
                                        [ngClass]="{'text-blue-800 font-bold bg-blue-200': item.payment_status === 'paid','text-yellow-600 font-bold bg-yellow-200': item.payment_status === 'pending'}">
                                        {{ item.payment_status === 'paid' ? 'จ่ายแล้ว' : item.payment_status ===
                                        'pending' ? 'รอดำเนินการ' : ''}}
                                    </div>
                                </td>
                                <td>{{ item.amount }}</td>
                                <td>{{ item.total_amount }}</td>
                                <td>{{ item.created_at | date: 'dd/MM/yyyy HH:mm' }}</td>
                            </tr>
                        </tbody>
                        <tbody *ngIf="dataRow?.length == 0">
                            <tr>
                                <td colspan="8" class="no-data-available text-center">ไม่มีข้อมูล !</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>

</div>