import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import {
  debounceTime,
  map,
  merge,
  Observable,
  Subject,
  switchMap,
  takeUntil,
} from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { <PERSON><PERSON>orm<PERSON>ield, MatLabel } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { MatRadioGroup, MatRadioButton } from '@angular/material/radio';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { EmployeeDepositService } from '../employee-deposit.service';
import { MatAutocomplete, MatOption } from '@angular/material/autocomplete';
import { NgFor, NgIf } from '@angular/common';
import { MatSelect } from '@angular/material/select';
import { PersonnelAutocompleteComponent } from 'app/shared/personnel-autocomplete/personnel-autocomplete.component';


@Component({
  selector: 'app-new-withdraw',
  animations: fuseAnimations,
  imports: [FormsModule, ReactiveFormsModule, MatFormField, MatLabel, MatInput, MatRadioGroup, MatRadioButton, MatButton, MatIcon, MatAutocomplete, NgFor, MatSelect, MatOption, PersonnelAutocompleteComponent, NgIf],
  templateUrl: './new-withdraw.component.html',
  styleUrl: './new-withdraw.component.scss'
})
export class NewWithdrawComponent implements OnInit {
  formData: FormGroup;
  customerFilterCtrl: FormControl = new FormControl();
  public UserList: any[];

  constructor(
    public dialogRef: MatDialogRef<NewWithdrawComponent>,
    private _changeDetectorRef: ChangeDetectorRef,
    private _fuseConfirmationService: FuseConfirmationService,
    private _formBuilder: FormBuilder,
    private _matDialog: MatDialog,
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
    private _authService: AuthService,
    private _Service: EmployeeDepositService,
  ) {
    this.formData = this._formBuilder.group({
      // year: [null, Validators.required],
      // month: [null, Validators.required],
      user_id: [null, Validators.required],
      amount: [null, Validators.required],
      payment_type: [null, Validators.required],

    });

    this._Service.getUser().subscribe((resp: any) => {
      this.UserList = resp.data;
      this._changeDetectorRef.markForCheck();
    });
  }

  ngOnInit(): void {

  }

  postWithdraw() {
    if (this.formData.invalid) {
      this._fuseConfirmationService.open({
        title: 'ข้อมูลไม่ครบถ้วน',
        message: 'กรุณากรอกข้อมูลให้ครบทุกช่องก่อนดำเนินการ',
        icon: {
          show: true,
          name: 'heroicons_outline:exclamation-circle',
          color: 'warn',
        },
        actions: {
          confirm: {
            show: true,
            label: 'ตกลง',
            color: 'warn',
          },
          cancel: {
            show: false,
            label: '',
          },
        },
        dismissible: true,
      });
      return; // หยุดไม่ให้ทำต่อ
    }
    const confirmation = this._fuseConfirmationService.open({
      title: 'เพิ่มเงินฝาก',
      message: 'คุณต้องการเพิ่มเงินฝากนี้ใช่หรือไม่ ?',
      icon: {
        show: true,
        name: 'heroicons_outline:information-circle',
        color: 'info',
      },
      actions: {
        confirm: {
          show: true,
          label: 'ยืนยัน',
          color: 'primary',
        },
        cancel: {
          show: true,
          label: 'ยกเลิก',
        },
      },
      dismissible: true,
    });

    // Subscribe to the confirmation dialog closed action
    confirmation.afterClosed().subscribe((result) => {
      // If the confirm button pressed...
      if (result === 'confirmed') {
        this._Service.postWithdraw(this.formData.value).subscribe({
          next: (resp: any) => {
            this.dialogRef.close();
          },
          error: (err: any) => {
            this._fuseConfirmationService.open({
              title: 'เกิดข้อผิดพลาด',
              message: err.error.message,
              icon: {
                show: true,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
              },
              actions: {
                confirm: {
                  show: false,
                  label: 'ยืนยัน',
                  color: 'primary',
                },
                cancel: {
                  show: false,
                  label: 'ยกเลิก',
                },
              },
              dismissible: true,
            });
          },
        });
      }
    });
  }

  onClose(): void {
    this.dialogRef.close();
  }

  onPersonnelSelected(selectedPersonnel: any): void {
    this.formData.patchValue({
      user_id: selectedPersonnel.id,
    })
  }

}

