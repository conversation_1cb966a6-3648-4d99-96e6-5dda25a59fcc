<div class="flex flex-col flex-auto min-w-0 bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between p-1 sm:pb-4 sm:px-10 border-b bg-card dark:bg-transparent">
        <div class="flex-1 min-w-0">
            <!-- Title -->
            <div class="mt-2">
                <h2 class="text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                    เพิ่มเงินฝาก
                </h2>
            </div>

        </div>
    </div>

    <!-- Main -->
    <div class="flex-auto overflow-y">
        <form class="flex flex-col mt-3 p-2 pb-4 overflow-hidden ng-valid" [formGroup]="formData">
            <div class="flex flex-col sm:flex-row px-8">


                <div class="flex flex-auto flex-wrap">

                    <div class="flex flex-col w-full lg:w-4/4 sm:pl-8">
                        <!-- name -->
                        <div class="flex mt-4">
                            <div class="mt-4 w-full">
                                <mat-label class="text-xl font-semibold">เลือกพนักงาน*</mat-label>
                                <ng-container *ngIf="UserList">
                                    <app-personnel-autocomplete [itemtypeData]="UserList"
                                        (personnelSelected)="onPersonnelSelected($event)">
                                    </app-personnel-autocomplete>
                                </ng-container>
                            </div>
                        </div>
                        <div class="flex mt-4">
                            <mat-form-field class="w-full pr-2">
                                <mat-label class="text-xl">จำนวนเงินที่จะฝาก</mat-label>
                                <input matInput [formControlName]="'amount'" type="number">
                            </mat-form-field>
                        </div>
                        <div class="flex mt-4">
                            <mat-form-field class="w-full pr-2">
                                <mat-label class="text-xl">รายละเอียด</mat-label>
                                <textarea matInput [formControlName]="'description'" rows="4"></textarea>
                            </mat-form-field>
                        </div>
                    </div>
                </div>
            </div>
            <!-- button -->
            <div class="flex items-center justify-end w-full border-t px-8 pt-4">
                <div class="flex items-center justify-end">
                    <button mat-flat-button
                        class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                        (click)="onClose()">
                        <mat-icon svgIcon="heroicons_solid:x-mark" class="mr-2"></mat-icon>
                        ยกเลิก
                    </button>
                    <button class="px-6 ml-3 mat-primary" mat-flat-button (click)="postDeposit()">
                        <mat-icon svgIcon="heroicons_solid:check"></mat-icon>
                        ยืนยัน
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>