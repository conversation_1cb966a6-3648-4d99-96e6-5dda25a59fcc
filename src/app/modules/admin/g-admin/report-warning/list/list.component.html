<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">รายงานใบตักเตือนพนักงาน</div>
        <!-- Actions -->
        <div class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4">

            <!-- Add button -->
            <!-- <a (click)="New()" class="ml-4" mat-flat-button [color]="'primary'">
                <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                <span class="ml-2 mr-1">สร้างใบเตือน</span>
            </a> -->

        </div>
    </div>



    <!-- Main -->
    <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

        <div class="flex flex-col p-3 sm:p-6">
            <div class="overflow-auto  bg-white shadow sm:rounded-lg">
                <!-- Products list -->

                <div class="flex flex-col flex-auto p-5 sm:overflow-auto overflow-x-scroll">
                    <form [formGroup]="formData">
                        <div class="flex flex-col md:flex-row justify-between">
                            <div class="flex flex-col md:flex-row gap-2 w-full md:w-2/3">
                                <mat-form-field  [ngClass]="formFieldHelpers" class="w-full">
                                    <mat-label
                                        class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/4 mt-4">เลือกปี</mat-label>
                                    <mat-select [formControlName]="'year'">
                                        <mat-option *ngFor="let year of years" [value]="year.value">
                                            {{ year.label }}
                                        </mat-option>
                                    </mat-select>
                                </mat-form-field>
                                <div class="w-full pt-1">
                                    <mat-label
                                        class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/4 mt-4">เลือกพนักงาน</mat-label>
                                    <ng-container *ngIf="UserList">
                                        <app-personnel-autocomplete [itemtypeData]="UserList"
                                            (personnelSelected)="onPersonnelSelected($event)">
                                        </app-personnel-autocomplete>
                                    </ng-container>
                                </div>
                            </div>
                            <div class="flex mt-0 sm:mt-6 md:mt-6 justify-end px-4 gap-2">
                                <button mat-flat-button
                                    class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                                    (click)="Search()">
                                    <mat-icon [svgIcon]="'heroicons_outline:magnifying-glass'"></mat-icon>
                                    <span class="ml-2 mr-1">ค้นหา</span>
                                </button>
                                <button mat-flat-button
                                    class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                                    type="reset">
                                    <mat-icon [svgIcon]="'heroicons_outline:arrow-path'"></mat-icon>
                                    <span class="ml-2 mr-1">ล้าง</span>
                                </button>
                            </div>
                        </div>
                    </form>
                    <table datatable [dtOptions]="dtOptions"
                        class="table w-full text-left text-gray-500 overflow-hidden">
                        <thead class="bg-gray-300 text-black">
                            <tr>
                                <th>เนื้อหา</th>
                                <th>ชื่อพนักงาน</th>
                                <th>จำนวนใบเตือน</th>
                                <th>สถานะ</th>
                                <th>ได้ใบล่าสุด</th>
                            </tr>
                        </thead>
                        <tbody *ngIf="dataRow?.length != 0">
                            <tr *ngFor="let item of dataRow; let i = index">
                                <td>
                                    <button mat-button (click)="View(item.user_id, item.id)"
                                        class="border-solid border rounded-xl p-2 bg-gray-100 hover:bg-gray-200">
                                        <span>รายละเอียด</span>
                                    </button>
                                </td>
                                <td>{{ item.fullname }}</td>
                                <td>{{ item.warning_count }}</td>
                                <td *ngIf="item.status_message != null" class="text-red-700">
                                    <div class="bg-red-200 rounded-lg w-fit p-2">
                                        {{ item.status_message }}
                                    </div>
                                </td>
                                <td *ngIf="item.status_message == null" class="text-yellow-700">
                                    <div class="bg-yellow-200 rounded-lg w-fit p-2">
                                        ใบเตือนยังไม่ครบ
                                    </div>
                                </td>
                                <td>{{ item.updated_at | date: 'dd/MM/yyyy HH:mm' }}</td>
                            </tr>
                        </tbody>
                        <tbody *ngIf="dataRow?.length == 0">
                            <tr>
                                <td colspan="5" class="no-data-available text-center">ไม่มีข้อมูล !</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>

</div>