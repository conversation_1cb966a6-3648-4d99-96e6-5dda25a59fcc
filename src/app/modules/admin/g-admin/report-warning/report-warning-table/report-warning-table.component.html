<div class="flex flex-col flex-auto min-w-0 bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between p-1 sm:pb-4 sm:px-10 border-b bg-card dark:bg-transparent">
        <div class="flex-1 min-w-0">
            <!-- Title -->
            <div class="mt-2">
                <h2 class="text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                    ใบเตือนทั้งหมด
                </h2>
            </div>

        </div>
    </div>

    <!-- Main -->
    <div class="flex-auto">
        <form class="flex flex-col mt-3 p-2 pb-4 overflow-hidden ng-valid">
            <div class="flex flex-col sm:flex-row">


                <div class="flex flex-auto flex-wrap">

                    <div class="flex flex-col w-full lg:w-4/4 sm:pl-8">
                        <!-- name -->
                        <!-- <div class="flex mb-4">
                            <div class="border-b-2" [ngClass]="{'border-gray-200': now_status == 'approved'}">
                                <button mat-button (click)="changetable('approved')"
                                    [ngClass]="{'bg-gray-200 font-bold': now_status == 'approved'}">
                                    <mat-icon svgIcon="heroicons_solid:x-circle"></mat-icon>
                                    <p class="mx-2">ยังไม่เซ็น</p>
                                </button>
                            </div>
                            <div class="border-b-2" [ngClass]="{'border-gray-200': now_status == 'finish'}">
                                <button mat-button (click)="changetable('finish')"
                                    [ngClass]="{'bg-gray-200 font-bold': now_status == 'finish'}">
                                    <mat-icon svgIcon="heroicons_solid:check-circle"></mat-icon>
                                    <p class="mx-2">เซ็นแล้ว</p>
                                </button>
                            </div>
                        </div> -->
                        <table datatable [dtOptions]="dtOptions"
                            class="table w-full text-left text-gray-500 overflow-hidden">
                            <thead class="bg-gray-300 text-black">
                                <tr>
                                    <th>เนื้อหา</th>
                                    <th>หัวเรื่อง</th>
                                    <th>ผู้อนุมัติ</th>
                                    <th>รับทราบ</th>
                                    <th>วันที่ได้รับ</th>
                                </tr>
                            </thead>
                            <tbody *ngIf="dataRow?.length != 0">
                                <tr *ngFor="let item of dataRow.warnings.slice().reverse(); let i = index">
                                    <td>
                                        <button mat-button (click)="View(item.id, item.status)"
                                            class="border-solid border rounded-xl p-2 bg-gray-100 hover:bg-gray-200">
                                            <span>รายละเอียด</span>
                                        </button>
                                    </td>
                                    <td>{{ item.title }}</td>
                                    <td>{{ item.approved_by }}</td>
                                    <td>{{ item.acknowledged_by }}</td>
                                    <td>{{ item.created_at | date: 'dd/MM/yyyy HH:mm' }}</td>
                                </tr>
                            </tbody>
                            <tbody *ngIf="dataRow?.length == 0">
                                <tr>
                                    <td colspan="5" class="no-data-available text-center">ไม่มีข้อมูล !</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
            <!-- button -->
            <div class="flex items-center justify-end w-full border-t px-8 pt-4">
                <div class="flex items-center justify-end">
                    <button mat-flat-button
                        class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                        (click)="onClose()">
                        <mat-icon svgIcon="heroicons_solid:x-mark" class="mr-2"></mat-icon>
                        ปิด
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>