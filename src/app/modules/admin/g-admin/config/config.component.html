<div class="flex flex-col flex-auto min-w-0">
    <!-- Header -->
    <!-- Loader -->
    <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
        <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
    </div>
    <div
        class="flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between p-6 sm:py-8 sm:px-10 border-b bg-card dark:bg-transparent">
        <div class="flex-1 flemin-w-0">
            <!-- Title -->
            <div class="flex flex-row mt-2 justify-between">
                <h2 class="text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                    จัดการ Config
                </h2>
            </div>
        </div>
    </div>
    <!-- Main -->
    <form [formGroup]="addForm">
        <div class="flex-auto p-6 sm:p-6">
            <div class="grid gap-4 w-full sm:grid-cols-1 md:grid-cols-1">
                <div class="bg-card rounded-2xl md:shadow overflow-hidden px-8 pt-6 pb-8 mb-4 flex flex-col my-2">
                    <div class="-mx-3 md:flex mb-6">
                        <div class="md:w-1/2 px-3">

                        </div>

                    </div>

                    <!-- <div class="-mx-3 md:flex mb-6 hidden">
                        <div class="md:w-1/2 px-3">
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                <mat-label>จำนวนงานที่ Telesale ขอเพิ่มได้ (งาน)</mat-label>
                                <input matInput type="number" min="0" [placeholder]="'เลือกจำนวนงานที่ขอได้แต่ล่ะครั้ง'"
                                    [formControlName]="'telesales_limit_req'">
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="-mx-3 md:flex mb-6 hidden">
                        <div class="md:w-1/2 px-3">
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                <mat-label>Telesale จะไม่สามารถของานเพิ่มได้ถ้ามีงานที่เสร็จสิ้นน้อยกว่าจำนวนท่กำหนด (%)</mat-label>
                                <input type="number" min="0" matInput [placeholder]="'เลือกจำนวนการปิดงานขั้นต่ำ'"
                                    [formControlName]="'telesales_percent_req'">
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="-mx-3 md:flex mb-6 hidden">
                        <div class="md:w-1/2 px-3">
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                <mat-label>จำนวนวันที่ต้องโทรครั้งต่อไปหลังจาก update สถานะการ Topup</mat-label>
                                <input type="number" min="0" matInput [placeholder]="'เลือกจำนวนวันในการโทร'"
                                    [formControlName]="'telesales_period_daycall'">
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="-mx-3 md:flex mb-6 hidden ">
                        <div class="md:w-1/2 px-3">
                            <mat-form-field class="w-full">
                                <mat-label>คำนวณคอมมิชชั่น</mat-label>
                                <mat-select [formControlName]="'commission_calculate'" required>
                                    <mat-option value="sales">จากยอดขาย</mat-option>
                                    <mat-option value="profit">จากกำไร</mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                    </div>                    
                    <div class="-mx-3 md:flex mb-6 hidden">
                        <div class="md:w-1/2 px-3">
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                <mat-label>ที่อยู่บริษัท</mat-label>
                                <input type="text"  matInput [placeholder]="'ที่อยู่'"
                                    [formControlName]="'company_address'">
                            </mat-form-field>
                        </div>
                    </div> -->
                    <div class="-mx-3 md:flex mb-6">
                        <div class="md:w-1/2 px-3">
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                <mat-label>กำหนดเงินประกันการทำงาน</mat-label>
                                <input type="text"  matInput [placeholder]="'จำนวน'"
                                    [formControlName]="'bond_required'">
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="-mx-3 md:flex mb-6">
                        <div class="md:w-1/2 px-3">
                            <mat-form-field [ngClass]="formFieldHelpers" class="w-full">
                                <mat-label>หักจ่ายเงินประกันการทำงานต่อเดือน</mat-label>
                                <input type="text"  matInput [placeholder]="'จำนวน'"
                                    [formControlName]="'bond_deduct_per_month'">
                            </mat-form-field>
                        </div>
                    </div>
                    <div mat-dialog-actions class="flex justify-end mt-2">
                        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="Submit()">
                            บันทึกข้อมูล
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
