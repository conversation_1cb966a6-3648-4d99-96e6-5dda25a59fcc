import { Route, Routes } from '@angular/router';



export default [
    // {
    //     path: '',
    //     pathMatch: 'full',
    //     redirectTo: 'brief-plan'
    // },
    {
        path: '',
        loadComponent: () => import('./assign-telesale.component').then(m => m.AssignTelesaleComponent),
        children: [
            {
                path: 'list',
                loadComponent: () => import('./list-cheif/list-assign.component').then(m => m.ListAssignComponent),
            },
            // {
            //     path: 'new-commission',
            //     component: NewCommissionComponent,

            // },
            // {
            //     path: 'edit/:id',
            //     component: EditCommissionComponent,
            //     // resolve: {
            //     //     products: PermissionProductsResolver,

            //     // }
            // },
        ],

        /*children : [
            {
                path     : '',
                component: ContactsListComponent,
                resolve  : {
                    tasks    : ContactsResolver,
                    countries: ContactsCountriesResolver
                },
                children : [
                    {
                        path         : ':id',
                        component    : ContactsDetailsComponent,
                        resolve      : {
                            task     : ContactsContactResolver,
                            countries: ContactsCountriesResolver
                        },
                        canDeactivate: [CanDeactivateContactsDetails]
                    }
                ]
            }
        ]*/
    },
] as Routes
