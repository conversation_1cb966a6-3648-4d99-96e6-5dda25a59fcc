<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">รายการที่มอบหมายแล้ว</div>
        <!-- Actions -->
        <!-- <div class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4">


            <a (click)="New()" class="ml-4" mat-flat-button [color]="'primary'">
                <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                <span class="ml-2 mr-1">บันทึกรายละเอียดการติดต่อ</span>
            </a>

        </div> -->
    </div>


    <!-- Main -->
    <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

        <div class="px-6 sm:px-6 grid md:grid-cols-2 xl:grid-cols-4 gap-6 mt-6">

            <div class="flex items-center p-8 bg-white shadow rounded-lg">
                <div
                    class="inline-flex flex-shrink-0 items-center justify-center h-16 w-16 text-purple-600 bg-purple-100 rounded-full mr-6">
                    <svg aria-hidden="true" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                </div>
                <div>
                    <span class="block text-2xl font-bold">{{show_all}}</span>
                    <span class="block text-gray-500">งานทั้งหมด</span>
                </div>
            </div>

            <div class="flex items-center p-8 bg-white shadow rounded-lg">
                <div
                    class="inline-flex flex-shrink-0 items-center justify-center h-16 w-16 text-green-600 bg-green-100 rounded-full mr-6">
                    <svg aria-hidden="true" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 7h8m0 0v8m0-8l-8 8-4-4-6 6" />
                    </svg>
                </div>
                <div>
                    <span class="block text-2xl font-bold">{{show_call_true}}</span>
                    <span class="block text-gray-500">ติตต่อสำเร็จ</span>
                </div>
            </div>


            <div class="flex items-center p-8 bg-white shadow rounded-lg">
                <div
                    class="inline-flex flex-shrink-0 items-center justify-center h-16 w-16 text-red-600 bg-red-100 rounded-full mr-6">
                    <svg aria-hidden="true" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M13 17h8m0 0V9m0 8l-8-8-4 4-6-6" />
                    </svg>
                </div>
                <div>
                    <span class="inline-block text-2xl font-bold">{{show_call_false}}</span>
                    <!-- <span class="inline-block text-xl text-gray-500 font-semibold">บาท</span> -->
                    <span class="block text-gray-500">ติตต่อไม่สำเร็จ</span>
                </div>
            </div>


            <!-- <div class="flex items-center p-8 bg-white shadow rounded-lg">
                <div
                    class="inline-flex flex-shrink-0 items-center justify-center h-16 w-16 text-blue-600 bg-blue-100 rounded-full mr-6">
                    <svg aria-hidden="true" fill="none" viewBox="0 0 24 24" stroke="currentColor" class="h-6 w-6">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2"
                            d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                    </svg>
                </div>
                <div>
                    <span class="block text-2xl font-bold">70</span>
                    <span class="block text-gray-500">สรุปยอดโทรทั้งหมด</span>
                </div>
            </div> -->
        </div>

        <div class="flex flex-col p-3 sm:p-6">
            <div class="overflow-auto  bg-white shadow sm:rounded-lg">
                <!-- Products list -->
                <div class="flex flex-auto p-5 sm:items-center sm:justify-between border-b">
                    <div class="text-2xl font-extrabold ">รายการที่มอบหมายแล้ว</div>

                    <div class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4">


                    </div>
                </div>

                <div class="flex flex-col flex-auto p-5 sm:overflow-auto overflow-x-scroll">
                    <form class="flex flex-col pt-4 pb-4 overflow-hidden ng-valid"
                            [formGroup]="filterForm">
                            <div class="flex flex-col sm:flex-row">
                                <div class="w-full">
                                    <div class="flex flex-auto flex-wrap">
                                        <div class="flex flex-col w-full lg:w-1/4 sm:pl-8">
                                            <mat-form-field class="w-full">
                                                <mat-date-range-input [formGroup]="filterForm" [rangePicker]="picker_range" class="w-full">
                                                    <input matStartDate formControlName="start_date" placeholder="Start date" readonly />
                                                    <input matEndDate formControlName="end_date" placeholder="End date" readonly />
                                                </mat-date-range-input>
                                                <mat-datepicker-toggle matSuffix [for]="picker_range"></mat-datepicker-toggle>
                                                <mat-date-range-picker #picker_range></mat-date-range-picker>
                                            </mat-form-field>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </form>
                    <form [formGroup]="formData">
                        <div class="flex justify-end">
                            <button class="px-6 ml-3 mat-primary" mat-flat-button (click)="create()"
                                *ngIf="!hiddenSave()">
                                <mat-icon svgIcon="heroicons_solid:check"></mat-icon>
                                ดึงงานกลับ
                            </button>
                        </div>
                    </form>
                    <!-- <a (click)="onTelCustomer()" class="ml-4" mat-flat-button [color]="'primary'">
                                <mat-icon [svgIcon]="'phone'"></mat-icon>
                                <span class="ml-2 mr-1">โทรหาลูกค้า</span>
                            </a> -->
                    <div class="table-responsive overflow-auto my-4">
                        <table datatable [dtOptions]="dtOptions"
                        class="table w-full text-left text-gray-500 overflow-hidden">
                        <thead
                        class="bg-gray-300 text-black">
                                <tr>
                                    <th> <mat-checkbox (change)="selectAllChanged($event)"></mat-checkbox></th>
                                    <th>วันที่สั่ง</th>
                                    <th>ชื่อ-สกุล</th>
                                    <th>อีเมล</th>
                                    <th>เบอร์ติดต่อ</th>
                                </tr>
                            </thead>
                            <tbody *ngIf="dataRow?.length != 0">
                                <tr *ngFor="let item of dataRow; let i = index">
                                    <td class="flex items-center    ">

                                        <mat-checkbox (change)="check($event,item.id)" [checked]="selectAll"
                                            *ngIf="!hiddenSave()"></mat-checkbox>

                                    </td>
                                    <td style="min-width: 200px;">{{ item.sale_order?.date_time | date: 'dd/MM/yyyy'  }}</td>
                                    <td style="min-width: 200px;">{{ item.customer.name }}</td>
                                    <td style="min-width: 150px;">{{ item.customer.email }}</td>
                                    <td style="min-width: 150px;">{{ item.customer.phone }}</td>
                                </tr>
                            </tbody>
                            <tbody *ngIf="dataRow?.length == 0">
                                <tr>
                                    <td colspan="6" class="no-data-available text-center">ไม่มีข้อมูล !</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>

                </div>

            </div>
        </div>
    </div>

</div>
