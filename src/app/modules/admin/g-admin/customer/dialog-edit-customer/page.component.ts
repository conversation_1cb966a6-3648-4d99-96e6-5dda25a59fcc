import { After<PERSON>iew<PERSON>nit, ChangeDetectorRef, Component, Inject, OnDestroy, OnInit, ViewChild } from '@angular/core';
import { FormBuilder, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { <PERSON><PERSON>ort, MatSortHeader } from '@angular/material/sort';
import { fuseAnimations } from '@fuse/animations';
import { MatDialog, MatDialogRef, MAT_DIALOG_DATA, MatDialogTitle, MatDialogActions } from '@angular/material/dialog';
import { Router } from '@angular/router';
import { CustomerPagination } from '../customer.types';
import { CustomerService } from '../customer.service';
import { ItemTypeService } from '../../item-type/item-type.service';
import { LocationService } from '../../location/location.service';
import { VendorService } from '../../vendor/vendor.service';
import { MatTableDataSource, MatTable, MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatCellDef, MatCell, MatHeaderRowDef, MatHeaderRow, MatRowDef, MatRow } from '@angular/material/table';
import { Subject } from 'rxjs';
import { NgIf } from '@angular/common';
import { CdkDrag } from '@angular/cdk/drag-drop';
import { MatAnchor, MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { MatFormField, MatLabel } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';

@Component({
    selector: 'app-dialog-edit-customer',
    templateUrl: './page.component.html',
    styleUrls: ['./page.component.scss'],
    animations: fuseAnimations,
    imports: [MatDialogTitle, NgIf, CdkDrag, MatAnchor, MatIcon, FormsModule, ReactiveFormsModule, MatFormField, MatLabel, MatInput, MatDialogActions, MatButton, MatTable, MatSort, MatColumnDef, MatHeaderCellDef, MatHeaderCell, MatSortHeader, MatCellDef, MatCell, MatHeaderRowDef, MatHeaderRow, MatRowDef, MatRow]
})

export class DialogEditCustomerComponent implements OnInit, AfterViewInit, OnDestroy {

    ACTION :string = 'HISTORY';
    @ViewChild('recentTransactionsTable', {read: MatSort}) recentTransactionsTableMatSort: MatSort;

    data: any;
    recentTransactionsDataSource: MatTableDataSource<any> = new MatTableDataSource();
    recentTransactionsTableColumns: string[] = ['no', 'old_name', 'old_telephone', 'old_email', 'old_address'];
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    isLoading: boolean = false;
    form: FormGroup
    filterData = [];
    dataRow: any;
    rawDataFilter: any[] = []
    /**
     * Constructor
     */
    constructor(
        @Inject(MAT_DIALOG_DATA) private _data,
        public dialogRef: MatDialogRef<DialogEditCustomerComponent>,
        private _formBuilder: FormBuilder,
        private _Service: CustomerService,

    ) {

        this.form = this._formBuilder.group({
            name: '',
            telephone: '',
            email: '',
            address: '',
        });

    }


    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {


        if(this._data.status === 'HISTORY') {
            this.recentTransactionsDataSource.data = this._data.data;
        } else {
            this.ACTION = 'EDIT'
            this.form.patchValue({
                ...this._data.data
            })
        }



    }

    trackByFn(index: number, item: any): any
    {
        return item.id || index;
    }


    submit(): void {
        this.dialogRef.close(this.form.value)
    }

    onClose() {
        this.dialogRef.close();
    }

    /**
     * After view init
     */
    ngAfterViewInit(): void {

    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        // this.addItem();
    }

}
