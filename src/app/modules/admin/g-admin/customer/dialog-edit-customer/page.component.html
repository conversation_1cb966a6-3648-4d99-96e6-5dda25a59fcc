<div mat-dialog-title>
    <div class="flex flex-auto p-2 sm:items-center sm:justify-between border-b">
        <div class="text-2xl font-extrabold ">
            <h3 mat-dialog-titleclass="modal-title" id="exampleModalLabel" cdkDrag *ngIf="ACTION === 'EDIT'"
                cdkDragRootElement=".cdk-overlay-pane">
                แก้ไขข้อมูล
            </h3>
            <h3 mat-dialog-titleclass="modal-title" id="exampleModalLabel" cdkDrag *ngIf="ACTION === 'HISTORY'"
                cdkDragRootElement=".cdk-overlay-pane">
                ประวัติการแก้ไขข้อมูล
            </h3>
        </div>
        <div class="flex shrink-0 items-center mt-4 sm:mt-0 sm:ml-4">
            <a mat-stroked-button type="button" class="border-none" (click)="onClose()">
                <mat-icon>close</mat-icon>
            </a>
        </div>
    </div>
</div>
<div class="flex-auto border-b" *ngIf="ACTION === 'EDIT'">
    <form [formGroup]="form">
        <div class="flex-auto p-6 sm:p-10">
            <div class="grid gap-4 w-full sm:grid-cols-1 md:grid-cols-1">
                <div class="bg-card rounded-2xl md:shadow overflow-hidden px-8 pt-6 pb-8 mb-4 flex flex-col my-2">
                    <div class="-mx-3 md:flex my-1">
                        <div class="md:w-full px-3">
                            <mat-form-field class="w-full">
                                <mat-label>ชื่อลูกค้า</mat-label>
                                <input matInput [placeholder]="'กรอกชื่อลูกค้า'" formControlName="name">
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="-mx-3 md:flex my-1">
                        <div class="md:w-full px-3 mb-6 md:mb-0">
                            <mat-form-field class="w-full">
                                <mat-label>เบอร์ติดต่อ</mat-label>
                                <input matInput [placeholder]="'กรอกเบอร์ติดต่อ'" formControlName="telephone">
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="-mx-3 md:flex my-1">
                        <div class="md:w-full px-3 mb-6 md:mb-0">
                            <mat-form-field class="w-full">
                                <mat-label>อีเมล</mat-label>
                                <input matInput [placeholder]="'กรอกอีเมล'" formControlName="email">
                            </mat-form-field>
                        </div>
                    </div>
                    <div class="-mx-3 md:flex my-1">
                        <div class="md:min-w-full px-3 mb-6 md:mb-0">
                            <mat-form-field class="w-full">
                                <mat-label>ที่อยู่</mat-label>
                                <textarea matInput [placeholder]="'กรอกที่อยู่'" [rows]="3"
                                    formControlName="address"></textarea>
                            </mat-form-field>
                        </div>
                    </div>
                    <div mat-dialog-actions class="flex justify-end mt-2">
                        <button class="px-6 ml-3" mat-flat-button>
                            ยกเลิก
                        </button>
                        <button class="px-6 ml-3" mat-flat-button [color]="'primary'" (click)="submit()">
                            ตกลง
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</div>
<div class="grid grid-cols-1 xl:grid-cols-3 gap-8 w-full mt-8" *ngIf="ACTION === 'HISTORY'">
    <!-- Recent transactions table -->
    <div class="xl:col-span-3 flex flex-col flex-auto bg-card shadow rounded-2xl overflow-hidden">
        <div class="overflow-x-auto mx-6">
            <table class="w-full bg-transparent" mat-table matSort [dataSource]="recentTransactionsDataSource"
                [trackBy]="trackByFn" #recentTransactionsTable>
                <!-- Transaction ID -->
                <ng-container matColumnDef="no">
                    <th mat-header-cell mat-sort-header *matHeaderCellDef>
                        ลำดับ
                    </th>
                    <td mat-cell *matCellDef="let transaction">
                        <span class="pr-6 font-medium text-sm text-secondary whitespace-nowrap">
                            {{transaction.id}}
                        </span>
                    </td>
                </ng-container>

                <!-- Date -->
                <ng-container matColumnDef="old_name">
                    <th mat-header-cell mat-sort-header *matHeaderCellDef>
                        ชื่อ เก่า
                    </th>
                    <td mat-cell *matCellDef="let transaction">
                        <span class="pr-6 font-medium text-sm text-secondary whitespace-nowrap">
                            {{transaction.old_name}}
                        </span>
                    </td>
                </ng-container>

                <!-- Name -->
                <ng-container matColumnDef="old_telephone">
                    <th mat-header-cell mat-sort-header *matHeaderCellDef>
                        Telephone เก่า
                    </th>
                    <td mat-cell *matCellDef="let transaction">
                        <span class="pr-6 font-medium text-sm text-secondary whitespace-nowrap">
                            {{transaction.old_telephone}}
                        </span>
                    </td>
                </ng-container>

                <!-- Amount -->
                <ng-container matColumnDef="old_email">
                    <th mat-header-cell mat-sort-header *matHeaderCellDef>
                        Email เก่า </th>
                    <td mat-cell *matCellDef="let transaction">
                        <span class="pr-6 font-medium text-sm text-secondary whitespace-nowrap">
                            {{transaction.old_email}}
                        </span>
                    </td>
                </ng-container>

                <!-- Status -->
                <ng-container matColumnDef="old_address">
                    <th mat-header-cell mat-sort-header *matHeaderCellDef>
                        ที่อยู่ เก่า
                    </th>
                    <td mat-cell *matCellDef="let transaction">
                        <span class="pr-6 font-medium text-sm text-secondary whitespace-nowrap">
                            {{transaction.old_address}}
                        </span>
                    </td>
                </ng-container>

                <!-- Footer -->


                <tr mat-header-row *matHeaderRowDef="recentTransactionsTableColumns"></tr>
                <tr class="order-row h-16" mat-row *matRowDef="let row; columns: recentTransactionsTableColumns;"></tr>
                <!-- <tr
                    class="h-16 border-0"
                    mat-footer-row
                    *matFooterRowDef="['recentOrdersTableFooter']"></tr> -->
            </table>
        </div>
    </div>

</div>