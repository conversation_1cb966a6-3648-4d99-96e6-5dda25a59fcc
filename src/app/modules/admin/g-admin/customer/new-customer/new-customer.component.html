<div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

    <!-- Header -->
    <div
        class="flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between p-6 sm:py-8 sm:px-10 border-b bg-card dark:bg-transparent">
        <div class="flex-1 min-w-0">
            <!-- Title -->
            <div class="mt-2">
                <h2 class="text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                    เพิ่มลูกค้าใหม่
                </h2>
            </div>

        </div>
    </div>

    <!-- Main -->
    <div class="flex-auto p-3 sm:p-6">
        <!-- This example requires Tailwind CSS v2.0+ -->
        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
            <div class="flex">
                <div class="w-full">
                    <div class="px-4 mt-4 sm:px-6">
                        <h2 class="text-2xl font-extrabold leading-6 text-gray-900">รายละเอียดลูกค้า</h2>
                        <p class="text-lg mt-1 max-w-2xl text-gray-500">กรุณากรอกข้อมูลลูกค้า</p>
                    </div>
                </div>
                <div class="w-full">
                    <div class="px-4 mt-4 sm:px-6">
                        <button class="px-6 ml-3 mat-primary" mat-flat-button (click)="addRow()">
                            <mat-icon svgIcon="heroicons_solid:plus"></mat-icon>
                            เพิ่มที่อยู่
                        </button>
                    </div>
                </div>

            </div>
            <form class="flex flex-col mt-3 p-8 pb-4 bg-card shadow overflow-hidden ng-valid" [formGroup]="formData">
                <div class="flex">
                    <div class="w-full">
                        <div class=" border-gray-200">
                            <dl>
                                <div class="bg-white px-4 mt-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-l font-extrabold text-gray-500  mt-4">ชื่อ - นามสกุล</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                                        <mat-form-field class="w-full pr-2">
                                            <input matInput [formControlName]="'name'"
                                                placeholder="กรุณาระบุชื่อ - นามสกุล" required>
                                        </mat-form-field>
                                    </dd>
                                </div>
                                <div class="bg-white-50 px-4 mt-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-l font-extrabold text-gray-500  mt-4">ผู้แนะนำ</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                                        <mat-form-field class="w-full pr-2">
                                            <input matInput [formControlName]="'contact'"
                                                placeholder="กรุณาระบุผู้แนะนำ" required>
                                        </mat-form-field>
                                    </dd>
                                </div>
                                <div class="bg-white-50 px-4 mt-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-l font-extrabold text-gray-500  mt-4">อีเมล</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                                        <mat-form-field class="w-full pr-2">
                                            <input matInput [formControlName]="'email'" placeholder="กรุณาระบุอีเมล" required [pattern]="emailPattern">
                                        </mat-form-field>
                                    </dd>
                                </div>
                                <div class="bg-white-50 px-4 mt-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-l font-extrabold text-gray-500  mt-4">เบอร์ติดต่อ</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                                        <mat-form-field class="w-full pr-2">
                                            <input matInput [formControlName]="'phone'" matInput maxlength="10"
                                                placeholder="กรุณาระบุเบอร์โทร" required>
                                        </mat-form-field>
                                    </dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                    <div class="w-full">
                        <div class=" border-gray-200" formArrayName="customer_line">
                            <dl>
                                <div class="bg-white px-4 flex sm:gap-4 sm:px-6"
                                    *ngFor="let depo; of: customer_line().controls; let i = index" [formGroupName]="i">
                                    <dt class="text-l font-extrabold text-gray-500 w-1/6 mt-4">ที่อยู่ </dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0 w-full">
                                        <div class="flex mt-4 px-5">
                                            <mat-form-field class="w-full pr-2">
                                                <textarea matInput rows="3" [formControlName]="'address'"></textarea>
                                            </mat-form-field>
                                            <button class="px-6 ml-3 mat-primary" mat-flat-button
                                                (click)="removeItem(i)">
                                                <mat-icon svgIcon="heroicons_solid:trash"></mat-icon>
                                                ลบ
                                            </button>
                                        </div>


                                    </dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                </div>
                <div class="flex items-center justify-end w-full border-t px-8 py-4">
                    <div class="flex items-center justify-end">
                        <button mat-flat-button class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4" [routerLink]="['/customer/list']">
                            <mat-icon svgIcon="heroicons_solid:x-mark" class="mr-2"></mat-icon>
                            ยกเลิก
                        </button>
                        <button class="px-6 ml-3 mat-primary" mat-flat-button (click)="NewCustomer()">
                            <mat-icon svgIcon="heroicons_solid:check"></mat-icon>
                            ยืนยัน
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>
