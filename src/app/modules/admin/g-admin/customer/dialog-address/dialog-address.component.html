<div mat-dialog-title>
    <div class="flex flex-auto p-2 sm:items-center sm:justify-between border-b">
        <div class="text-2xl font-extrabold ">
            <h3 mat-dialog-titleclass="modal-title" id="exampleModalLabel" cdkDrag cdkDragRootElement=".cdk-overlay-pane">
                เลือกที่อยู่ลูกค้า
            </h3>
        </div>
 
        <div class="flex shrink-0 items-center mt-4 sm:mt-0 sm:ml-4">
            <a mat-stroked-button type="button" (click)="onClose()">
                <mat-icon>close</mat-icon>
                ปิด
            </a>
        </div>
    </div>
</div>

<div class="flex-auto border-b">
    <form class="flex flex-col mt-3" [formGroup]="formData">
        <div class="flex">
            <!-- <div class="w-full pr-2">
                <mat-form-field class="w-2/3">
                    <mat-label>เลือกหมวดสินค้า</mat-label>
                    <mat-select [formControlName]="'item_type_id'" (selectionChange)="onChangeItemType($event.value)">
                        <mat-option *ngFor="let item of itemtypeData" [value]="item.id">
                            {{item.name}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div> -->
            <div class="w-full">
                <mat-form-field class="w-1/3">
                    <mat-label>ค้นหาที่อยู่ลูกค้า</mat-label>
                    <input matInput [formControlName]="'filter'" (keyup)=" onFilter($event)"
                        placeholder="ค้นหาที่อยู่ลูกค้า">
                </mat-form-field>
            </div>
        </div>
    </form>

    <div class="my-4">ตารางข้อมูล : ดับเบิ้ลคลิกที่แถว เพื่อเลือกข้อมูลนั้น</div>

</div>
<div class="flex flex-col flex-auto min-w-0 bg-card dark:bg-transparent">
    <div class="flex flex-col">
        <div class="overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div class="py-2 inline-block min-w-full sm:px-6 lg:px-8">
                <div class="overflow-hidden">
                    <table class=" table row-border hover w-full text-lg text-left text-gray-500 dark:text-gray-400 bg-gray-100"
                        style="text-align: center;">
                        <thead
                            class="text-lg text-gray-700 uppercase bg-white-100 dark:bg-gray-700 dark:text-gray-400 text-center">
                            <tr>
                                <!-- <th scope="col" class="text-lg font-medium text-gray-900 px-6 py-4 text-left">
                                    #
                                </th> -->
                                <th scope="col" class="text-lg font-medium text-gray-900 px-6 py-4 text-left">
                                    ที่อยู่ลูกค้า <b>[ {{ this.dataRow.name}} ] </b>
                                </th>


                            </tr>
                        </thead>
                        <tbody *ngIf="dataRow2?.length != 0">
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-md py-15"
                                *ngFor="let customer of dataRow2; let k = index" style="cursor: pointer;"
                                (dblclick)="submit(customer)">
                                <!-- <td class="px-6 py-4 whitespace-nowrap text-lg font-medium text-gray-900">
                                    {{k+1}}</td> -->
                                <td class="text-lg text-gray-900 font-light px-6 py-4 whitespace-nowrap">
                                    {{customer.address}}
                                </td>
                                <!-- <td class="text-lg text-gray-900 font-light px-6 py-4 whitespace-nowrap">
                                    {{customer.email}}
                                </td>
                                <td class="text-lg text-gray-900 font-light px-6 py-4 whitespace-nowrap">
                                    {{customer.phone}}
                                </td> -->
                            </tr>
                        </tbody>
                        <tbody *ngIf="dataRow2?.length == 0">
                            <tr>
                                <td class="no-data-available text-center">ไม่มีข้อมูล !</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
            &nbsp;


        </div>
    </div>
</div>
