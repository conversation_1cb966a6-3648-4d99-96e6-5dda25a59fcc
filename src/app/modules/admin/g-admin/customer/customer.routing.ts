import { Route, Routes } from '@angular/router';
// import { CreateUserComponent } from './create-user/create-user.component';
// import { UserListComponent } from './list/list.component';




// import { AssetTypeResolver, PermissionProductsResolver } from './user.resolvers';


export default [
    // {
    //     path: '',
    //     pathMatch: 'full',
    //     redirectTo: 'brief-plan'
    // },
    {
        path: '',
        loadComponent: () => import('./customer.component').then(m => m.CustomerComponent),
        children: [
            {
                path: 'list',
                loadComponent: () => import('./list/list.component').then(m => m.CustomerListComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },
            {
                path: 'new-customer',
                loadComponent: () => import('./new-customer/new-customer.component').then(m => m.NewCustomerComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },
            {
                path: 'edit/:id',
                loadComponent: () => import('./edit-customer/edit-customer.component').then(m => m.EditCustomerComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },


        ]

        /*children : [
            {
                path     : '',
                component: ContactsListComponent,
                resolve  : {
                    tasks    : ContactsResolver,
                    countries: ContactsCountriesResolver
                },
                children : [
                    {
                        path         : ':id',
                        component    : ContactsDetailsComponent,
                        resolve      : {
                            task     : ContactsContactResolver,
                            countries: ContactsCountriesResolver
                        },
                        canDeactivate: [CanDeactivateContactsDetails]
                    }
                ]
            }
        ]*/
    }
] as Routes
