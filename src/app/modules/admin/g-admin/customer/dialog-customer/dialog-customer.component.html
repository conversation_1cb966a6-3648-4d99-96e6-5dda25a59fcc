<div mat-dialog-title>

    <div class="flex flex-auto p-2 sm:items-center sm:justify-between border-b">
        <div class="text-2xl font-extrabold ">
            <h3 mat-dialog-titleclass="modal-title" id="exampleModalLabel" cdkDrag cdkDragRootElement=".cdk-overlay-pane">
                เลือกลูกค้า
            </h3>
        </div>

        <div class="flex shrink-0 items-center mt-4 sm:mt-0 sm:ml-4">
            <a mat-stroked-button type="button" (click)="onClose()">
                <mat-icon>close</mat-icon>
                ปิด
            </a>
        </div>
    </div>

</div>

<div class="flex-auto border-b">
    <form class="flex flex-col mt-3" [formGroup]="formData">
        <div class="flex">
            <!-- <div class="w-full pr-2">
                <mat-form-field class="w-2/3">
                    <mat-label>เลือกหมวดสินค้า</mat-label>
                    <mat-select [formControlName]="'item_type_id'" (selectionChange)="onChangeItemType($event.value)">
                        <mat-option *ngFor="let item of itemtypeData" [value]="item.id">
                            {{item.name}}
                        </mat-option>
                    </mat-select>
                </mat-form-field>
            </div> -->
            <div class="w-full">
                <mat-form-field class="w-1/3">
                    <mat-label>ค้นหาลูกค้า</mat-label>
                    <input matInput [formControlName]="'filter'" (keyup)=" onFilter($event)" placeholder="ค้นหาลูกค้า">
                </mat-form-field>
            </div>
        </div>
    </form>

    <div class="my-4">ตารางข้อมูล : ดับเบิ้ลคลิกที่แถว เพื่อเลือกข้อมูลนั้น</div>

</div>
<div class="flex flex-col flex-auto min-w-0 bg-card dark:bg-transparent">
    <div class="flex flex-col">
        <div class="overflow-x-auto sm:-mx-6 lg:-mx-8">
            <div class="py-2 inline-block min-w-full sm:px-6 lg:px-8">
                <div class="overflow-hidden">
                    <table class=" table row-border hover w-full text-lg text-left text-gray-500 dark:text-gray-400 bg-gray-100"
                        style="text-align: center;">
                        <thead
                            class="text-lg text-gray-700 uppercase bg-white-100 dark:bg-gray-700 dark:text-gray-400 text-center">
                            <tr>
                                <!-- <th scope="col" class="text-lg font-medium text-gray-900 px-6 py-4 text-left">
                                    #
                                </th> -->
                                <th scope="col" class="text-lg font-medium text-gray-900 px-6 py-4 text-left">
                                    รหัสลูกค้า
                                </th>
                                <th scope="col" class="text-lg font-medium text-gray-900 px-6 py-4 text-left">
                                    ชื่อลูกค้า
                                </th>
                                <th scope="col" class="text-lg font-medium text-gray-900 px-6 py-4 text-left">
                                    อีเมล
                                </th>
                                <th scope="col" class="text-lg font-medium text-gray-900 px-6 py-4 text-left">
                                    เบอร์ติดต่อ
                                </th>

                            </tr>
                        </thead>
                        <tbody>
                            <tr class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-md py-15"
                                *ngFor="let customer of dataRow; let k = index" style="cursor: pointer;"
                                (dblclick)="submit(customer)">
                                <!-- <td class="px-6 py-4 whitespace-nowrap text-lg font-medium text-gray-900">
                                    {{k+1}}</td> -->
                                 <td class="text-lg text-gray-900 font-light px-6 py-4 whitespace-nowrap">
                                     {{customer.id}}
                                 </td>
                                <td class="text-lg text-gray-900 font-light px-6 py-4 whitespace-nowrap">
                                    {{customer.name}}
                                </td>
                                <td class="text-lg text-gray-900 font-light px-6 py-4 whitespace-nowrap">
                                    {{customer.email}}
                                </td>
                                <td class="text-lg text-gray-900 font-light px-6 py-4 whitespace-nowrap">
                                    {{customer.phone}}
                                </td>

                            </tr>
                            <!-- <p class="border-b"
                            *ngFor="let customers of customer.main_customer_line; let j = index">
                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                {{j+1}}</td>
                            <td class="text-sm text-gray-900 font-light px-6 py-4 whitespace-nowrap">
                                {{customers.address}}
                            </td>
                        </p> -->
                        </tbody>
                    </table>
                </div>
            </div>
            &nbsp;
            <!-- <form [formGroup]="formData1">
                        <div class="py-2 inline-block min-w-full sm:px-6 lg:px-8" formArrayName="item">
                            <div class="overflow-hidden">
                                <table class="min-w-full">
                                    <thead class="border-b">
                                        <tr>
                                            <th scope="col" class="text-sm font-extrabold text-gray-900 px-6 py-4 text-left">
                                                #
                                            </th>
                                            <th scope="col" class="text-sm font-extrabold text-gray-900 px-6 py-4 text-left">
                                                ชื่อสินค้า
                                            </th>
                                            <th scope="col" class="text-sm font-extrabold text-gray-900 px-6 py-4 text-left">
                                                จำนวนคงเหลือ
                                            </th>
                                            <th scope="col" class="text-sm font-extrabold text-gray-900 px-6 py-4 text-left">
                                                ราคา
                                            </th>
                                            <th scope="col" class="text-sm font-extrabold text-gray-900 px-6 py-4 text-left">
                                                จัดการ
                                            </th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    <tbody>
                                        <tr class="border-b" *ngFor="let depo; of: item().controls; let i = index"
                                            [formGroupName]="i">
                                            <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                                                {{i +1}}</td>
                                            <td class="text-sm text-gray-900 font-light px-6 py-4 whitespace-nowrap">
                                                <input matInput [formControlName]="'item_id'">
                                            </td>
                                            <td class="text-sm text-gray-900 font-light px-6 py-4 whitespace-nowrap">
                                                <input matInput [formControlName]="'qty'">
                                            </td>
                                            <td class="text-sm text-gray-900 font-light px-6 py-4 whitespace-nowrap">
                                                <input matInput [formControlName]="'price'">
                                            </td>
                                            <td class="text-sm text-gray-900 font-light px-6 py-4 whitespace-nowrap">
                                                <button class="px-6 ml-3" mat-flat-button (click)="removeItem(i)">
                                                    <mat-icon svgIcon="heroicons_solid:trash"> </mat-icon>&nbsp;
                                                    ลบ
                                                </button>
                                            </td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </form> -->

            <!-- <div class="flex items-center justify-end w-full border-t px-8 py-4">
                        <div class="flex items-center justify-end">

                            <button class="px-6 ml-3 mat-sec" mat-flat-button (click)="onClose()">
                                <mat-icon svgIcon="heroicons_solid:x-mark"></mat-icon>
                                ยกเลิก
                            </button>
                            <button class="px-6 ml-3 mat-primary" mat-flat-button (click)="onSubmit()">
                                <mat-icon svgIcon="heroicons_solid:check"></mat-icon>
                                ยืนยัน
                            </button>
                        </div>
                    </div> -->

        </div>
    </div>
</div>
