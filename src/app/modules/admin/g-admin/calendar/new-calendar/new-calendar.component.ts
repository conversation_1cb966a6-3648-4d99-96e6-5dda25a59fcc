import {
    After<PERSON>iew<PERSON>nit,
    ChangeDetector<PERSON>ef,
    Component,
    On<PERSON><PERSON>roy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { BankPagination } from '../calendar.types';
import { CalendarService } from '../calendar.service';
// import { ImportOSMComponent } from '../card/import-osm/import-osm.component';

import dayGridPlugin from '@fullcalendar/daygrid';
import { AddComponent } from '../add/add.component';
import { EditComponent } from '../edit/edit.component';
import { PositionService } from '../../position/position.service';
import { AddByUserComponent } from '../add-by-user/add.component';
import { NgIf, NgFor, CommonModule } from '@angular/common';
import { MatProgressBar } from '@angular/material/progress-bar';
import { MatButton, MatButtonModule } from '@angular/material/button';
import { MatIcon, MatIconModule } from '@angular/material/icon';
import { MatLabel, MatFormField, MatFormFieldModule } from '@angular/material/form-field';
import { MatSelect, MatSelectModule } from '@angular/material/select';
import { MatOption } from '@angular/material/autocomplete';
import { MatInputModule } from '@angular/material/input';
import { MatTabsModule } from '@angular/material/tabs';
import { FullCalendarModule } from '@fullcalendar/angular';
import { CalendarApi, CalendarOptions } from '@fullcalendar/core';

// defineFullCalendarElement();

@Component({
    selector: 'app-new-calendar',
    templateUrl: './new-calendar.component.html',
    styleUrls: ['./new-calendar.component.scss'],
    animations: fuseAnimations,
    imports: [
        CommonModule, MatIconModule, MatFormFieldModule, ReactiveFormsModule, MatInputModule, MatFormFieldModule,
        MatTabsModule, MatSelectModule, MatButtonModule, FullCalendarModule
    ],
})
export class NewCalendarComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild('calendar') calendar: CalendarApi;
    ACTION: string = '';
    events: any = [];
    files: File[] = [];
    formData: FormGroup;
    flashErrorMessage: string;
    flashMessage: 'success' | 'error' | null = null;
    isLoading: boolean = false;
    searchInputControl: FormControl = new FormControl();
    selectedProduct: any | null = null;
    filterForm: FormGroup;
    tagsEditMode: boolean = false;
    env_path = environment.API_URL;

    // me: any | null;
    // get roleType(): string {
    //     return 'marketing';
    // }
    userData: any[];
    supplierId: string | null;
    pagination: BankPagination;

    DataYear: any[] = [];
    positionData: any;

    calendarOptions: CalendarOptions = {
        locale: 'th',
        plugins: [dayGridPlugin],
        headerToolbar: {
            // left: 'prevYear,prev,next,nextYear today',
            left: 'prev,next today',
            center: 'title',
            right: 'prevYear,nextYear',
            // right: 'dayGridMonth,dayGridWeek,dayGridDay,',
        },
        buttonText: {
            today: 'วันนี้',
            month: 'เดือน',
            week: 'สัปดาห์',
            // day: 'วัน',
            prevYear: 'ปีก่อนหน้า',
            nextYear: 'ปีถัดไป',
        },
        contentHeight: 600,
        eventClick: this.onEvent.bind(this),
        events: [],
    };

    calendarOptionsByUser: CalendarOptions = {
        locale: 'th',
        plugins: [dayGridPlugin],
        headerToolbar: {
            // left: 'prevYear,prev,next,nextYear today',
            left: 'prev,next today',
            center: 'title',
            right: 'prevYear,nextYear',
            // right: 'dayGridMonth,dayGridWeek,dayGridDay,',
        },
        buttonText: {
            today: 'วันนี้',
            month: 'เดือน',
            week: 'สัปดาห์',
            // day: 'วัน',
            prevYear: 'ปีก่อนหน้า',
            nextYear: 'ปีถัดไป',
        },
        contentHeight: 600,
        eventClick: this.onEvent.bind(this),
        events: [],
    };

    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
        private _Service: CalendarService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService,
        private _ServicePosition: PositionService
    ) {
        this.formData = this._formBuilder.group({
            year: '',
            position_id: '',
            user_id: '',
        });

        let yearNow = new Date().getUTCFullYear();

        for (let index = 0; index < 10; index++) {
            //ย้อนหลัง 3 ปี
            this.DataYear.push({
                yearTH: yearNow + 543 + index - 3,
                years: yearNow + index - 3,
            });
        }
    }

    ngOnInit(): void {
        const url = this._router.url;
        if (url === '/calendar/user-calendar') {
            this.ACTION = 'USER-CALENDAR';
        } else {
            this.ACTION = 'CALENDAR';
        }

        this.clearForm();
        this.listPosition();
        // this.calendarOptions;
        this.calendarOptions.rerenderDelay = 3;
    }
    hiddenEdit() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 26);
        return menu.edit == 0;
    }
    hiddenDelete() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 26);
        return menu.delete == 0;
    }
    hiddenSave() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 26);
        return menu.save == 0;
    }
    ngAfterViewInit(): void {}

    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
    }

    onChangePosition(data: any) {
        this.GetUser(data);
    }

    GetUser(data): void {
        this.userData = [];
        this._Service.getUser(data).subscribe((resp: any) => {
            this.userData = resp.data;
        });
    }

    showFlashMessage(type: 'success' | 'error'): void {
        // Show the message
        this.flashMessage = type;

        // Mark for check
        this._changeDetectorRef.markForCheck();

        // Hide it after 3 seconds
        setTimeout(() => {
            this.flashMessage = null;

            // Mark for check
            this._changeDetectorRef.markForCheck();
        }, 3000);
    }

    listPosition(): any {
        this.positionData = [];
        this._Service.getPosition(1).subscribe((resp: any) => {
            this.positionData = resp.data;
        });
    }

    clearForm(): void {
        let year = new Date().getUTCFullYear();
        this.formData.patchValue({
            year: year,
            position_id: '',
            user_id: '',
        });
    }

    getPlane() {
        if (!this.formData.get('position_id').value) {
            this._fuseConfirmationService.open({
                title: 'กรุณาตรวจสอบข้อมูล',
                message: 'ระบุตำแหน่งงาน ก่อนทำการค้นหา',
                icon: {
                    show: true,
                    name: 'heroicons_outline:exclamation-triangle',
                    color: 'warning',
                },
                actions: {
                    confirm: {
                        show: true,
                        label: 'ตกลง',
                        color: 'primary',
                    },
                    cancel: {
                        show: false,
                        label: 'ยกเลิก',
                    },
                },
                dismissible: true,
            });
        } else {
            let body = {
                position_id: this.formData.value.position_id,
                year: this.formData.value.year,
            };
            this._Service.getCalendar(body).subscribe(
                (res: any) => {
                    if (res.data.length <= 0) {
                        this.events = [];
                        this.calendarOptions = {
                            ...this.calendarOptions,
                            events: this.events,
                        };
                        this._fuseConfirmationService.open({
                            title: 'ไม่พบข้อมูลที่ค้นหา',
                            message: 'กรุณาเลือกค้นหาด้วย ปี และตำแหน่งใหม่!',
                            icon: {
                                show: true,
                                name: 'heroicons_outline:exclamation-triangle',
                                color: 'warning',
                            },
                            actions: {
                                confirm: {
                                    show: true,
                                    label: 'ตกลง',
                                    color: 'primary',
                                },
                                cancel: {
                                    show: false,
                                    label: 'ยกเลิก',
                                },
                            },
                            dismissible: true,
                        });
                    } else {
                        this.events = [];
                        for (let i = 0; i <= res.data.length - 1; i++) {
                            let color = '';
                            if (res.data[i]['type'] === 'Holiday') {
                                color = '#7f1d1d';
                            } else {
                                color = '#0284c7';
                            }
                            let typeDays =
                                res.data[i]['type'] == 'Holiday'
                                    ? 'วันหยุด'
                                    : 'วันทำงาน';
                            let desc =
                                res.data[i]['description'] != null
                                    ? 'รายละเอียด :' +
                                      res.data[i]['description']
                                    : '';
                            let sendData = {
                                title:
                                    res.data[i]['time_in'] +
                                    ' - ' +
                                    res.data[i]['time_out'] +
                                    ' ' +
                                    typeDays +
                                    '' +
                                    desc,
                                date: res.data[i]['date'],
                                groupId: res.data[i]['id'],
                                color: color,

                                description: desc,
                            };
                            this.events.push(sendData);
                        }
                        this.calendarOptions = {
                            ...this.calendarOptions,
                            events: this.events,
                        };

                        // this.gotoCalandar(this.formData.value.year);
                    }
                },
                (error: any) => {
                    this.formData.patchValue({
                        position_id: '',
                    });
                    this._fuseConfirmationService.open({
                        title: 'พบข้อผิดพลาด กรุณาตรวจสอบข้อมูล',
                        message: error,
                        icon: {
                            show: true,
                            name: 'heroicons_outline:exclamation-triangle',
                            color: 'warning',
                        },
                        actions: {
                            confirm: {
                                show: false,
                                label: 'ยืนยัน',
                                color: 'primary',
                            },
                            cancel: {
                                show: false,
                                label: 'ยกเลิก',
                            },
                        },
                        dismissible: true,
                    });
                }
            );
        }
    }

    gotoCalandar(toYear: any): void {
        let yearNow = new Date().getUTCFullYear();
        let clickYear = toYear - yearNow;

        // this.calendar.nextYear();
    }

    New(): void {
        if (this._router.url === '/calendar/new-calendar') {
            const dialogRef = this._matDialog.open(AddComponent, {});

            dialogRef.afterClosed().subscribe((item) => {
                // this.getPlane();
                this._changeDetectorRef.markForCheck();
            });
        } else {
            const dialogRef = this._matDialog.open(AddByUserComponent, {});

            dialogRef.afterClosed().subscribe((item) => {
                // this.getPlane();
                this._changeDetectorRef.markForCheck();
            });
        }
    }

    onEvent(arg: any): void {
        const dialogRef = this._matDialog.open(EditComponent, {
            data: { id: arg.event._def.groupId },
        });

        dialogRef.afterClosed().subscribe((item) => {
            this.getPlane();
            this._changeDetectorRef.markForCheck();
        });
    }
}
function defineFullCalendarElement() {
    throw new Error('Function not implemented.');
}

