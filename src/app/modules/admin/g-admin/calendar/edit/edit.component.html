<div class="flex flex-col sm:flex-row flex-0 sm:justify-between p-1 sm:pb-4 sm:px-10 border-b bg-card dark:bg-transparent w-auto min-w-200 max-h-screen">
    <div class="flex-1 min-w-0">
        <!-- Title -->
        <div class="mt-2">
            <h1 class="text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                แก้ไขตารางงาน
            </h1>
        </div>
    </div>
</div>
<div class="flex-auto p-3 sm:p-2">
    <form [formGroup]="formData">
        <div class="flex mt-4">
            <label class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">ตำแหน่งงาน</label>
            <mat-form-field class="w-full pr-2">
                <mat-select [formControlName]="'position_id'" disabled>
                    <mat-option [value]="''">
                        เลือกตำแหน่ง
                    </mat-option>
                    <mat-option *ngFor="let item of positionData" [value]="item.id">
                        {{item.name}}
                    </mat-option>
                </mat-select>
            </mat-form-field>
        </div>

        <div class="flex mt-4">
            <label class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">วันที่</label>
            <mat-form-field class="w-full pr-2">
                <input readonly [formControlName]="'date'" matInput [matDatepicker]="picker_start_date" disabled>
                <mat-datepicker-toggle matSuffix [for]="picker_start_date"></mat-datepicker-toggle>
                <mat-datepicker #picker_start_date></mat-datepicker>
            </mat-form-field>
        </div>
        <div class="flex mt-4">
            <label class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">เวลาเข้างาน</label>
            <mat-form-field class="w-full pr-2">
                <input matInput type="time" [formControlName]="'time_in'">
            </mat-form-field>
        </div>
        <div class="flex mt-4">
            <label class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">เวลาออกงาน</label>
            <mat-form-field class="w-full pr-2">
                <input matInput type="time" [formControlName]="'time_out'">
            </mat-form-field>
        </div>
        <div class="flex mt-4">
            <label class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">ประเภทวันทำงาน</label>
            <mat-form-field class="w-full pr-2">
                <mat-select [formControlName]="'type'">
                    <mat-option [value]="''">
                        <span>เลือกประเภทวันทำงาน</span>
                    </mat-option>
                    <mat-option [value]="'Holiday'">
                        <span>วันหยุด</span>
                    </mat-option>
                    <mat-option [value]="'Work'">
                        <span>วันทำงาน</span>
                    </mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        <div class="flex mt-4">
            <label class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">รายละเอียด</label>
            <mat-form-field class="w-full pr-2">
                <textarea matInput rows="3" [formControlName]="'description'"></textarea>
            </mat-form-field>
        </div>
        <div class="flex items-center justify-end w-full border-t px-8 pt-4">
            <div class="flex items-center justify-end">
                <button mat-flat-button class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4" (click)="onClose()">
                    <mat-icon svgIcon="heroicons_solid:x-mark" class="mr-2"></mat-icon>
                    ยกเลิก
                </button>
                <button class="px-6 ml-3 mat-primary" mat-flat-button (click)="Update()">
                    <mat-icon svgIcon="heroicons_solid:check"></mat-icon>
                    ยืนยัน
                </button>
            </div>
        </div>
    </form>
</div>
