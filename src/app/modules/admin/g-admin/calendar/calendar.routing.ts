import { Route, Routes } from '@angular/router';







export default [
    // {
    //     path: '',
    //     pathMatch: 'full',
    //     redirectTo: 'brief-plan'
    // },
    {
        path: '',
        loadComponent: () => import('./calendar.component').then(m => m.CalendarComponent),
        children: [
            {
                path: 'user-calendar',
                loadComponent: () => import('./new-calendar/new-calendar.component').then(m => m.NewCalendarComponent),
            },
            {
                path: 'new-calendar',
                loadComponent: () => import('./new-calendar/new-calendar.component').then(m => m.NewCalendarComponent),
            },
            {
                path: 'ads-calendar',
                loadComponent: () => import('./ads-calendar/ads-calendar.component').then(m => m.AdsCalendarComponent),
            },
            {
                path: 'admin-calendar',
                loadComponent: () => import('./admin-calendar/admin-calendar.component').then(m => m.AdminCalendarComponent),
            },
            {
                path: 'telesale-calendar',
                loadComponent: () => import('./telesale-calendar/telesale-calendar.component').then(m => m.TelesaleCalendarComponent),
            },
            {
                path: 'manager-calendar',
                loadComponent: () => import('./manager-calendar/manager-calendar.component').then(m => m.ManagerCalendarComponent),
            },
            {
                path: 'pack-calendar',
                loadComponent: () => import('./pack-calendar/pack-calendar.component').then(m => m.PackCalendarComponent),
            },


        ]
        /*children : [
            {
                path     : '',
                component: ContactsListComponent,
                resolve  : {
                    tasks    : ContactsResolver,
                    countries: ContactsCountriesResolver
                },
                children : [
                    {
                        path         : ':id',
                        component    : ContactsDetailsComponent,
                        resolve      : {
                            task     : ContactsContactResolver,
                            countries: ContactsCountriesResolver
                        },
                        canDeactivate: [CanDeactivateContactsDetails]
                    }
                ]
            }
        ]*/
    }
] as Routes
