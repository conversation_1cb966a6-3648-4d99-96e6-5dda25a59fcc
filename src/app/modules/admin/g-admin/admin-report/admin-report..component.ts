import {
    ChangeDetectionStrategy,
    Component,
    OnInit,
    ViewEncapsulation,
} from '@angular/core';
import { RouterOutlet } from '@angular/router';

@Component({
    selector: 'app-admin-report',
    templateUrl: './admin-report.component.html',
    styleUrls: ['./admin-report.component.scss'],
    encapsulation: ViewEncapsulation.None,
    changeDetection: ChangeDetectionStrategy.OnPush,
    imports: [RouterOutlet]
})
export class AdminReportComponent implements OnInit {
    constructor() {}

    ngOnInit(): void {}
}
