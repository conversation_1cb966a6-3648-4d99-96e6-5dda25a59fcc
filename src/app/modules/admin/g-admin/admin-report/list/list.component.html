<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">
    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">รายงานยอดขายแอดมิน</div>
        <!-- Actions -->
        <div class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4">
            <!-- Add product button -->

        </div>
    </div>
    <div class="flex-auto">
        <form class="flex flex-col pt-4 pb-4 bg-card shadow overflow-hidden ng-valid" [formGroup]="filterForm">
            <div class="flex flex-col sm:flex-row">
                <div class="w-full">
                    <div class="flex flex-auto flex-wrap">
                        <div class="flex flex-col w-full lg:w-1/6 sm:pl-8">
                            <mat-form-field class="w-full">
                                <mat-select [formControlName]="'user_id'" matInput placeholder="เลือกพนักงาน" >
                                    <mat-option *ngFor="let user of userData" [value]="user.id">
                                        {{user.first_name + ' ' + user.last_name}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                        <div class="flex flex-col w-full lg:w-1/6 sm:pl-8">
                            <mat-form-field class="w-full">
                                <input readonly [formControlName]="'date_start'" matInput placeholder="วันที่เริ่มต้น"
                                    [matDatepicker]="picker_start_date">
                                <mat-datepicker-toggle matSuffix [for]="picker_start_date"></mat-datepicker-toggle>
                                <mat-datepicker #picker_start_date></mat-datepicker>
                            </mat-form-field>
                        </div>
                        <div class="flex flex-col w-full lg:w-1/6 sm:pl-8">
                            <mat-form-field class="w-full">

                                <input readonly [formControlName]="'date_end'" matInput placeholder="วันที่สิ้นสุด"
                                    [matDatepicker]="picker_end_date">
                                <mat-datepicker-toggle matSuffix [for]="picker_end_date"></mat-datepicker-toggle>
                                <mat-datepicker #picker_end_date></mat-datepicker>
                            </mat-form-field>
                        </div>
                        <div class="flex flex-col w-full lg:w-1/6 sm:pl-8">
                            <mat-form-field class="w-full">
                                <mat-select [formControlName]="'page_id'" matInput placeholder="เลือกเพจ" multiple>
                                    <mat-option *ngFor="let item of Page" [value]="item.id">
                                        {{item.name}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        </div>
                        <div class="flex flex-col w-full lg:w-1/6 sm:pl-8 sm:pr-4">
                                <button mat-flat-button
                                class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                                (click)="GetReport()">
                                <mat-icon [svgIcon]="'heroicons_outline:magnifying-glass'"></mat-icon>
                                <span class="ml-2 mr-1">ค้นหา</span>
                            </button>
                            </div>
                            <div class="flex flex-col w-full lg:w-1/6  sm:pr-4">
                                <button mat-flat-button
                                class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                               type="reset" >
                                <mat-icon [svgIcon]="'heroicons_outline:arrow-path'"></mat-icon>
                                <span class="ml-2 mr-1">ล้าง</span>
                            </button>
                            </div>
                    </div>
                </div>
            </div>
        </form>
        <div class="flex flex-col" id="print-section">
            <div class="overflow-x-auto sm:-mx-6 lg:-mx-8">
                <div class="py-2 inline-block min-w-full sm:px-6 lg:px-8">
                    <div class="overflow-hidden">
                        <table class="table w-full text-left text-gray-500 overflow-hidden">
                            <thead
                            class="bg-gray-300 text-black">
                                <tr>
                                    <th scope="col" class="text-l font-large text-gray-900 px-6 py-4 text-left">
                                        ลำดับ
                                    </th>
                                    <th scope="col" class="text-l font-large text-gray-900 px-6 py-4 text-left">
                                        ชื่อพนักงาน
                                    </th>
                                    <th scope="col" class="text-l font-large text-gray-900 px-6 py-4 text-left">
                                        ตำแหน่ง
                                    </th>
                                    <th scope="col" class="text-l font-large text-gray-900 px-6 py-4 text-left">
                                        วันที่
                                    </th>
                                    <th scope="col" class="text-l font-large text-gray-900 px-6 py-4 text-left">
                                        จำนวนออเดอร์
                                    </th>
                                    <th scope="col" class="text-l font-large text-gray-900 px-6 py-4 text-left">
                                        ยอดขาย
                                    </th>
                                    <th scope="col" class="text-l font-large text-gray-900 px-6 py-4 text-left">
                                        ค่าคอมมิชชั่น
                                    </th>
                                    <th scope="col" class="text-l font-large text-gray-900 px-6 py-4 text-left">
                                        รายชื่อเพจ
                                    </th>
                                </tr>
                            </thead>

                            <tbody>
                                <ng-container *ngFor="let item of dataRow; let k = index">
                                    <tr class="bg-gray-100 border-b">
                                        <td class="px-6 py-4 text-sm font-medium text-gray-900" [attr.rowspan]="item.date.length + 1">
                                            {{k + 1}}
                                        </td>
                                        <td class="text-sm text-gray-900 font-light px-6 py-4" [attr.rowspan]="item.date.length + 1">
                                            {{item?.first_name }} {{item?.last_name }}
                                        </td>
                                        <td class="text-sm text-gray-900 font-light px-6 py-4" [attr.rowspan]="item.date.length + 1">
                                            {{item?.position?.name ?? '-'}}
                                        </td>
                                    </tr>
                                    <ng-container *ngFor="let dateitem of item.date; let d = index">
                                        <tr class="border-b">
                                            <td class="text-sm text-gray-900 font-light px-6 py-4">
                                                {{dateitem?.date | date: 'dd/MM/yyyy'}}
                                            </td>
                                            <td class="text-sm text-gray-900 font-light px-6 py-4 text-center">
                                                {{dateitem?.date_count_order ?? '-'}}
                                            </td>
                                            <td class="text-sm text-gray-900 font-light px-6 py-4 text-center">
                                                {{dateitem?.date_comission ?? '-'}}
                                            </td>
                                            <td class="text-sm text-gray-900 font-light px-6 py-4 text-center">
                                                {{dateitem?.date_sum_total ?? '-'}}
                                            </td>
                                            <td class="text-sm text-gray-900 font-light px-6 py-4">
                                                <div *ngFor="let pageItem of dateitem.page; let i = index">
                                                    <a class="text-link" (click)="openDialog(dateitem)">{{pageItem.page.name}}</a>
                                                </div>
                                            </td>
                                        </tr>
                                    </ng-container>
                                </ng-container>
                            </tbody>



                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
