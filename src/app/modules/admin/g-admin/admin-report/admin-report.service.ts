import { HttpClient, HttpHeaders } from '@angular/common/http';
import { Injectable } from '@angular/core';
import { environment } from 'environments/environment';
import { Observable, switchMap, of, tap } from 'rxjs';
const token = localStorage.getItem('accessToken') || null;
@Injectable({
    providedIn: 'root',
})
export class AdminReportService {
    constructor(private _httpClient: HttpClient) {}
    httpOptionsFormdata = {
        headers: new HttpHeaders({ Authorization: `Bearer ${token}` }),
    };
    getByItemType(itemTypeId: number): Observable<any> {
        return this._httpClient
            .post(
                environment.API_URL + 'api/get_item',
                { item_type_id: itemTypeId },
                this.httpOptionsFormdata
            )
            .pipe(
                switchMap((response: any) => {
                    // Return a new observable with the response
                    return of(response);
                })
            );
    }
    getConfig(): Observable<any> {
        return this._httpClient.get(environment.API_URL + 'api/config').pipe(
            switchMap((response: any) => {
                return of(response.data);
            })
        );
    }
    getReport(data: any): Observable<any> {
        return this._httpClient
            .post(
                environment.API_URL + 'api/report_sales_admin_new',
                data,
                this.httpOptionsFormdata
            )
            .pipe(
                switchMap((response: any) => {
                    // Return a new observable with the response
                    return of(response);
                })
            );
    }
    getUser(): Observable<any> {
        return this._httpClient.get(environment.API_URL + 'api/get_user');
    }

    getPage() {
        return this._httpClient.get(environment.API_URL + 'api/get_page');
    }
}
