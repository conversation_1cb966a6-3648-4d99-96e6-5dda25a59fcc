import { Route, Routes } from '@angular/router';
// import { CreateUserComponent } from './create-user/create-user.component';
// import { UserListComponent } from './list/list.component';




// import { AssetTypeResolver, PermissionProductsResolver } from './user.resolvers';


export default [
    // {
    //     path: '',
    //     pathMatch: 'full',
    //     redirectTo: 'brief-plan'
    // },
    {
        path: '',
        loadComponent: () => import('./manage-warning.component').then(m => m.ManageWarningComponent),
        children: [
            {
                path: 'list',
                loadComponent: () => import('./list/list.component').then(m => m.ListComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
                data: {
                    status: null,
                }
            },
            {
                path: 'list/sent',
                loadComponent: () => import('./list/list.component').then(m => m.ListComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
                data: {
                    status: 'approved',
                }
            },
            {
                path: 'list/notfinish',
                loadComponent: () => import('./list/list.component').then(m => m.ListComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
                data: {
                    status: 'open',
                }
            },
            {
                path: 'list/finish',
                loadComponent: () => import('./list/list.component').then(m => m.ListComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
                data: {
                    status: 'finish',
                }
            },


        ]

        /*children : [
            {
                path     : '',
                component: ContactsListComponent,
                resolve  : {
                    tasks    : ContactsResolver,
                    countries: ContactsCountriesResolver
                },
                children : [
                    {
                        path         : ':id',
                        component    : ContactsDetailsComponent,
                        resolve      : {
                            task     : ContactsContactResolver,
                            countries: ContactsCountriesResolver
                        },
                        canDeactivate: [CanDeactivateContactsDetails]
                    }
                ]
            }
        ]*/
    }
] as Routes
