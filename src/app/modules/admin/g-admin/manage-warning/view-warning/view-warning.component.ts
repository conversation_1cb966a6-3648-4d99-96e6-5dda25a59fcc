import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import {
  debounceTime,
  map,
  merge,
  Observable,
  Subject,
  switchMap,
  takeUntil,
} from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { <PERSON><PERSON>orm<PERSON>ield, MatLabel } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { MatRadioGroup, MatRadioButton } from '@angular/material/radio';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { ManageWarningService } from '../manage-warning.service';
import { MatAutocomplete } from '@angular/material/autocomplete';
import { MAT_DIALOG_DATA } from '@angular/material/dialog';
import { Inject } from '@angular/core';
import { NgIf } from '@angular/common';

@Component({
  selector: 'app-view-warning',
  templateUrl: './view-warning.component.html',
  styleUrl: './view-warning.component.scss',
  animations: fuseAnimations,
  imports: [FormsModule, ReactiveFormsModule, MatFormField, MatLabel, MatInput, MatRadioGroup, MatRadioButton, MatButton, MatIcon, MatAutocomplete, NgIf]
})
export class ViewWarningComponent implements OnInit {
  dataWarning: any;
  dataWarningPunishment: any;
  formData: FormGroup;
hasCompanyLogo: boolean = false;

  constructor(
    public dialogRef: MatDialogRef<ViewWarningComponent>,
    private _changeDetectorRef: ChangeDetectorRef,
    private _fuseConfirmationService: FuseConfirmationService,
    private _formBuilder: FormBuilder,
    private _matDialog: MatDialog,
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
    private _authService: AuthService,
    private _Service: ManageWarningService,
    @Inject(MAT_DIALOG_DATA) public data: any,
  ) {
    this.formData = this._formBuilder.group({
      title: [null, Validators.required],
      description: [null, Validators.required],
      approved_by: [null, Validators.required],
    });
  }

  ngOnInit(): void {
    this._Service.getWarningType().subscribe((resp: any)=>{
      this.dataWarning = resp.data
    })
    this._Service.getWarningPunishmentType().subscribe((resp: any)=>{
      this.dataWarningPunishment = resp.data
    })
    this._Service.getWarningById(this.data.id)
      .subscribe((resp) => {
        this.dataWarning = resp.data
        this.formData.patchValue({
          title: resp.data.title,
          description: resp.data.description,
          approved_by: JSON.parse(localStorage.getItem('user') || '{}')?.id,
        })
      })
  }

  approve() {
    const confirmation = this._fuseConfirmationService.open({
      title: 'อนุมัติ',
      message: 'คุณต้องการอนุมัติที่จะส่งใบเตือนใช่หรือไม่ ?',
      icon: {
        show: true,
        name: 'heroicons_outline:information-circle',
        color: 'info',
      },
      actions: {
        confirm: {
          show: true,
          label: 'ยืนยัน',
          color: 'primary',
        },
        cancel: {
          show: true,
          label: 'ยกเลิก',
        },
      },
      dismissible: true,
    });

    // Subscribe to the confirmation dialog closed action
    confirmation.afterClosed().subscribe((result) => {
      // If the confirm button pressed...
      if (result === 'confirmed') {
        this._Service.approveWarning(this.formData.value, this.data.id).subscribe({
          next: (resp: any) => {
            this.dialogRef.close();
          },
          error: (err: any) => {
            this._fuseConfirmationService.open({
              title: 'เกิดข้อผิดพลาด',
              message: err.error.message,
              icon: {
                show: true,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
              },
              actions: {
                confirm: {
                  show: false,
                  label: 'ยืนยัน',
                  color: 'primary',
                },
                cancel: {
                  show: false,
                  label: 'ยกเลิก',
                },
              },
              dismissible: true,
            });
          },
        });
      }
    });
  }

  onClose(): void {
    this.dialogRef.close();
  }

  getThaiDate(dateStr: string): string {
    const date = new Date(dateStr);
    const months = [
      'มกราคม', 'กุมภาพันธ์', 'มีนาคม', 'เมษายน', 'พฤษภาคม', 'มิถุนายน',
      'กรกฎาคม', 'สิงหาคม', 'กันยายน', 'ตุลาคม', 'พฤศจิกายน', 'ธันวาคม'
    ];

    const day = date.getDate();
    const month = months[date.getMonth()];
    const year = date.getFullYear() + 543;

    return `${day} ${month} พ.ศ. ${year}`;
  }
}
