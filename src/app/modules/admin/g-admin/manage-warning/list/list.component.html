<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">ใบตักเตือนพนักงาน</div>
        <!-- Actions -->
        <div class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4">

            <!-- Add button -->
            <a (click)="New()" class="ml-4" mat-flat-button [color]="'primary'">
                <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                <span class="ml-2 mr-1">สร้างใบเตือน</span>
            </a>

        </div>
    </div>



    <!-- Main -->
    <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

        <div class="flex flex-col p-3 sm:p-6">
            <div class="overflow-auto  bg-white shadow sm:rounded-lg">
                <!-- Products list -->

                <div class="flex flex-col flex-auto p-5 sm:overflow-auto overflow-x-scroll">
                    <div class="flex sm:flex-col md:flex-row lg:flex-row justify-between sm:gap-2 mb-4">
                        <div class="flex md:flex-row sm:flex-col">
                            <div class="border-b-2 " [ngClass]="{'border-gray-200': now_status == null}">
                                <a [routerLink]="['/manange_warning/list']" mat-button
                                    class="rounded-none text-kPrimary font-bold"
                                    [ngClass]="{'bg-gray-200 font-bold': now_status == null}">
                                    <mat-icon svgIcon="heroicons_solid:information-circle"></mat-icon>
                                    <p class="mx-2">ทั้งหมด</p>
                                </a>
                            </div>
                            <div class="border-b-2 " [ngClass]="{'border-gray-200': now_status == 'open'}">
                                <a [routerLink]="['/manange_warning/list/notfinish']" mat-button
                                    class="rounded-none text-kPrimary font-bold"
                                    [ngClass]="{'bg-gray-200 font-bold': now_status == 'open'}">
                                    <mat-icon svgIcon="heroicons_solid:x-circle"></mat-icon>
                                    <p class="mx-2">ยังไม่ได้ส่ง</p>
                                </a>
                            </div>
                            <div class="border-b-2 " [ngClass]="{'border-gray-200': now_status == 'approved'}">
                                <a [routerLink]="['/manange_warning/list/sent']" mat-button
                                    class="rounded-none text-kPrimary font-bold"
                                    [ngClass]="{'bg-gray-200 font-bold': now_status == 'approved'}">
                                    <mat-icon svgIcon="heroicons_solid:check-circle"></mat-icon>
                                    <p class="mx-2">ส่งแล้ว</p>
                                </a>
                            </div>
                            <div class="border-b-2 " [ngClass]="{'border-gray-200': now_status == 'finish'}">
                                <a [routerLink]="['/manange_warning/list/finish']" mat-button
                                    class="rounded-none text-kPrimary font-bold"
                                    [ngClass]="{'bg-gray-200 font-bold': now_status == 'finish'}">
                                    <mat-icon svgIcon="heroicons_solid:pencil"></mat-icon>
                                    <p class="mx-2">เซ็นแล้ว</p>
                                </a>
                            </div>
                        </div>
                    </div>
                    <table datatable [dtOptions]="dtOptions"
                        class="table w-full text-left text-gray-500 overflow-hidden">
                        <thead class="bg-gray-300 text-black">
                            <tr>
                                <th *ngIf="now_status != 'approve'">จัดการ</th>
                                <th>เนื้อหา</th>
                                <th>หัวเรื่อง</th>
                                <th>ชื่อพนักงาน</th>
                                <th>ผู้อนุมัติ</th>
                                <th>สถานะ</th>
                                <th>วันที่เพิ่ม</th>
                            </tr>
                        </thead>
                        <tbody *ngIf="dataRow?.length != 0">
                            <tr *ngFor="let item of dataRow; let i = index">
                                <td class="w-[10%]" *ngIf="now_status != 'approve'">
                                    <button mat-icon-button (click)="Edit(item.id)">
                                        <mat-icon>edit</mat-icon>
                                    </button>
                                    <button mat-icon-button (click)="Delete(item.id)">
                                        <mat-icon>delete</mat-icon>
                                    </button>
                                </td>
                                <td>
                                    <button mat-button (click)="View(item.id)" class="border-solid border rounded-xl p-2 bg-gray-100 hover:bg-gray-200">
                                        <span>รายละเอียด</span>
                                    </button>
                                </td>
                                <td>{{ item.title }}</td>
                                <td>{{ item.user.fullname }}</td>
                                <td>{{ item.approved_by?.fullname }}</td>
                                <td *ngIf="item.acknowledged_by == null">
                                    <div class="bg-blue-200 rounded-lg w-fit p-2 text-blue-700">
                                        รอเซ็นรับทราบ
                                    </div>
                                </td>
                                <td *ngIf="item.acknowledged_by != null">
                                    <div class="bg-green-200 rounded-lg w-fit p-2 text-green-700">
                                        เซ็นรับทราบแล้ว
                                    </div>
                                </td>
                                <td>{{ item.updated_at | date: 'dd/MM/yyyy HH:mm' }}</td>
                            </tr>
                        </tbody>
                        <tbody *ngIf="dataRow?.length == 0">
                            <tr>
                                <td colspan="6" class="no-data-available text-center">ไม่มีข้อมูล !</td>
                            </tr>
                        </tbody>
                    </table>
                </div>

            </div>
        </div>
    </div>

</div>