import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import {
  FormBuilder,
  FormControl,
  FormGroup,
  Validators,
} from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import {
  debounceTime,
  map,
  merge,
  Observable,
  Subject,
  switchMap,
  takeUntil,
} from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { sortBy, startCase } from 'lodash-es';
// import { NewDepartmentComponent } from '../new-department/new-department.component';
// import { ImportOSMComponent } from '../card/import-osm/import-osm.component';
import { MatTableDataSource } from '@angular/material/table';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { NgIf, NgFor, DatePipe } from '@angular/common';
import { MatProgressBar } from '@angular/material/progress-bar';
import { MatAnchor, MatIconButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { ManageWarningService } from '../manage-warning.service';
import { NewWarningComponent } from '../new-warning/new-warning.component';
import { NgClass } from '@angular/common';
import { RouterLink } from '@angular/router';
import { EditWarningComponent } from '../edit-warning/edit-warning.component';
import { ViewWarningComponent } from '../view-warning/view-warning.component';

@Component({
  selector: 'app-list',
  animations: fuseAnimations,
  imports: [NgIf, MatProgressBar, MatAnchor, MatIcon, DataTablesModule, NgFor, MatIconButton, DatePipe, NgClass, RouterLink],
  templateUrl: './list.component.html',
  styleUrl: './list.component.scss'
})
export class ListComponent implements OnInit {
  isLoading: boolean = false;
  dtOptions: DataTables.Settings = {};
  dataRow: any[] = [];

  @ViewChild(DataTableDirective)
  dtElement!: DataTableDirective;
  private destroy$ = new Subject<any>();

  now_status: string = "";

  constructor(
    private _changeDetectorRef: ChangeDetectorRef,
    private _fuseConfirmationService: FuseConfirmationService,
    private _formBuilder: FormBuilder,
    // private _Service: PermissionService,
    private _matDialog: MatDialog,
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
    private _authService: AuthService,
    private _Service: ManageWarningService,
  ) {
    this.now_status = this._activatedRoute.snapshot.data.status;
  }

  ngOnInit(): void {
    this.loadTable()
  }

  pages = { current_page: 1, last_page: 1, per_page: 10, begin: 0 };
  loadTable(): void {
    const that = this;
    this.dtOptions = {
      pagingType: 'full_numbers',
      pageLength: 10,
      serverSide: true,
      processing: true,
      order: [[0 , 'desc']],
      language: {
        url: 'https://cdn.datatables.net/plug-ins/1.11.3/i18n/th.json',
      },
      ajax: (dataTablesParameters: any, callback) => {
        dataTablesParameters.status = this.now_status;
        dataTablesParameters.branch_id = JSON.parse(localStorage.getItem('user') || '{}')?.branch_id;
        that._Service
          .getWarningTable(dataTablesParameters)
          .subscribe((resp: any) => {
            this.dataRow = resp.data;
            this.pages.current_page = resp.current_page;
            this.pages.last_page = resp.last_page;
            this.pages.per_page = resp.per_page;
            if (resp.current_page > 1) {
              this.pages.begin =
                resp.per_page * resp.current_page - 1;
            } else {
              this.pages.begin = 0;
            }
            callback({
              recordsTotal: resp.total,
              recordsFiltered: resp.total,
              data: [],
            });
            this._changeDetectorRef.markForCheck();
          });
      },
      columns: [
        { data: 'active' },
        // { data: 'step' },
        // { data: 'amount' },
        // { data: 'updated_at' }
      ],
    };
  }

  rerender(): void {
    this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
      dtInstance.ajax.reload();
    });
  }

  New() {
    const dialogRef = this._matDialog.open(NewWarningComponent, {
      // width: '50%',
      // minHeight: 'calc(100vh - 90px)',
      // height: 'auto'
      width: '800px',
      height: 'auto',
    });

    dialogRef.afterClosed().subscribe((item) => {
      this.rerender();
      this._changeDetectorRef.markForCheck();
    });
  }

  Edit(id: any) {
    const dialogRef = this._matDialog.open(EditWarningComponent, {
      // width: '50%',
      // minHeight: 'calc(100vh - 90px)',
      // height: 'auto'
      width: '800px',
      height: 'auto',
      data: { id: id },
    });

    dialogRef.afterClosed().subscribe((item) => {
      this.rerender();
      this._changeDetectorRef.markForCheck();
    });
  }

  Delete(id: any) {
    const confirmation = this._fuseConfirmationService.open({
      title: 'ยืนยันลบใบแจ้งเตือน',
      message: 'คุณต้องการลบใบแจ้งเตือนนี้ใช่หรือไม่ ?',
      icon: {
        show: true,
        name: 'heroicons_outline:exclamation-triangle',
        color: 'warning',
      },
      actions: {
        confirm: {
          show: true,
          label: 'ยืนยัน',
          color: 'primary',
        },
        cancel: {
          show: true,
          label: 'ยกเลิก',
        },
      },
      dismissible: true,
    });

    // Subscribe to the confirmation dialog closed action
    confirmation.afterClosed().subscribe((result) => {
      // If the confirm button pressed...
      if (result === 'confirmed') {
        this._Service
          .deleteWarning(id)
          .pipe(takeUntil(this.destroy$))
          .subscribe({
            next: (resp: any) => {
              this.rerender();
            },
            error: (err: any) => {
              this._fuseConfirmationService.open({
                title: 'เกิดข้อผิดพลาด',
                message: err.error.message,
                icon: {
                  show: true,
                  name: 'heroicons_outline:exclamation-triangle',
                  color: 'warning',
                },
                actions: {
                  confirm: {
                    show: false,
                    label: 'ยืนยัน',
                    color: 'primary',
                  },
                  cancel: {
                    show: false,
                    label: 'ยกเลิก',
                  },
                },
                dismissible: true,
              });
            },
          });
      }
    });
  }

  View(id: any) {
    const dialogRef = this._matDialog.open(ViewWarningComponent, {
      // width: '50%',
      // minHeight: 'calc(100vh - 90px)',
      // height: 'auto'
      width: '1000px',
      height: '95%',
      data: { id: id },
    });

    dialogRef.afterClosed().subscribe((item) => {
      this.rerender();
      this._changeDetectorRef.markForCheck();
    });
  }
}
