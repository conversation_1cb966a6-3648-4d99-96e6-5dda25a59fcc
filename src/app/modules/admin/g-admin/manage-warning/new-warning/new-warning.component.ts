import {
  AfterViewInit,
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  OnDestroy,
  OnInit,
  ViewChild,
  ViewEncapsulation,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxChange } from '@angular/material/checkbox';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import {
  debounceTime,
  map,
  merge,
  Observable,
  Subject,
  switchMap,
  takeUntil,
} from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, MatLabel } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { MatRadioGroup, MatRadioButton } from '@angular/material/radio';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
import { ManageWarningService } from '../manage-warning.service';
import { MatAutocomplete, MatOption } from '@angular/material/autocomplete';
import { NgFor, NgIf } from '@angular/common';
import { MatSelect } from '@angular/material/select';
import { PersonnelAutocompleteComponent } from 'app/shared/personnel-autocomplete/personnel-autocomplete.component';
export interface WarningType {
  id: number;
  title: string;
}

export const WARNING_TYPES: WarningType[] = [
  { id: 1, title: 'ใบตักเตือนเรื่องขาดงานโดยไม่แจ้งล่วงหน้า' },
  { id: 2, title: 'ใบตักเตือนเรื่องมาสายเป็นประจำ' },
  { id: 3, title: 'ใบตักเตือนเรื่องลางานเกินจำนวนที่กำหนด' },
  { id: 4, title: 'ใบตักเตือนเรื่องไม่ปฏิบัติตามคำสั่งของหัวหน้างาน' },
  { id: 5, title: 'ใบตักเตือนเรื่องไม่ส่งงานตามกำหนดเวลา' },
  { id: 6, title: 'ใบตักเตือนเรื่องมีพฤติกรรมไม่เหมาะสมในที่ทำงาน' },
  { id: 7, title: 'ใบตักเตือนเรื่องแต่งกายไม่สุภาพตามระเบียบของบริษัท' },
  { id: 8, title: 'ใบตักเตือนเรื่องใช้โทรศัพท์มือถือระหว่างเวลางาน' },
  { id: 9, title: 'ใบตักเตือนเรื่องละเลยหน้าที่การงาน' },
  { id: 10, title: 'ใบตักเตือนเรื่องทะเลาะวิวาทหรือใช้คำพูดไม่สุภาพกับเพื่อนร่วมงาน' },
  { id: 11, title: 'ใบตักเตือนเรื่องสูบบุหรี่ในเขตห้ามสูบ' },
  { id: 12, title: 'ใบตักเตือนเรื่องทำให้เกิดความเสียหายต่อทรัพย์สินบริษัท' },
  { id: 13, title: 'ใบตักเตือนเรื่องละเมิดนโยบายความลับของบริษัท' },
  { id: 14, title: 'ใบตักเตือนเรื่องการใช้เครื่องมือหรืออุปกรณ์ของบริษัทโดยไม่ได้รับอนุญาต' }
];
@Component({
  selector: 'app-new-warning',
  templateUrl: './new-warning.component.html',
  styleUrl: './new-warning.component.scss',
  animations: fuseAnimations,
  imports: [FormsModule, ReactiveFormsModule, MatFormField, MatLabel, MatInput, MatRadioGroup, MatRadioButton, MatButton, MatIcon, MatAutocomplete, NgFor, MatSelect, MatOption, PersonnelAutocompleteComponent, NgIf]
})
export class NewWarningComponent implements OnInit {
  formData: FormGroup;
  customerFilterCtrl: FormControl = new FormControl();
  public UserList: any[];
  warningTypes = WARNING_TYPES
  dataWarning: any;
  dataWarningPunishment: any;
  constructor(
    public dialogRef: MatDialogRef<NewWarningComponent>,
    private _changeDetectorRef: ChangeDetectorRef,
    private _fuseConfirmationService: FuseConfirmationService,
    private _formBuilder: FormBuilder,
    private _matDialog: MatDialog,
    private _router: Router,
    private _activatedRoute: ActivatedRoute,
    private _authService: AuthService,
    private _Service: ManageWarningService,
  ) {
    this.formData = this._formBuilder.group({
      branch_id: JSON.parse(localStorage.getItem('user') || '{}')?.branch_id,
      user_id: [null, Validators.required],
      title: [null, Validators.required],
      title_other: null,
      description: [null, Validators.required],
      punishment: [null, Validators.required],
      punishment_other: null,

    });

    console.log(this.warningTypes, 'WarnType');


    this._Service.getUser().subscribe((resp: any) => {
      this.UserList = resp.data;
      this._changeDetectorRef.markForCheck();
    });
  }

  ngOnInit(): void {
    this._Service.getWarningType().subscribe((resp: any) => {
      this.dataWarning = resp.data
    })
    this._Service.getWarningPunishmentType().subscribe((resp: any) => {
      this.dataWarningPunishment = resp.data
    })
  }

  postWarning() {
    if (this.formData.invalid) {
      this._fuseConfirmationService.open({
        title: 'ข้อมูลไม่ครบถ้วน',
        message: 'กรุณากรอกข้อมูลให้ครบทุกช่องก่อนดำเนินการ',
        icon: {
          show: true,
          name: 'heroicons_outline:exclamation-circle',
          color: 'warn',
        },
        actions: {
          confirm: {
            show: true,
            label: 'ตกลง',
            color: 'warn',
          },
          cancel: {
            show: false,
            label: '',
          },
        },
        dismissible: true,
      });
      return; // หยุดไม่ให้ทำต่อ
    }
    const confirmation = this._fuseConfirmationService.open({
      title: 'สร้างใบเตือน',
      message: 'คุณต้องการสร้างใบเตือนใช่หรือไม่ ?',
      icon: {
        show: true,
        name: 'heroicons_outline:information-circle',
        color: 'info',
      },
      actions: {
        confirm: {
          show: true,
          label: 'ยืนยัน',
          color: 'primary',
        },
        cancel: {
          show: true,
          label: 'ยกเลิก',
        },
      },
      dismissible: true,
    });

    // Subscribe to the confirmation dialog closed action
    confirmation.afterClosed().subscribe((result) => {
      // If the confirm button pressed...
      if (result === 'confirmed') {
        this._Service.postWarning(this.formData.value).subscribe({
          next: (resp: any) => {
            this.dialogRef.close();
          },
          error: (err: any) => {
            this._fuseConfirmationService.open({
              title: 'เกิดข้อผิดพลาด',
              message: err.error.message,
              icon: {
                show: true,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
              },
              actions: {
                confirm: {
                  show: false,
                  label: 'ยืนยัน',
                  color: 'primary',
                },
                cancel: {
                  show: false,
                  label: 'ยกเลิก',
                },
              },
              dismissible: true,
            });
          },
        });
      }
    });
  }

  onClose(): void {
    this.dialogRef.close();
  }

  onPersonnelSelected(selectedPersonnel: any): void {
    this.formData.patchValue({
      user_id: selectedPersonnel.id,
    })
  }
}
