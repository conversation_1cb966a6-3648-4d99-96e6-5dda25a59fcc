<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">
    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">คอมมิชชั่น</div>
        <!-- Actions -->
        <a (click)="New()" class="ml-4" mat-flat-button [color]="'primary'" *ngIf="!hiddenSave()">
            <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
            <span class="ml-2 mr-1">สร้างคอมมิชชั่น</span>
        </a>
    </div>
    <!-- Main -->
    <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

        <div class="flex flex-col p-3 sm:p-6">
            <div class="overflow-auto  bg-white shadow sm:rounded-lg">
                <!-- Products list -->

                <div class="flex flex-col flex-auto p-5 sm:overflow-auto overflow-x-scroll">

                    <table datatable [dtOptions]="dtOptions"
                    class="table w-full text-left text-gray-500 overflow-hidden">
                    <thead
                    class="bg-gray-300 text-black">
                            <tr>
                                <th>จัดการค่าคอม</th>
                                <th>ลำดับ</th>
                                <th>ตำแหน่ง</th>
                                <th>เริ่ม</th>
                                <th>สิ้นสุด</th>
                                <th>เปอร์เซ็น</th>
                            </tr>
                        </thead>
                        <tbody *ngIf="dataRow?.length > 0">
                            <tr *ngFor="let item of dataRow; let i = index"
                                class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-md">
                                <td style="min-width: 80px;">
                                    <button mat-icon-button (click)="Edit(item.id)" title="เพิ่มข้อมูล"
                                        [disabled]="hiddenEdit()">
                                        <mat-icon>edit</mat-icon>
                                    </button>
                                    <button mat-icon-button (click)="Delete(item.id)" title="เพิ่มข้อมูล"
                                        [disabled]="hiddenDelete()">
                                        <mat-icon>delete</mat-icon>
                                    </button>
                                </td>
                                <td style="min-width: 80px;">{{ pages.begin + (i + 1) }}</td>
                                <!-- <td> {{item.position_id == 4? 'ทีมเทเลเซล':item.position_id==3?
                                    'ทีมแอดมินตอบแชท':item.position_id==2? 'ทีมยิงแอดโฆษณา':item.position_id==1?
                                    'นักพัฒนา':item.position_id}} </td> -->
                                <td style="min-width: 80px;">{{ item.position.name }}</td>
                                <td> {{item.start}} </td>
                                <td>{{ item.end }}
                                </td>
                                <td>{{ item.percent }}</td>
                            </tr>
                        </tbody>
                        <tbody *ngIf="dataRow?.length == 0">
                            <tr>
                                <td colspan="8" class="no-data-available text-center">ไม่มีข้อมูล !</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

</div>
