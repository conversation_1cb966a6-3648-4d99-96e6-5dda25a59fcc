import { Route, Routes } from '@angular/router';
// import { CreateUserComponent } from './create-user/create-user.component';
// import { UserListComponent } from './list/list.component';




// import { AssetTypeResolver, PermissionProductsResolver } from './user.resolvers';


export default [
    // {
    //     path: '',
    //     pathMatch: 'full',
    //     redirectTo: 'brief-plan'
    // },
    {
        path: '',
        loadComponent: () => import('./commission.component').then(m => m.CommissionComponent),
        children: [
            {
                path: 'list',
                loadComponent: () => import('./list/list.component').then(m => m.ListComponent),

            },
            {
                path: 'new-commission',
                loadComponent: () => import('./new-commission/new-commission.component').then(m => m.NewCommissionComponent),

            },
            {
                path: 'edit/:id',
                loadComponent: () => import('./edit-commission/edit-commission.component').then(m => m.EditCommissionComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },


        ]

        /*children : [
            {
                path     : '',
                component: ContactsListComponent,
                resolve  : {
                    tasks    : ContactsResolver,
                    countries: ContactsCountriesResolver
                },
                children : [
                    {
                        path         : ':id',
                        component    : ContactsDetailsComponent,
                        resolve      : {
                            task     : ContactsContactResolver,
                            countries: ContactsCountriesResolver
                        },
                        canDeactivate: [CanDeactivateContactsDetails]
                    }
                ]
            }
        ]*/
    }
] as Routes
