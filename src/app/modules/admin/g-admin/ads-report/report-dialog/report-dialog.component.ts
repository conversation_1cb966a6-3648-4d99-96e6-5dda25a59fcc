import { Component, Inject, OnInit ,ViewEncapsulation} from '@angular/core';
import { MAT_DIALOG_DATA, MatDialogRef, MatDialogTitle, MatDialogContent, MatDialogActions, MatDialogClose } from '@angular/material/dialog';
import { FormBuilder, FormGroup } from '@angular/forms';

import {default as _rollupMoment, Moment} from 'moment';
import {Mat<PERSON><PERSON>picker, MatDatepickerModule} from '@angular/material/datepicker';
import {MatInputModule} from '@angular/material/input';
import {MatFormFieldModule} from '@angular/material/form-field';
import {MomentDateAdapter} from '@angular/material-moment-adapter';
import {FormControl, FormsModule, ReactiveFormsModule} from '@angular/forms';
import _moment from 'moment';
import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { HttpClient } from '@angular/common/http';
import { AdminReportService } from '../ads-report.service';
import { environment } from 'environments/environment';
import { CdkScrollable } from '@angular/cdk/scrolling';
import { NgFor } from '@angular/common';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';

const moment: typeof _moment = _rollupMoment || _moment;
export const MY_FORMATS = {
    parse: {
      dateInput: 'MM/YYYY',
    },
    display: {
      dateInput: 'MM/YYYY',
      monthYearLabel: 'MMM YYYY',
      dateA11yLabel: 'LL',
      monthYearA11yLabel: 'MMMM YYYY',
    },
  };
@Component({
    selector: 'app-report-dialog',
    templateUrl: './report-dialog.component.html',
    styleUrls: ['./report-dialog.component.scss'],
    providers: [
        { provide: DateAdapter, useClass: MomentDateAdapter, deps: [MAT_DATE_LOCALE] },
        { provide: MAT_DATE_FORMATS, useValue: MY_FORMATS },
    ],
    encapsulation: ViewEncapsulation.None,
    imports: [MatDialogTitle, CdkScrollable, MatDialogContent, NgFor, MatDialogActions, MatButton, MatDialogClose, MatIcon]
})
export class ReportDialogComponent implements OnInit {
    dateForm: FormGroup;
    date = new FormControl(moment());
    constructor(
        @Inject(MAT_DIALOG_DATA) public data: any,
        private dialogRef: MatDialogRef<ReportDialogComponent>,
        private fb: FormBuilder,
        private http: HttpClient,
        private _Service: AdminReportService,
    ) {
    }

    ngOnInit(): void {
    }


    onCancel(): void {
        this.dialogRef.close();
    }

}
