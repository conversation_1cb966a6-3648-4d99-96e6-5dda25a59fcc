<h2 mat-dialog-title>{{data.page?.page?.name}}</h2>
<div class="summary-info">
    <h5>Order ทั้งหมด: <font color="blue">{{data.page?.page_count_order}}</font></h5>
    <h5>ยอดขาย ทั้งหมด: <font color="green">{{data.page?.page_total}}</font></h5>
    <h5>ค่าคอมมิชชั่น ทั้งหมด: <font color="red">{{data.page?.page_comission}}</font></h5>
  </div>
<mat-dialog-content class="scrollable-content">
    <table class="min-w-full">
        <thead class="bg-white border-b">
            <tr>
                <th scope="col" class="text-l font-large text-gray-900 px-6 py-4 text-left">
                    ลำดับ
                </th>
                <th scope="col" class="text-l font-large text-gray-900 px-6 py-4 text-left">
                    ชื่อสินค้า
                </th>
                <th scope="col" class="text-l font-large text-gray-900 px-6 py-4 text-left">
                    จำนวนออเดอร์
                </th>
                <th scope="col" class="text-l font-large text-gray-900 px-6 py-4 text-left">
                    ยอดขาย
                </th>
                <th scope="col" class="text-l font-large text-gray-900 px-6 py-4 text-left">
                    ค่าคอมมิชชั่น
                </th>
      </tr>
    </thead>
    <tbody>
      <tr class="bg-gray-100 border-b" *ngFor="let item of data.page?.item; let i = index">
        <td class="text-sm text-gray-900 font-light px-6 py-4 whitespace-nowrap text-center">{{i + 1}}</td>
<td class="text-sm text-gray-900 font-light px-6 py-4 whitespace-nowrap text-center">{{item.item.name ?? '-'}}</td>
<td class="text-sm text-gray-900 font-light px-6 py-4 whitespace-nowrap text-center">{{item.sum_qty ?? '-'}}</td>
<td class="text-sm text-gray-900 font-light px-6 py-4 whitespace-nowrap text-center">{{item.sum_total ?? '-'}}</td>
<td class="text-sm text-gray-900 font-light px-6 py-4 whitespace-nowrap text-center">{{item.commission ?? '-'}}</td>
      </tr>
    </tbody>
  </table>
</mat-dialog-content>
<mat-dialog-actions align="end">
  <button class="px-6 ml-3 mat-primary" mat-flat-button mat-dialog-close>
    <mat-icon svgIcon="heroicons_outline:x-mark"></mat-icon>
    ปิด
</button>
</mat-dialog-actions>
