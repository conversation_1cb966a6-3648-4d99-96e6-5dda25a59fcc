import {
    After<PERSON><PERSON>wInit,
    ChangeDetector<PERSON><PERSON>,
    <PERSON>mponent,
    On<PERSON><PERSON>roy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { FormGroup, FormBuilder, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MAT_DATE_FORMATS } from '@angular/material/core';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { DataTableDirective } from 'angular-datatables';
import { Subject, lastValueFrom } from 'rxjs';
import { AdminReportService } from '../ads-report.service';
import { DatePipe, NgIf, NgFor, DecimalPipe } from '@angular/common';
import { ReportDialogComponent } from '../report-dialog/report-dialog.component';
import { DialogAdsSaleOrderComponent } from '../dialog/dialog.component';
import { <PERSON><PERSON>rogressBar } from '@angular/material/progress-bar';
import { MatFormField, MatSuffix } from '@angular/material/form-field';
import { MatSelect } from '@angular/material/select';
import { MatOption } from '@angular/material/autocomplete';
import { MatInput } from '@angular/material/input';
import { MatDatepickerInput, MatDatepickerToggle, MatDatepicker } from '@angular/material/datepicker';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
export const MY_DATE_FORMATS = {
    parse: {
        dateInput: 'DD/MM/YYYY',
    },
    display: {
        dateInput: 'DD/MM/YYYY',
        monthYearLabel: 'MMMM YYYY',
        dateA11yLabel: 'LL',
        monthYearA11yLabel: 'MMMM YYYY',
    },
};
@Component({
    selector: 'app-list',
    templateUrl: './list.component.html',
    styleUrls: ['./list.component.scss'],
    animations: fuseAnimations,
    providers: [{ provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMATS }],
    imports: [NgIf, MatProgressBar, FormsModule, ReactiveFormsModule, MatFormField, MatSelect, NgFor, MatOption, MatInput, MatDatepickerInput, MatDatepickerToggle, MatSuffix, MatDatepicker, MatButton, MatIcon, DecimalPipe, DatePipe]
})
export class ListComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild(DataTableDirective)
    dtElement!: DataTableDirective;
    public dtOptions: DataTables.Settings = {};
    public dataRow: any[];
    private destroy$ = new Subject<any>();
    totalRowSummary: any;
    flashMessage: 'success' | 'error' | null = null;
    isLoading: boolean = false;
    selectedLocation = '';
    selectedItemType = '';
    selectedItem = '';
    itemtypeData: any = [];
    itemData: any = [];
    userData: any = [];
    filterForm: FormGroup;
    configaddress: any;
    Page: any = [];
    items: any[] = []
    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
        private _Service: AdminReportService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute
    ) {

    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    async ngOnInit(): Promise<void> {
        this.filterForm = this._formBuilder.group({
            user_id: '',
            date_start: '',
            date_end: '',
            channal: '',
            page_id: []
        });


        // this.GetReport();

        // this.loadTable();
        this._Service.getPage().subscribe((res: any) => {
            this.Page = res.data;
        });
        this._Service.getConfig().subscribe((res: any) => {
            this.configaddress = res.company_address;
        });
        this._Service.getUser().subscribe((res: any) => {
            this.userData = res.data;
        });
    }

    /**
     * After view init
     */
    ngAfterViewInit(): void { }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        // this._unsubscribeAll.next(null);
        // this._unsubscribeAll.complete();
    }

    onChangeItemType(e): void {
        this._Service.getByItemType(e).subscribe((resp: any) => {
            this.itemData = resp.data;
            // this.rawDataFilter = this.dataRow
        });
    }

    showFlashMessage(type: 'success' | 'error'): void {
        // Show the message
        this.flashMessage = type;
        // Mark for check
        this._changeDetectorRef.markForCheck();

        // Hide it after 3 seconds
        setTimeout(() => {
            this.flashMessage = null;

            // Mark for check
            this._changeDetectorRef.markForCheck();
        }, 3000);
    }

    pages = { current_page: 1, last_page: 1, per_page: 10, begin: 0 };

    rerender(): void {
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            dtInstance.ajax.reload();
        });
    }
    openDialog(data: any): void {
        const dialogRef = this._matDialog.open(DialogAdsSaleOrderComponent, {
            width: '900px',
            maxHeight: '90vh',
            data: data
        });
        dialogRef.afterClosed().subscribe(result => {

        }
        );
    }
    print(): void {
        let printContents, popupWin;
        printContents = document.getElementById('print-section').innerHTML;
        popupWin = window.open(
            'top=0,left=0,height=100%,width=auto'
        );
        popupWin.document.open();
        popupWin.document.write(`
        <html>
          <head>
            <style>
            @media print {
              @page {
                  size: A4 portrait !important;
              }

              body {
                  writing-mode: horizontal-tb;
              }

              .is-print {
                  display: inline-block;
                  width: 100%;
              }

              .no-print {
                  display: none;
              }

              body,
              .page-body {
                  // margin: 10!important;
                  box-shadow: 0;
              }

              body {
                  background: rgb(204, 204, 204);
              }

              .container-fluid {
                  padding: 0 !important;
                  margin-left: 0px !important;
              }

              .container {
                  padding: 0px !important;
                  margin: 0px !important;
              }

              .page-body {
                  background: white;
                  display: block;
                  margin-bottom: 0.5cm;
                  margin: auto;
                  margin-top: 5.5rem;
                  box-shadow: 0 0 0.5cm rgba(0, 0, 0, 0.5);
                  padding: 0.5cm;
              }

              .page-body {
                  width: 100%;
                  height: auto;
                  padding: 0.5cm;
              }

              table {
                  width: 100%;
              }

              table,
              th,
              td {
                  font-size: 10px;
                  padding: 3px;
                  border: 1px solid black;
              }

              @page {
                  size: auto
              }
                   h1.title {
                        text-align: center;
                        font-size: 20px;
                        margin-bottom: 20px;
                    }
                             .logo {
                    width: 100px; /* Adjust width as needed */
                    height: auto; /* Maintain aspect ratio */
                }
                         .card1 {
                        text-align: center;
                        margin-bottom: 20px;
                    }
          }
            </style>

          </head>
      <body onload="window.print();window.close()">
              <div class="card1">
                <img src="/assets/images/logo/globalrichnewlogo.png" class="logo" >
                <p class="font-bold" style="font-size: 16px;">บริษัท แกรนด์ โกลบอล ริช จำกัด<br /></p>
                <p style="font-size: 14px;">${this.configaddress}</p>
              </div>
              <h1 class="title">รายงานคงเหลือสินค้าตามสถานที่จัดเก็บ</h1>
              ${printContents}
            </body>
        </html>`);
        popupWin.document.close();
        // if (window.onafterprint) {
        //     this.router.navigate(['manufacture/injection/report/' + this.injectionId]);
        // }
        // else {
        //     this.router.navigate(['manufacture/injection/report/' + this.injectionId]);
        // }

        // this.router.navigate(['manufacture/injection/report/' + this.injectionId]);
    }

    onFilter() {
        this.rerender();
    }

    genExcel() {
        window.open(
            'https://wmk1.net/api/public/api/exportExcel?bank_id=' +
            this.filterForm.value.bank_id +
            '&text=' +
            this.filterForm.value.text +
            '&start_date=' +
            this.filterForm.value.start_date +
            '&end_date=' +
            this.filterForm.value.end_date +
            '&type=' +
            this.filterForm.value.type
        );
    }

    totalPriceTable() {
        let total = 0;
        for (let data of this.dataRow) {
            total += Number(data.price);
        }
        return total;
    }

    genPDF() {
        window.open(
            'https://wmk1.net/api/public/api/genPDF?bank_id=' +
            this.filterForm.value.bank_id +
            '&text=' +
            this.filterForm.value.text +
            '&start_date=' +
            this.filterForm.value.start_date +
            '&end_date=' +
            this.filterForm.value.end_date +
            '&type=' +
            this.filterForm.value.type
        );
    }

    GetReport(): void {
        const datePipe = new DatePipe("en-US");
        const datestart = datePipe.transform(
            this.filterForm.value.date_start,
            "yyyy-MM-dd"
        );
        const dateend = datePipe.transform(
            this.filterForm.value.date_end,
            "yyyy-MM-dd"
        );
        this.filterForm.patchValue({
            date_start: datestart,
            date_end: dateend,
        })
        const formValue = this.filterForm.value;

        Object.keys(formValue).forEach((key) => {
            if (formValue['page_id'] === null) {
                formValue['page_id'] = [];
            }
        });

        this._Service.getReport(formValue).subscribe((resp: any) => {
            this.dataRow = resp.data;
            this._changeDetectorRef.markForCheck();
        });
    }
}
