.text-link {
    color: #007bff; /* Modern, clickable color */
    font-weight: 600;
    text-decoration: underline;
    cursor: pointer;
    transition: all 0.2s ease-in-out;

    &:hover {
      color: #0056b3; /* Slightly darker on hover */
      text-decoration: none;
      transform: scale(1.05); /* Slightly larger on hover */
    }

    &::after {
      content: ' →'; /* Adds an arrow to indicate interaction */
      font-size: 0.9em;
      color: #007bff;
      transition: all 0.2s ease-in-out;
    }

    &:hover::after {
      color: #0056b3;
    }
  }
