<div>
    <div class="flex flex-row justify-between border-b-2 border-gray-300">
        <table class="w-full text-md text-left rtl:text-right text-gray-500 dark:text-gray-400 border-2 border-black">
            <thead class="text-md text-gray-700 uppercase bg-gray-50 dark:bg-gray-700 dark:text-gray-400">
                <tr class="border-2 border-black">
                    <th scope="col" class="px-2 py-2 w-1/12">
                    </th>
                    <th scope="col" class="px-2 py-2 w-4/12">
                        สินค้า
                    </th>
                    <th scope="col" class="px-2 py-2 w-2/12 text-right">
                        จำนวนรวม
                    </th>
                    <th scope="col" class="px-2 py-2 w-2/12 text-right">
                       ยอดขายรวม
                    </th>
                    <th scope="col" class="px-2 py-2 w-1/12 text-right">
                        ค่า COD
                    </th>
                    <th scope="col" class="px-2 py-2 w-2/12 text-right">
                        ค่าขนส่ง
                    </th>
                </tr>

            </thead>
            <tbody>
                <ng-container *ngFor="let item of itemData?.sale_order_line; let i = index">
   
                    <tr [ngClass]="'border-2 border-black bg-green-100 '">
                        <td class="px-2 py-2 text-md">
                            {{i + 1}}
                        </td>
                        <td class="px-2 py-2 text-md">
                            {{ item?.item?.name ?? '-' }} SKU : {{ item?.item?.sku ?? '-' }}
                        </td>
                        <td class="px-2 py-2 text-md text-right">
                            {{ (item?.sum_qty | number )?? 0 }}
                        </td>
                        <td class="px-2 py-2 text-md text-right">
                            {{ item?.sum_total  | number : '1.2' ?? '-' }}
                        </td>
                        <td class="px-2 py-2 text-md text-right">
                            {{ item?.sum_cod  | number : '1.2' ?? '-' }}
                        </td>
                        <td class="px-2 py-2 text-md text-right">
                            {{ item?.sum_shipping  | number : '1.2' ?? '-' }}
                        </td>
                    </tr>
                </ng-container>


            </tbody>
        </table>
    </div>
</div>
