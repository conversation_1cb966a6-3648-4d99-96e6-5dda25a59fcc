import { Route, Routes } from '@angular/router';




export default [
    {
        path: '',
        loadComponent: () => import('./advancemoney.component').then(m => m.AdvancemoneyComponent),
        children: [
            {
                path: 'list',
                loadComponent: () => import('./list/list.component').then(m => m.ListComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },
            {
                path: 'edit/:id',
                loadComponent: () => import('./edit/edit.component').then(m => m.EditComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },
        ],
    },
] as Routes
