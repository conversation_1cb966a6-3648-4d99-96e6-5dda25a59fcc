<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">รายการเบิกเงินเดือนล่วงหน้า</div>
        <!-- Actions -->
    </div>
    <!-- Main -->
    <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

        <div class="flex flex-col p-3 sm:p-6">
            <div class="overflow-auto  bg-white shadow sm:rounded-lg">
                <!-- Products list -->
                <div class="flex flex-col flex-auto overflow-auto sm:overflow-y-auto">
                    <form [formGroup]="filterForm">
                        <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent ">
                            <div class="flex flex-row p-3 sm:p-2 bg-card">
                                <div class="flex flex-col md:flex-row gap-4 w-full p-4">
                                    <div class="flex flex-col w-1/3">
                                        <ng-container *ngIf="UserList">
                                            <mat-label class="font-bold">เลือกพนักงาน</mat-label>
                                            <app-personnel-autocomplete [itemtypeData]="UserList"
                                                (personnelSelected)="onPersonnelSelected($event)">
                                            </app-personnel-autocomplete>
                                        </ng-container>
                                    </div>
                                </div>
                                <div class="flex mt-0 sm:mt-6 md:mt-6 justify-end px-4 gap-2">
                                    <button mat-flat-button
                                        class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                                        (click)="GetReport()">
                                        <mat-icon [svgIcon]="'heroicons_outline:magnifying-glass'"></mat-icon>
                                        <span class="ml-2 mr-1">ค้นหา</span>
                                    </button>
                                    <button mat-flat-button
                                        class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                                        type="reset">
                                        <mat-icon [svgIcon]="'heroicons_outline:arrow-path'"></mat-icon>
                                        <span class="ml-2 mr-1">ล้าง</span>
                                    </button>
                                </div>

                            </div>
                        </div>
                    </form>
                </div>
                <div class="flex flex-col flex-auto p-5 sm:overflow-auto overflow-x-scroll">
                    <table datatable [dtOptions]="dtOptions"
                        class="table w-full text-left text-gray-500 overflow-hidden">
                        <thead class="bg-gray-300 text-black">
                            <tr>
                                <th>จัดการ</th>
                                <th>ลำดับ</th>
                                <th>ชื่อ - นามสกุล</th>
                                <th>จำนวนเงิน</th>
                                <th>รายละเอียด</th>
                                <th>เดือนที่เบิก</th>
                                <th>วันที่แจ้ง</th>
                                <th>สถานะ</th>
                            </tr>
                        </thead>
                        <tbody *ngIf="dataRow?.length != 0">
                            <tr *ngFor="let item of dataRow; let i = index"
                                class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-md py-15">
                                <td>
                                    <button mat-button (click)="Edit(item)" [disabled]="hiddenEdit()">
                                        <mat-icon>edit</mat-icon>
                                    </button>
                                </td>
                                <td style="width: 5%;">{{ pages.begin + (i + 1) }}</td>
                                <td> {{item.user ? item.user?.first_name: '-'}} {{item.user ? item.user?.last_name:
                                    '-'}}</td>
                                <td> {{item?.qty ?? "-" }} </td>
                                <td> {{item?.objective ?? "-" }} </td>
                                <td> {{item?.month_withdraw ?? "-" }} </td>
                                <td>
                                    {{
                                    item.created_at !== null
                                    ? (item.created_at | date : "dd-MM-yyyy")
                                    : "-"
                                    }}
                                </td>
                                <td>
                                    <div class=" text-blue-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-blue-200 dark:blue-green-900"
                                        *ngIf="item.status === 'open'">
                                        รออนุมัติ
                                    </div>
                                    <div class=" text-green-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                                        *ngIf="item.status === 'approved'">
                                        อนุมัติ
                                    </div>
                                    <div class=" text-red-500 text-md font-semibold mr-2 px-2.5 py-0.5 rounded dark:bg-green-200 dark:text-green-900"
                                        *ngIf="item.status === 'cancel'">
                                        ไม่อนุมัติ
                                    </div>
                                </td>
                            </tr>
                        </tbody>
                        <tbody *ngIf="dataRow?.length == 0">
                            <tr>
                                <td colspan="12" class="no-data-available text-center">ไม่มีข้อมูล !</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>