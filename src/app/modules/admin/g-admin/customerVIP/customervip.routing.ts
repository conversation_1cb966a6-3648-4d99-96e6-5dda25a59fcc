import { Route, Routes } from "@angular/router";




export default [
    // {
    //     path: '',
    //     pathMatch: 'full',
    //     redirectTo: 'brief-plan'
    // },
    {
        path: '',
         loadComponent: () => import('./customervip.component').then(m => m.CustomerVipComponent),
        children: [
            {
                path: 'list',
                loadComponent: () => import('./list/list.component').then(m => m.ListCustomerVipComponent),

            },
            // {
            //     path: 'new-commission',
            //     component: NewCommissionComponent,

            // },
            // {
            //     path: 'edit/:id',
            //     component: EditCommissionComponent,
            //     // resolve: {
            //     //     products: PermissionProductsResolver,

            //     // }
            // },


        ]

        /*children : [
            {
                path     : '',
                component: ContactsListComponent,
                resolve  : {
                    tasks    : ContactsResolver,
                    countries: ContactsCountriesResolver
                },
                children : [
                    {
                        path         : ':id',
                        component    : ContactsDetailsComponent,
                        resolve      : {
                            task     : ContactsContactResolver,
                            countries: ContactsCountriesResolver
                        },
                        canDeactivate: [CanDeactivateContactsDetails]
                    }
                ]
            }
        ]*/
    }
] as Routes
