<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">จัดการลูกค้า VIP</div>
        <!-- Actions -->
    </div>

    <div class="flex md:flex-row w-full min-w-0 bg-gray-100  px-6 pt-6">
        <div class="grid justify-center w-full min-w-0 gap-2 grid-cols-1 sm:grid-cols-2 md:grid-cols-4">
            <div
                class="flex flex-col flex-auto p-6 overflow-hidden shadow rounded-2xl bg-card border-4 border-orange-500">
                <div class="flex items-start justify-between">
                    <div class="text-lg font-bold leading-6 tracking-tight truncate">ลูกค้า VIP ทั้งหมด</div>
                </div>
                <div class="flex flex-col items-end mt-2 grow justify-end">
                    <div class="flex font-bold leading-none tracking-tight text-orange-600 h-auto md:text-4xl">
                        {{ (this.countData ?? 0 ) | number }} </div>
                </div>
            </div>


        </div>
    </div>
    <!-- Main -->
    <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

        <div class="flex flex-col p-3 sm:p-6">
            <div class="overflow-auto  bg-white shadow sm:rounded-lg">
                <!-- Products list -->
                <div class="flex flex-auto p-5 sm:items-center sm:justify-between border-b">
                    <div class="text-2xl font-extrabold ">ข้อมูลลูกค้า VIP</div>

                    <div class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4">


                    </div>
                </div>

                <div class="flex flex-col flex-auto p-5 sm:overflow-auto overflow-x-scroll">
                    <form [formGroup]="formData">
                        <div class="flex justify-between">
                            <mat-form-field class="w-1/3 pr-2">
                                <mat-select formControlName="user_id">
                                    <mat-option [value]="''">
                                        เลือกพนักงาน
                                    </mat-option>
                                    <mat-option *ngFor="let item of Employee" [value]="item.id">
                                        {{item.first_name}} {{item.last_name}}
                                    </mat-option>
                                </mat-select>
                            </mat-form-field>
                        
                            <div class="flex mt-0 justify-end px-4 gap-2">
                                <button mat-flat-button
                                    class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                                    (click)="this.rerender()">
                                    <mat-icon [svgIcon]="'heroicons_outline:magnifying-glass'"></mat-icon>
                                    <span class="ml-2 mr-1">ค้นหา</span>
                                </button>
                                <button mat-flat-button
                                    class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4"
                                    type="reset">
                                    <mat-icon [svgIcon]="'heroicons_outline:arrow-path'"></mat-icon>
                                    <span class="ml-2 mr-1">ล้าง</span>
                                </button>
                                <button mat-flat-button
                                    class="bg-red-700 text-white-700 hover:bg-red-500 dark:bg-red-600 dark:text-white-200 dark:hover:bg-white-500 rounded-lg py-2 px-4"
                                    (click)="tranvip()" *ngIf="!hiddenSave()"
                                    >
                                    <mat-icon class="px-2"> transfer_within_a_station</mat-icon>
                                    <span class="ml-2 mr-1">ย้ายลูกค้า VIP</span>
                                </button>
                                <button mat-flat-button
                                class="bg-red-700 text-white-700 hover:bg-red-500 dark:bg-red-600 dark:text-white-200 dark:hover:bg-white-500 rounded-lg py-2 px-4"
                                (click)="create()" *ngIf="!hiddenSave()"
                                >
                                <mat-icon svgIcon="heroicons_solid:plus"></mat-icon>
                                <span class="ml-2 mr-1">เพิ่มลูกค้า VIP</span>
                            </button>
                            </div>
                        </div>
                    </form>
                    <div class="table-responsive overflow-auto my-4">
                        <table datatable [dtOptions]="dtOptions"
                        class="table w-full text-left text-gray-500 overflow-hidden">
                        <thead
                        class="bg-gray-300 text-black">
                                <tr>
                                    <th>ลำดับ</th>
                                    <th>ชื่อลูกค้า</th>
                                    <th>พนักงานที่ดูแล</th>
                                    <th>อีเมล</th>
                                    <th>เบอร์ติดต่อ</th>
                                    <th>วันเกิด</th>
                                    <th>ลบลูกค้า</th>
                                </tr>
                            </thead>
                            <tbody *ngIf="dataRow?.length != 0">
                                <tr *ngFor="let item of dataRow; let i = index">
                                    <td style="min-width: 60px; width: 60px;">
                                        {{ (i + 1) }}
                                    </td>
                                    <td style="min-width: 200px">
                                        {{ item.customer[0].name }}
                                    </td>
                                    <td style="min-width: 200px;">{{ item.user[0].account_name }}</td>
                                    <td style="min-width: 200px;">{{ item.customer[0].email }}</td>
                                    <td style="min-width: 150px;">{{ item.customer[0].phone }}</td>
                                    <td style="min-width: 150px;">{{ item.customer[0].birth_day ? item.customer[0].birth_day : 'ไม่ได้ระบุไว้' }}</td>
                                    <td style="min-width: 80px">
                                        <button class="text-red-600" mat-stroked-button title="ลบลูกค้า" (click)="deleteCus(item.id)"
                                            [disabled]="hiddenEdit()">
                                            <mat-icon color="error">delete</mat-icon>&nbsp;ลบลูกค้า</button>
                                    </td>
                                </tr>
                            </tbody>
                            <tbody *ngIf="dataRow?.length == 0">
                                <tr>
                                    <td colspan="6" class="no-data-available text-center">ไม่มีข้อมูล !</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>

            </div>
        </div>
    </div>

</div>
