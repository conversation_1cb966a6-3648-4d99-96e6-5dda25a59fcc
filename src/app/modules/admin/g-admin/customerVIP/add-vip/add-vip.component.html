<div class="flex flex-col flex-auto min-w-0 bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between p-1 sm:pb-4 sm:px-10 border-b bg-card dark:bg-transparent">
        <div class="flex-1 min-w-0">
            <!-- Title -->
            <div class="mt-2">
                <h2 class="text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                    เพิ่มลลูกค้า VIP
                </h2>
            </div>

        </div>
    </div>

    <!-- Main -->
    <div class="flex-auto p-3 sm:p-2">
        <form [formGroup]="formData">

            <mat-form-field class="w-full pr-2">
                <mat-label class="text-xl">เลือกลูกค้า</mat-label>
                <mat-select formControlName="customer_id">
                    <mat-option *ngFor="let item of Customer" [value]="item.id">
                        {{item.name}}
                    </mat-option>
                </mat-select>
            </mat-form-field>
            <mat-form-field class="w-full pr-2">
                <mat-label class="text-xl">เลือกพนักงาน</mat-label>
                <mat-select formControlName="user_id">
                    <mat-option *ngFor="let item of Employee" [value]="item.id">
                        {{item.first_name}} {{item.last_name}}
                    </mat-option>
                </mat-select>
            </mat-form-field>
            <div class="flex items-center justify-end w-full border-t px-8 py-4 mt-4">
                <div class="flex items-center justify-end">
                    <button mat-flat-button class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4" (click)="onClose()">
                        <mat-icon svgIcon="heroicons_solid:x-mark" class="mr-2"></mat-icon>
                        ยกเลิก
                    </button>
                    <button class="px-6 ml-3 mat-primary" mat-flat-button (click)="New()">
                        <mat-icon svgIcon="heroicons_solid:check"></mat-icon>
                        ยืนยัน
                    </button>
                </div>
            </div>
        </form>
    </div>
</div>
