import { HttpClient } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { DataTablesResponse } from 'app/shared/datatable.types';
import { environment } from 'environments/environment';
import { Observable, of, switchMap } from 'rxjs';

@Injectable({
    providedIn: 'root'
})
export class BorrowingTypeService {

    private _http = inject(HttpClient);

    constructor() { }

    getDataPage(
        dataTablesParameters: any
    ): Observable<DataTablesResponse> {
        return this._http
            .post(
                environment.API_URL + 'api/equipment/categories/page',
                dataTablesParameters
            )
            .pipe(
                switchMap((response: any) => {
                    return of(response);
                })
            );
    }

    // Get users for dropdown
    getUsers(): Observable<any> {
        return this._http.get(environment.API_URL + 'api/get_user');
    }

    // Get equipments for dropdown
    getEquipments(): Observable<any> {
        return this._http.get(environment.API_URL + 'api/equipment/equipments');
    }

    // Create new borrowing
    createBorrowing(data: any): Observable<any> {
        return this._http.post(environment.API_URL + 'api/equipment/borrowings', data);
    }

    // Update borrowing
    updateBorrowing(id: number, data: any): Observable<any> {
        return this._http.put(environment.API_URL + 'api/equipment/borrowings/' + id, data);
    }

    // Get borrowing by ID
    getBorrowing(id: number): Observable<any> {
        return this._http.get(environment.API_URL + 'api/equipment/borrowings/' + id);
    }

    // Delete borrowing
    deleteBorrowing(id: number): Observable<any> {
        return this._http.delete(environment.API_URL + 'api/equipment/borrowings/' + id);
    }

    approveBorrowing(id: number): Observable<any> {
        return this._http.post(environment.API_URL + 'api/equipment/borrowings/' + id + '/approve', {});
    }

    markBorrowed(id: number): Observable<any> {
        return this._http.post(environment.API_URL + 'api/equipment/borrowings/' + id + '/mark-borrowed', {});
    }

    returnBorrowing(id: number): Observable<any> {
        return this._http.post(environment.API_URL + 'api/equipment/borrowings/' + id + '/return', {});
    }

    cancelBorrowing(id: number, reason: string): Observable<any> {
        return this._http.post(environment.API_URL + 'api/equipment/borrowings/' + id + '/cancel', {
            reason: reason
        });
    }
}
