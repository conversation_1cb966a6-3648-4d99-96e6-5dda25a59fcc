import { ChangeDetectorRef, Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormBuilder, FormGroup, ReactiveFormsModule, Validators } from '@angular/forms';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatSelectModule } from '@angular/material/select';
import { MatRadioModule } from '@angular/material/radio';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { MatNativeDateModule } from '@angular/material/core';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MasterDataService } from 'app/shared/master-data.service';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import moment from 'moment';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDialog } from '@angular/material/dialog';
import { DialogDeletemoneyComponent } from '../../deletemoney/dialog-add/dialog-add.component';
import { DialogPlusemoneyComponent } from '../../plusmoney/dialog-add/dialog-add.component';
import { PageService } from '../page.services';

export interface Permission { id: number; name: string; }
export interface Department { id: number; name: string; }
export interface Position { id: number; name: string; }
export interface Employee { id: number; first_name: string; last_name: string }
export interface WorkShift { id: number; name: string; }
export interface Branch { id: number; name: string; }




interface MasterData {
  permissions: Permission[];
  departments: Department[];
  positions: Position[];
  employees: Employee[];
  workShifts: WorkShift[];
  branchs: Branch[];
}

// export const MY_DATE_FORMATS = {
//   parse: {
//     dateInput: 'DD/MM/YYYY',
//   },
//   display: {
//     dateInput: 'DD/MM/YYYY',
//     monthYearLabel: 'MMMM YYYY',
//     dateA11yLabel: 'LL',
//     monthYearA11yLabel: 'MMMM YYYY',
//   },
// };


@Component({
  selector: 'app-payroll-setting-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    MatFormFieldModule,
    MatInputModule,
    MatSelectModule,
    MatRadioModule,
    MatDatepickerModule,
    MatNativeDateModule,
    MatButtonModule,
    MatIconModule,
    MatTabsModule,

  ],

  templateUrl: './company.component.html',
})
export class PayrollSettingFormComponent implements OnInit {
  formData!: FormGroup;

  types: [
    { value: 'sso', name: 'เงินประกันสังคม' },
    { value: 'pvd', name: 'เงินกองทุน' }
  ]

  // Mock dropdown data
  incomeData = [];
  deductionData = [];
  positionList = [];
  departmentList = [];
  branchList = [];
  permissionList = [];
  nationalList = [
    { value: 'thai', name: 'ไทย' },
    { value: 'lao', name: 'ลาว' },
    { value: 'mymar', name: 'เมียนม่า' },
    { value: 'other', name: 'อื่น ๆ' },
  ];
  workTypeList = [
    { value: 'sso', name: 'เงินประกันสังคม' },
    { value: 'pvd', name: 'เงินกองทุน' }
  ];

  employeeList: any = []
  workShiftList: any = []
  brandhList: any = []
  url_doc: any = []

  previewImage: string | ArrayBuffer | null = null;
  previewSignature: string | ArrayBuffer | null = null;
  formFieldHelpers: string[] = ['fuse-mat-dense'];
  masterData!: MasterData;
  userId: any;
  constructor(
    private _formBuilder: FormBuilder,
    private masterDataService: MasterDataService,
    private _fuseConfirmationService: FuseConfirmationService,
    private _router: Router,
    private _service: PageService,
    private _activatedRoute: ActivatedRoute,
    private _changeDetectorRef: ChangeDetectorRef,
    private _matDialog: MatDialog
  ) {
    this._activatedRoute.params.subscribe((params) => {
      const id = params.id;
      this.userId = id
    })
  }

  ngOnInit(): void {
    this.formData = this._formBuilder.group({
      id: [''],
      code: [''],
      name: ['', Validators.required],
      employer_rate: [1, [Validators.required, Validators.min(0)]],
      employee_rate: [1, [Validators.required, Validators.min(0)]],
      max_wage_calculate_status: [1, Validators.required], // 0 หรือ 1
      min_wage_base: [1, [Validators.required, Validators.min(0)]],
      max_wage_base: [1, [Validators.required, Validators.min(0)]],
      status: [1, Validators.required], // 0 = ปิดใช้งาน, 1 = ใช้งาน
    });

    if (this.userId) {
      this._service.getBonusStepById(this.userId).subscribe((resp: any) => {
        this.formData.patchValue({
          ...resp.data,
        })

      })
    }
  }

  onSubmit(): void {
    // console.log(this.formData.value);
    const confirmation = this._fuseConfirmationService.open({
      title: 'บันทึกข้อมูล',
      message: 'คุณต้องการบันทึกข้อมูลใช่หรือไม่ ?',
      icon: {
        show: true,
        name: 'heroicons_outline:question-mark-circle',
        color: 'info',
      },
      actions: {
        confirm: {
          show: true,
          label: 'ยืนยัน',
          color: 'primary',
        },
        cancel: {
          show: true,
          label: 'ยกเลิก',
        },
      },
      dismissible: true,
    });

    // Subscribe to the confirmation dialog closed action
    confirmation.afterClosed().subscribe((result) => {
      // If the confirm button pressed...
      if (result === 'confirmed') {

        let formValue = this.formData.value;


        if (this.userId) {

          this._service.putBonusStep(formValue, this.userId).subscribe({
            next: (resp: any) => {
              this._router.navigateByUrl('payroll-contribution-setting/list');
            },
            error: (err: any) => {
              this._fuseConfirmationService.open({
                title: 'เกิดข้อผิดพลาด',
                message: err.error?.message || 'ไม่สามารถอัปเดตข้อมูลได้',
                icon: {
                  show: true,
                  name: 'heroicons_outline:exclamation-triangle',
                  color: 'warning',
                },
                actions: {
                  confirm: {
                    show: false,
                    label: 'ยืนยัน',
                    color: 'primary',
                  },
                  cancel: {
                    show: false,
                    label: 'ยกเลิก',
                  },
                },
                dismissible: true,
              });
            },
          });
        } else {
          this._service.postBonusStep(formValue).subscribe({
            next: (resp: any) => {
              this._router.navigateByUrl('payroll-contribution-setting/list').then(() => { });
            },
            error: (err: any) => {
              if (typeof err.error.message === 'object' && err.error.message !== null) {
                const errorMessages = Object.keys(err.error.message)
                  .map((key) => `${key}: ${err.error.message.errors[key]}`)
                  .join('\n');
                this._fuseConfirmationService.open({
                  title: 'เกิดข้อผิดพลาด',
                  message: errorMessages,
                  icon: {
                    show: true,
                    name: 'heroicons_outline:exclamation-triangle',
                    color: 'warning',
                  },
                  actions: {
                    confirm: {
                      show: false,
                      label: 'ยืนยัน',
                      color: 'primary',
                    },
                    cancel: {
                      show: false,
                      label: 'ยกเลิก',
                    },
                  },
                  dismissible: true,
                });
              } else {
                this._fuseConfirmationService.open({
                  title: 'เกิดข้อผิดพลาด',
                  message: err.error.message,
                  icon: {
                    show: true,
                    name: 'heroicons_outline:exclamation-triangle',
                    color: 'warning',
                  },
                  actions: {
                    confirm: {
                      show: false,
                      label: 'ยืนยัน',
                      color: 'primary',
                    },
                    cancel: {
                      show: false,
                      label: 'ยกเลิก',
                    },
                  },
                  dismissible: true,
                });
              }

            }
          });
        }
      }
    });
  }

  patchvalueName(event: any) {
    if (event.value === 'sso') {
      this.formData.patchValue(
        {
          name: 'ประกันสังคม'
        }
      )
    } else if (event.value === 'pvd') {
      this.formData.patchValue(
        {
          name: 'กองทุนสำรองเลี้ยงชีพ'
        }
      )
    }
  }

  backTo() {
    this._router.navigateByUrl('payroll-contribution-setting/list')
  }
}
