<mat-tab-group mat-stretch-tabs="false" mat-align-tabs="start" class="w-full">
  <mat-tab label="การตั้งค่า">
    <form [formGroup]="formData"
      class="grid gap-6 grid-cols-1 md:grid-cols-2 lg:grid-cols-4 p-8 bg-white rounded-lg shadow-md w-full"
      enctype="multipart/form-data">
      <!-- Header Section -->
      <div class="col-span-full mb-6 border-b pb-4">
        <h2 class="text-2xl font-semibold text-gray-800">ตั้งค่าเงินประกันสังคมและเงินกองทุน</h2>
        <p class="text-gray-600 mt-1">กรุณากรอกข้อมูลให้ครบถ้วนและถูกต้อง</p>
      </div>
      <!-- System Access Section -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>ประเภทเงินสมทบ</mat-label>
        <mat-select formControlName="code" (selectionChange)="patchvalueName($event)">
          <mat-option *ngFor="let item of workTypeList" [value]="item.value">
            {{ item.name }}
          </mat-option>
        </mat-select>
        <mat-icon matSuffix>account_balance</mat-icon>
      </mat-form-field>
      <!-- name -->
      <mat-form-field class="w-full col-span-3" [ngClass]="formFieldHelpers">
        <mat-label>ชื่อเงินสมทบ</mat-label>
        <input matInput type="text" formControlName="name" />
      </mat-form-field>
      <!-- Email -->
      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>อัตราเงินสบทบนายจ้าง</mat-label>
        <input matInput type="number" formControlName="employer_rate" />
      </mat-form-field>

      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>อัตราเงินสบทบลูกจ่้าง</mat-label>
        <input matInput type="number" formControlName="employee_rate" />
      </mat-form-field>

      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>ฐานเงินขั้นต่ำการคิดเงินสมทบ</mat-label>
        <input matInput type="number" formControlName="min_wage_base" />
      </mat-form-field>

      <mat-form-field class="w-full" [ngClass]="formFieldHelpers">
        <mat-label>ฐานเงินสูงสุดการคิดเงินสมทบ</mat-label>
        <input matInput type="number" formControlName="max_wage_base" />
      </mat-form-field>

      <div class="items-start">
        <label class="block font-medium text-gray-700 mb-2 flex items-center">
          สถานะการคิดฐานเงินสมทบ
        </label>
        <mat-radio-group class="flex flex-row gap-6" formControlName="max_wage_calculate_status">
          <mat-radio-button class="radio-option" [value]="1">
            <div class="flex items-center">
              ใช่
            </div>
          </mat-radio-button>
          <mat-radio-button class="radio-option" [value]="0">
            <div class="flex items-center">
              ไม่ใช่
            </div>
          </mat-radio-button>
        </mat-radio-group>
      </div>

      <!-- Submit Section -->
      <div class="col-span-full flex justify-end mt-8 pt-6 border-t">
        <button mat-raised-button color="warn" class="mr-4" type="button" (click)="backTo()">
          <mat-icon>cancel</mat-icon>
          ยกเลิก
        </button>
        <button mat-raised-button class="bg-green-600 hover:bg-green-700 text-white" (click)="onSubmit()">
          <mat-icon>save</mat-icon>
          บันทึกข้อมูล
        </button>
      </div>
    </form>
  </mat-tab>

</mat-tab-group>