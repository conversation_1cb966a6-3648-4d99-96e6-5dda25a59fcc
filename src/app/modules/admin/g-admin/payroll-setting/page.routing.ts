import { Route, Routes } from '@angular/router';
import { PayrollSettingFormComponent } from './company/company.component';


export default [
    // {
    //     path: '',
    //     pathMatch: 'full',
    //     redirectTo: 'brief-plan'
    // },
    {
        path: '',
        loadComponent: () => import('./page.component').then(m => m.PageComponent),
        children: [
            {
                path: 'list',
                loadComponent: () => import('./page.component').then(m => m.PageComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },




        ]

        /*children : [
            {
                path     : '',
                component: ContactsListComponent,
                resolve  : {
                    tasks    : ContactsResolver,
                    countries: ContactsCountriesResolver
                },
                children : [
                    {
                        path         : ':id',
                        component    : ContactsDetailsComponent,
                        resolve      : {
                            task     : ContactsContactResolver,
                            countries: ContactsCountriesResolver
                        },
                        canDeactivate: [CanDeactivateContactsDetails]
                    }
                ]
            }
        ]*/
    },
    {
        path: 'edit/:id',
        component: PayrollSettingFormComponent

    },
    {
        path: 'form',
        component: PayrollSettingFormComponent

    },
] as Routes
