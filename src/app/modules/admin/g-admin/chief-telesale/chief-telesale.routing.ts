import { Route, Routes } from "@angular/router";




export default [
    // {
    //     path: '',
    //     pathMatch: 'full',
    //     redirectTo: 'brief-plan'
    // },
    {
        path: '',
         loadComponent: () => import('./chief-telesale.component').then(m => m.ChiefTelesaleComponent),
        children: [
            {
                path: 'list',
                loadComponent: () => import('./list-cheif/list-cheif.component').then(m => m.ListCheifComponent),
                data: {
                    status: 'open',
                    topup_status: null
                }
            },
            {
                path: 'list/share',
                loadComponent: () => import('./list-cheif/list-cheif.component').then(m => m.ListCheifComponent),
                data: {
                    status: 'share',
                    topup_status: 'open'
                }
            },
            {
                path: 'list/follow',
                loadComponent: () => import('./list-cheif/list-cheif.component').then(m => m.ListCheifComponent),
                data: {
                    status: 'share',
                    topup_status: 'follow'
                }
            },
            {
                path: 'list/success',
                loadComponent: () => import('./list-cheif/list-cheif.component').then(m => m.ListCheifComponent),
                data: {
                    status: 'share',
                    topup_status: 'success'
                }
            },
            {
                path: 'list/failed',
                loadComponent: () => import('./list-cheif/list-cheif.component').then(m => m.ListCheifComponent),
                data: {
                    status: 'share',
                    topup_status: 'failed'
                }
            },
            {
                path: 'list/refund',
                loadComponent: () => import('./list-cheif/list-cheif.component').then(m => m.ListCheifComponent),
                data: {
                    status: 'refund',
                    topup_status: null
                }
            },
            // {
            //     path: 'new-commission',
            //     component: NewCommissionComponent,

            // },
            // {
            //     path: 'edit/:id',
            //     component: EditCommissionComponent,
            //     // resolve: {
            //     //     products: PermissionProductsResolver,

            //     // }
            // },


        ]

        /*children : [
            {
                path     : '',
                component: ContactsListComponent,
                resolve  : {
                    tasks    : ContactsResolver,
                    countries: ContactsCountriesResolver
                },
                children : [
                    {
                        path         : ':id',
                        component    : ContactsDetailsComponent,
                        resolve      : {
                            task     : ContactsContactResolver,
                            countries: ContactsCountriesResolver
                        },
                        canDeactivate: [CanDeactivateContactsDetails]
                    }
                ]
            }
        ]*/
    }
] as Routes