import {
    After<PERSON>iew<PERSON>nit,
    ChangeDetector<PERSON><PERSON>,
    Component,
    OnDestroy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import {
    Subject,
} from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog, MatDialogRef } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { ChiefTelesaleService } from '../chief-telesale.service';
import { MatFormField, MatLabel } from '@angular/material/form-field';
import { MatSelect } from '@angular/material/select';
import { <PERSON><PERSON><PERSON>, NgClass } from '@angular/common';
import { MatOption } from '@angular/material/autocomplete';
import { MatInput } from '@angular/material/input';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';
// import { ImportOSMComponent } from '../card/import-osm/import-osm.component';

const userData = JSON.parse(localStorage.getItem('user')) || '';

@Component({
    selector: 'cheiftelesale',
    templateUrl: './new.component.html',
    styleUrls: ['./new.component.scss'],
    // changeDetection: ChangeDetectionStrategy.OnPush,
    animations: fuseAnimations,
    imports: [FormsModule, ReactiveFormsModule, MatFormField, MatLabel, MatSelect, NgFor, MatOption, NgClass, MatInput, MatButton, MatIcon]
})
export class NewComponent implements OnInit, AfterViewInit, OnDestroy {

    formFieldHelpers: string[] = ['fuse-mat-dense'];
    formData: FormGroup;
    flashErrorMessage: string;
    flashMessage: 'success' | 'error' | null = null;
    isLoading: boolean = false;
    searchInputControl: FormControl = new FormControl();
    selectedProduct: any | null = null;
    filterForm: FormGroup;
    tagsEditMode: boolean = false;
    env_path = environment.API_URL;
    supplierId: string | null;
    url: any = [];
    files: File[] = [];
    Employee: any = [];
    /**
     * Constructor
     */
    constructor(
        public dialogRef: MatDialogRef<NewComponent>,
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
        private _Service: ChiefTelesaleService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService
    ) { }

    ngOnInit(): void {
        this.formData = this._formBuilder.group({
            user_id: [null, Validators.required],
            qty_work_telesate: [null, Validators.required],
        });
        this.getEmployee();
    }

    onClose() {
        this.dialogRef.close();
    }

    /**
     * After view init
     */
    ngAfterViewInit(): void { }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
    }

    New(): void {
        let errorMessage = '';
        if (!this.formData.value.user_id) {
            errorMessage += 'กรุณาเลือกพนักงาน\n';
            this.showError(errorMessage.trim());
            return;
        }
        if (!this.formData.value.qty_work_telesate) {
            errorMessage += 'กรุณาระบุจำนวน\n';
            this.showError(errorMessage.trim());
            return;
        }
        this.flashMessage = null;
        this.flashErrorMessage = null;
        const confirmation = this._fuseConfirmationService.open({
            title: 'มอบหมายงานแบบสุ่ม',
            message: 'คุณต้องการมอบหมายงานแบบสุ่มใช่หรือไม่ ?',
            icon: {
                show: true,
                name: 'heroicons_outline:information-circle',
                color: 'info',
            },
            actions: {
                confirm: {
                    show: true,
                    label: 'ยืนยัน',
                    color: 'primary',
                },
                cancel: {
                    show: true,
                    label: 'ยกเลิก',
                },
            },
            dismissible: true,
        });
        confirmation.afterClosed().subscribe((result) => {
            if (result === 'confirmed') {
                // let formValue = this.formData.value;

                // const formData = new FormData();
                // Object.entries(this.formData.value).forEach(
                //     ([key, value]: any[]) => {
                //         formData.append(key, value);
                //     }
                // );
                // const user_idArray = this.formData.get('user_id').value;
                // formData.append('user_id', JSON.stringify(user_idArray));
                // const formData = new FormData();
                // formData.append(
                //     'qty_work_telesate',
                //     this.formData.get('qty_work_telesate').value
                // );
                const requestData = {
                    user_id: this.formData.get('user_id').value,
                    qty_work_telesate:
                        this.formData.get('qty_work_telesate').value,
                    // เพิ่มข้อมูลอื่น ๆ ที่คุณต้องการส่งไป
                };

                // ถ้า user_id เป็น FormArray
                this._Service.assignrandom(requestData).subscribe({
                    next: (resp: any) => {
                        this.showFlashMessage('success');
                        this.dialogRef.close();
                    },
                    error: (err: any) => {
                        this._fuseConfirmationService.open({
                            title: 'เกิดข้อผิดพลาด',
                            message: err.error.message,
                            icon: {
                                show: true,
                                name: 'heroicons_outline:exclamation-triangle',
                                color: 'warning',
                            },
                            actions: {
                                confirm: {
                                    show: false,
                                    label: 'ยืนยัน',
                                    color: 'primary',
                                },
                                cancel: {
                                    show: false,
                                    label: 'ยกเลิก',
                                },
                            },
                            dismissible: true,
                        });
                    },
                });
            }
        });
    }

    showFlashMessage(type): void {
        if (type == 'success') {
            this._fuseConfirmationService.open({
                title: 'บันทึกเรียบร้อย',
                message: 'ขอบคุณครับ!!',
                icon: {
                    show: true,
                    name: 'heroicons_outline:check-circle',
                    color: 'success',
                },
                actions: {
                    confirm: {
                        show: true,
                        label: 'ตกลง',
                        color: 'primary',
                    },
                    cancel: {
                        show: false,
                        label: 'ยกเลิก',
                    },
                },
                dismissible: true,
            });

            setTimeout(() => {
                this.flashMessage = null;
                this._changeDetectorRef.markForCheck();
                // this.dialogRef.close()
            }, 3000);
        } else {
            // error
        }
    }

    onSelect(event) {
        this.files.push(...event.addedFiles);
        // Trigger Image Preview
        setTimeout(() => {
            this._changeDetectorRef.detectChanges();
        }, 150);
        this.formData.patchValue({
            image: this.files[0],
        });
    }

    onRemove(event) {
        this.files.splice(this.files.indexOf(event), 1);
        this.formData.patchValue({
            image: '',
        });
    }
    getEmployee() {
        this._Service.getEmployee().subscribe((res: any) => {
            this.Employee = res.data;
        });
    }
    onChange(event: any): void {
        var reader = new FileReader();
        reader.readAsDataURL(event.target.files[0]);
        setTimeout(() => {
            this._changeDetectorRef.detectChanges();
        }, 150);
        reader.onload = (e: any) => (this.url = e.target.result);
        const file = event.target.files[0];
        this.formData.patchValue({
            image: file,
        });
        this._changeDetectorRef.markForCheck();
    }

    showError(message: string): void {
        this._fuseConfirmationService.open({
            "title": "เกิดข้อผิดพลาด",
            "message": message,
            "icon": {
                "show": true,
                "name": "heroicons_outline:exclamation-triangle",
                "color": "warning"
            },
            "actions": {
                "confirm": {
                    "show": false,
                    "label": "ยืนยัน",
                    "color": "primary"
                },
                "cancel": {
                    "show": false,
                    "label": "ยกเลิก",

                }
            },
            "dismissible": true
        });
    }
}
