<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">มอบหมายงาน</div>
    </div>
    <div class="flex md:flex-row w-full min-w-0 bg-gray-100  px-6 pt-6">
        <div class="grid justify-center w-full min-w-0 gap-2 grid-cols-1 sm:grid-cols-2 md:grid-cols-6">
            <div
                class="flex flex-col flex-auto p-6 overflow-hidden rounded-2xl bg-card border-4 border-yellow-500">
                <div class="flex items-start justify-between">
                    <div class="text-lg font-bold leading-6 tracking-tight truncate">รอดำเนินการ</div>
                </div>
                <div class="flex flex-col items-end mt-2 grow justify-end">
                    <div
                        class="flex font-bold leading-none tracking-tight text-yellow-500 h-auto md:text-4xl">
                        {{ (this.countData?.count_open ?? 0 ) | number }} </div>
                </div>
            </div>
            <div
                class="flex flex-col flex-auto p-6 overflow-hidden shadow rounded-2xl bg-card border-4 border-orange-500">
                <div class="flex items-start justify-between">
                    <div class="text-lg font-bold leading-6 tracking-tight truncate">มอบหมายแล้ว</div>
                </div>
                <div class="flex flex-col items-end mt-2 grow justify-end">
                    <div
                        class="flex font-bold leading-none tracking-tight text-orange-600 h-auto md:text-4xl">
                        {{ (this.countData?.count_share ?? 0 ) | number }}  </div>
                </div>
            </div>
            <div
                class="flex flex-col flex-auto p-6 overflow-hidden shadow bg-card rounded-2xl border-4 border-blue-500">
                <div class="flex items-start justify-between">
                    <div class="text-lg font-bold leading-6 tracking-tight truncate">ติดตาม</div>
                </div>
                <div class="flex flex-col items-end mt-2 grow justify-end">
                    <div class="font-bold leading-none tracking-tight text-blue-500 h-auto md:text-4xl">
                        {{ (this.countData?.count_follow ?? 0 ) | number }}</div>
                </div>
            </div>

            <div
                class="flex flex-col flex-auto p-6 overflow-hidden shadow rounded-2xl bg-card border-4 border-green-500">
                <div class="flex items-start justify-between">
                    <div class="text-lg font-bold leading-6 tracking-tight truncate">สำเร็จ</div>
                </div>
                <div class="flex flex-col items-end mt-2 grow justify-end">
                    <div class="font-bold leading-none tracking-tight text-green-500 h-auto md:text-4xl">
                        {{ (this.countData?.cont_success ?? 0 ) | number }}</div>
                </div>
            </div>
            <div class="flex flex-col flex-auto p-6 overflow-hidden shadow bg-card rounded-2xl border-4 border-red-500">
                <div class="flex items-start justify-between">
                    <div class="text-lg font-bold leading-6 tracking-tight truncate">ไม่สำเร็จ</div>
                </div>
                <div class="flex flex-col items-end mt-2 grow justify-end">
                    <div class="font-bold leading-none tracking-tight text-red-500 h-auto md:text-4xl">
                        {{ (this.countData?.count_failed ?? 0 ) | number }}</div>
                </div>
            </div>
            <div class="flex flex-col flex-auto p-6 overflow-hidden shadow rounded-2xl bg-card border-4 border-black">
                <div class="flex items-start justify-between">
                    <div class="text-lg font-bold leading-6 tracking-tight truncate">คืนงาน</div>
                </div>
                <div class="flex flex-col items-end mt-2 grow justify-end">
                    <div class="font-bold leading-none tracking-tight text-black h-auto md:text-4xl">
                        {{ (this.countData?.count_refund ?? 0 ) | number }}</div>
                </div>
            </div>
        </div>
    </div>
    <!-- Main -->
    <div class="flex flex-col flex-auto min-w-0 bg-gray-100 p-6">
        <div class="overflow-auto bg-white shadow-lg rounded-lg">
            <div class="flex flex-row justify-between py-2 px-5">
                <div class="flex md:flex-row justify-start items-center mt-5 gap-2 w-full">
                    <div
                        class="flex text-md font-normal tracking-tight leading-7 sm:leading-10 truncate justify-center items-center w-1/2">
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full min-h-[40px]">
                            <mat-icon svgIcon="search"></mat-icon>
                            <input matInput [placeholder]="' ค้นหา'" [(ngModel)]="searchQuery"
                                (ngModelChange)="applySearch()">
                        </mat-form-field>
                    </div>
                    <div
                        class="flex text-md font-normal tracking-tight leading-7 sm:leading-10 truncate justify-center items-center w-1/2" >
                        <mat-form-field [ngClass]="formFieldHelpers" class="w-full min-h-[40px]" [formGroup]="formData">
                            <mat-select [formControlName]="'user_id'" placeholder="เลือกพนักงาน">
                                <mat-option *ngFor="let item of Employee" [value]="item.id">
                                    {{item.first_name }} {{ item.last_name}}
                                </mat-option>
                            </mat-select>
                        </mat-form-field>
                    </div>
                </div>
                <div
                    class="flex sm:flex-col md:flex-row text-3xl md:text-4xl font-light tracking-tight leading-7 items-center">
                    <button class="ml-3 font-light rounded-md items-center gap-1 sm:w-full" mat-flat-button [color]="'primary'"
                        (click)="tranvip()">
                        <div class="font-light">
                            ย้ายเป็นลูกค้า VIP
                        </div>
                    </button>
                    <button class="ml-3 font-light rounded-md items-center gap-1 sm:w-full" mat-flat-button [color]="'primary'"
                        (click)="New()">
                        <div class="font-light">
                            Random Assign
                        </div>
                    </button>
                    <button class="ml-3 font-light rounded-md items-center gap-1 sm:w-full" mat-flat-button [color]="'primary'"
                        (click)="create()">
                        <div class="font-light">
                            Assign
                        </div>
                    </button>
                </div>
            </div>
            <div class="flex sm:flex-col md:flex-row lg:flex-row px-6 justify-between sm:gap-2">
                <div class="flex md:flex-row sm:flex-col">
                    <div class="border-b-2 " [ngClass]="{'border-gray-200': status == 'open'}">
                        <a [routerLink]="['/chief/list']" mat-button class="rounded-none text-kPrimary font-bold"
                            [ngClass]="{'bg-gray-200 font-bold': status == 'open'}">
                            <mat-icon svgIcon="heroicons_solid:clock"></mat-icon>
                            <p class="mx-2">รอดำเนินการ</p>
                            <span
                                class="bg-yellow-500 text-white px-2 py-1 rounded-sm text-sm font-bold">{{this.countData?.count_open
                                ?? 0}}</span>
                        </a>
                    </div>
                    <div class="border-b-2 "
                        [ngClass]="{'border-gray-200': status === 'share' && topup_status === 'open'}">
                        <a [routerLink]="['/chief/list/share']" mat-button class="rounded-none"
                            [ngClass]="{'bg-gray-200 font-bold': status === 'share' && topup_status === 'open'}">
                            <mat-icon svgIcon="heroicons_solid:user-circle"></mat-icon>
                            <p class="mx-2">มอบหมาย</p>
                            <span
                                class="bg-orange-600 text-white px-2 py-1 rounded-sm text-sm font-bold">{{this.countData?.count_share
                                ?? 0}}</span>
                        </a>
                    </div>
                    <div class="border-b-2 "
                        [ngClass]="{'border-gray-200': status == 'share' && topup_status === 'follow'}">
                        <a [routerLink]="['/chief/list/follow']" mat-button class="rounded-none"
                            [ngClass]="{'bg-gray-200 font-bold': status == 'share' && topup_status === 'follow'}">
                            <mat-icon svgIcon="heroicons_solid:information-circle"></mat-icon>
                            <p class="mx-2">ติดตาม</p>
                            <span
                                class="bg-blue-500 text-white px-2 py-1 rounded-sm text-sm font-bold">{{this.countData?.count_follow
                                ??
                                0}}</span>
                        </a>
                    </div>
                    <div class="border-b-2 "
                        [ngClass]="{'border-gray-200':  status == 'share' &&topup_status === 'success'}">
                        <a [routerLink]="['/chief/list/success']" mat-button class="rounded-none"
                            [ngClass]="{'bg-gray-200 font-bold':  status == 'share' &&topup_status === 'success'}">
                            <mat-icon svgIcon="heroicons_solid:check-circle"></mat-icon>
                            <p class="mx-2">สำเร็จ</p>
                            <span
                                class="bg-green-500 text-white px-2 py-1 rounded-sm text-sm font-bold">{{this.countData?.cont_success
                                ?? 0}}</span>
                        </a>
                    </div>
                    <div class="border-b-2 " [ngClass]="{'border-gray-200': topup_status == 'failed'}">
                        <a [routerLink]="['/chief/list/failed']" mat-button class="rounded-none"
                            [ngClass]="{'bg-gray-200 font-bold':status == 'share' && topup_status == 'failed'}"><mat-icon
                                svgIcon="heroicons_solid:x-circle"></mat-icon>
                            <p class="mx-2">ไม่สำเร็จ</p>
                            <span class="bg-red-500 text-white px-2 py-1 rounded-sm text-sm font-bold">
                                {{this.countData?.count_failed ?? 0}}</span>
                        </a>
                    </div>
                    <div class="border-b-2 " [ngClass]="{'border-gray-200': status == 'refund'}">
                        <a [routerLink]="['/chief/list/refund']" mat-button class="rounded-none"
                            [ngClass]="{'bg-gray-200 font-bold': status == 'refund'}">
                            <mat-icon svgIcon="heroicons_solid:face-frown"></mat-icon>
                            <p class="mx-2">
                                ขอคืนงาน</p>
                            <span class="bg-black text-white px-2 py-1 rounded-sm text-sm font-bold">
                                {{this.countData?.count_refund ?? 0}}</span>
                        </a>
                    </div>
                    <div>
                        <button class="ml-3 font-light rounded-md items-center gap-1 bg-slate-400" mat-flat-button
                        (click)="filterData()">
                            <mat-icon svgIcon="heroicons_outline:magnifying-glass" class="mr-2"></mat-icon>
                            กรองข้อมูล
                    </button>
                    </div>
                </div>
            </div>
            <div class="flex flex-col p-6">
                <table datatable [dtOptions]="dtOptions"
                    class="table w-full text-left text-gray-500 overflow-hidden whitespace-nowrap">
                    <thead class="bg-gray-300 text-black">
                        <tr>
                            <th style="min-width: 80px; text-align: center; vertical-align: middle;">
                                <mat-checkbox (change)="selectAllChanged($event)"></mat-checkbox>
                            </th>
                            <th class="text-center w-20">ช่องทาง</th>
                            <th class="text-center ">หมายเลขคำสั่งซื้อ</th>
                            <th class="text-center ">ชื่อลูกค้า</th>
                            <th class="text-center ">ดำเนินการโดย</th>
                            <th class="text-center ">การชำระเงิน</th>
                            <th class="text-center ">อัพเซล</th>
                            <th class="text-center ">ยอดอัพเซล</th>
                            <th class="text-center ">ยอดสุทธิ</th>
                            <th class="text-center ">ขนส่ง</th>
                            <th class="text-center ">จำนวน</th>
                            <th class="text-center ">วันที่สั่งซื้อ</th>
                        </tr>
                    </thead>
                    <tbody *ngIf="dataRow?.length != 0" class="divide-y divide-gray-200">
                        <tr *ngFor="let item of dataRow; let i = index"
                            class="bg-white hover:bg-gray-200 transition-colors duration-300 ease-in-out">
                            <td style="min-width: 80px; text-align: center; vertical-align: middle;">

                                <mat-checkbox [(ngModel)]="item.IsChecked" [color]="'primary'" [checked]="selectAll"
                                    title="เลือกรายการนี้" class="" (change)="onCheckboxChange($event, item.id)"
                                    *ngIf="!hiddenSave()"></mat-checkbox>
                            </td>
                            <td class="text-center whitespace-nowrap">
                                <div *ngIf="item.sale_order?.channal === 'SP'" class="flex justify-center items-center">
                                    <img [src]='sp' class="h-8 w-8 rounded-full shadow-md" alt="">
                                </div>
                                <div *ngIf="item.sale_order?.channal === 'line'"
                                    class="flex justify-center items-center">
                                    <img [src]='line' class="h-8 w-8 rounded-full shadow-md" alt="">
                                </div>
                                <div *ngIf="item.sale_order?.channal === 'facebook'"
                                    class="flex justify-center items-center">
                                    <img [src]='facebook' class="h-8 w-8 rounded-full shadow-md" alt="">
                                </div>
                                <div *ngIf="item.sale_order?.channal === 'tiktok'"
                                    class="flex justify-center items-center">
                                    <img [src]='tiktok' class="h-8 w-8 rounded-full shadow-md" alt="">
                                </div>
                            </td>
                            <td class="text-center whitespace-nowrap cursor-pointer text-blue-500 underline" (click)="ViewOrder(item.sale_order_id)">{{ item.sale_order?.order_id ?? '-' }}</td>
                            <td class="text-center whitespace-nowrap">{{ item.sale_order?.name ?? '-' }}</td>

                            <td class="text-center whitespace-nowrap">{{ item.user_topup?.account_name ?? '-' }}
                            </td>
                            <td class="text-center whitespace-nowrap">
                                <span *ngIf="item.sale_order?.payment_type === 'COD'"
                                    class="bg-yellow-200 text-yellow-800 px-2 py-1 rounded-lg text-sm font-bold">COD</span>
                                <span *ngIf="item.sale_order?.payment_type === 'transfer'"
                                    class="bg-green-200 text-green-800 px-2 py-1 rounded-lg text-sm font-bold">โอนจ่าย</span>
                            </td>
                            <td class="text-center whitespace-nowrap justify-center items-center cursor-pointer">
                                <div *ngIf="item.status === 'open' && item.topup_status === 'open'"
                                    class="flex justify-center items-center">
                                    <img src="/assets/icons/customer-support-open.png" alt="User Headset Icon"
                                        matTooltip="รอดำเนินการ" matTooltipPosition="above" class="icon" />
                                </div>
                                <div *ngIf="item.status === 'share' && item.topup_status === 'open'"
                                    class="flex justify-center items-center">
                                    <img src="/assets/icons/customer-support-sendworkalready.png"
                                        alt="User Headset Icon" matTooltip="มอบหมาย" matTooltipPosition="above"
                                        class="icon" />
                                </div>
                                <div *ngIf="item.status === 'share' && item.topup_status === 'follow'"
                                    class="flex justify-center items-center ">
                                    <img src="/assets/icons/customer-support-callalreadywait.png"
                                        alt="User Headset Icon" class="icon" />
                                </div>
                                <div *ngIf="item.status === 'share' && item.topup_status === 'success'"
                                    class="flex justify-center items-center">
                                    <mat-icon svgIcon="heroicons_solid:check-circle" class="text-green-500"></mat-icon>

                                </div>
                                <div *ngIf="item.status === 'share' && item.topup_status === 'failed'"
                                    class="flex justify-center items-center">
                                    <img src="/assets/icons/customer-support-failed.png" alt="User Headset Icon"
                                        class="icon" />
                                </div>
                            </td>
                            <td
                            class="text-center whitespace-nowrap"
                            [ngClass]="{
                              'text-green-500': item.status === 'share' && item.topup_status === 'success'
                            }"
                          >
                            <span *ngIf="item.status === 'share' && item.topup_status === 'success'">+</span>
                            {{ (item.upsale_success_total ?? '0.00') | number : '1.2-2' }}
                          </td>


                            <td class="text-center whitespace-nowrap">{{ (item.sale_order?.total ?? '-' ) | number : '1.2-2'}}</td>
                            <td class="text-center whitespace-nowrap">
                                <div class="flex justify-center items-center">
                                    <img [src]='item.sale_order.delivered_by?.image' class="h-8 w-8 rounded-full shadow-md" alt="">
                                </div>
                            </td>
                            <td class="text-center whitespace-nowrap">{{ item.sale_order_qty ?? 0 }} </td>
                            <td class="text-center whitespace-nowrap">{{ item.created_at | date: 'dd/MM/yyyy HH:mm'
                                }}</td>
                        </tr>
                    </tbody>
                    <tbody *ngIf="dataRow?.length == 0">
                        <tr>
                            <td colspan="10" class="no-data-available text-center">ไม่มีข้อมูล !</td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>
