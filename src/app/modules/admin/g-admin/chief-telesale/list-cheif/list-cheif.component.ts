import {
    AfterViewInit,
    ChangeDetectionStrategy,
    ChangeDetectorRef,
    Component,
    OnDestroy,
    OnInit,
    ViewChild,
    ViewEncapsulation,
} from '@angular/core';
import { FormBuilder, FormControl, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatCheckboxChange, MatCheckbox } from '@angular/material/checkbox';
import { MatPaginator, PageEvent } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import {
    debounceTime,
    map,
    merge,
    Observable,
    Subject,
    switchMap,
    takeUntil,
} from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { environment } from 'environments/environment';
import { MatTabChangeEvent } from '@angular/material/tabs';
// import { ImportOSMComponent } from '../card/import-osm/import-osm.component';
import { MatTableDataSource } from '@angular/material/table';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
// import { CommissionService } from '../commission.service';
// import { EditCommissionComponent } from '../edit-commission/edit-commission.component';
// import { NewCommissionComponent } from '../new-commission/new-commission.component';
import { AutofillMonitor } from '@angular/cdk/text-field';
import { ChiefTelesaleService } from '../chief-telesale.service';
import { NewComponent } from '../new/new.component';
import { NewFileComponent } from '../newfile/newfile.component';
import { TranvipComponent } from '../../worktelesale/tranvip/tranvip.component';
import { FilterComponent } from '../../worktelesale/work-telesale-filter/filter.component';
import { WorktelesaleService } from '../../worktelesale/worktelesale.service';
import { DialogViewOrderComponent } from '../../worktelesale/dialog-view-order/dialog-view-order.component';
import { NgIf, NgClass, NgFor, DecimalPipe, DatePipe } from '@angular/common';
import { MatProgressBar } from '@angular/material/progress-bar';
import { MatFormField } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput } from '@angular/material/input';
import { MatSelect } from '@angular/material/select';
import { MatOption } from '@angular/material/autocomplete';
import { MatButton, MatAnchor } from '@angular/material/button';
import { MatTooltip } from '@angular/material/tooltip';
@Component({
    selector: '',
    templateUrl: './list-cheif.component.html',
    styleUrls: ['./list-cheif.component.scss'],
    // encapsulation: ViewEncapsulation.None,
    // changeDetection: ChangeDetectionStrategy.OnPush,
    animations: fuseAnimations,
    imports: [NgIf, MatProgressBar, MatFormField, NgClass, MatIcon, MatInput, FormsModule, ReactiveFormsModule, MatSelect, NgFor, MatOption, MatButton, MatAnchor, RouterLink, DataTablesModule, MatCheckbox, MatTooltip, DecimalPipe, DatePipe]
})
export class ListCheifComponent implements OnInit, AfterViewInit, OnDestroy {
    @ViewChild(DataTableDirective)
    dtElement!: DataTableDirective;
    public dtOptions: DataTables.Settings = {};
    dtOptionsList: DataTables.Settings = {};

    private destroy$ = new Subject<any>();
    // dataRow: any = []
    @ViewChild(MatPaginator) _paginator: MatPaginator;
    @ViewChild(MatSort) private _sort: MatSort;
    status: string = '';
    topup_status: string = '';
    flashMessage: 'success' | 'error' | null = null;
    isLoading: boolean = false;
    searchInputControl: FormControl = new FormControl();
    selectedProduct: any | null = null;
    filterForm: FormGroup;
    tagsEditMode: boolean = false;
    formData: FormGroup;
    selectAll = false;
    Employee: any = [];
    work_telesateSelected: any[] = [];
    selectedTabLabel: string;
    formFieldHelpers: string[] = ['fuse-mat-dense'];
    public total = 0;
    searchQuery: string = ''
    delivery: any = [];
    all: any = [0, 0, 0, 0, 0, 0];
    call_false: any = [0, 0, 0, 0, 0, 0];
    call_true: any = [0, 0, 0, 0, 0, 0];
    show_all: any = 0;
    show_call_false: any = 0;
    show_call_true: any = 0;
    countData: any;
    line: string = 'assets/images/line.png';
    tiktok: string = 'assets/images/tiktok.png';
    facebook: string = 'assets/images/facebook.png';
    sp: string = 'assets/images/sp.png';
    form: FormGroup
    public dataRow: any[] = [];
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    /**
     *
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
        private _Service: ChiefTelesaleService,
        private _ServiceWorkTelesale: WorktelesaleService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute
    ) {
        this.status = this._activatedRoute.snapshot.data.status;
        this.topup_status = this._activatedRoute.snapshot.data.topup_status;

        this.form = this._formBuilder.group({
            date_start: null,
            date_end: null,
            up_sale_date_start: null,
            up_sale_date_stop: null,
            user_id: null,
            channal: null,
            payment_type: null,
            delivery_id: null,
        });
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        // this.ViewOrder('226');
        this.formData = this._formBuilder.group({
            user_id: null,
            work_telesate: []
        })
        this.GetDashboard()
        this.loadTable();
        this.getEmployee();
        this.getDelivery();

    }
    hiddenEdit() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 11);
        return menu.edit == 0;
    }
    hiddenDelete() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 11);
        return menu.delete == 0;
    }
    hiddenSave() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 11);
        return menu.save == 0;
    }

    getDelivery() {
        this._Service.getDelivery().subscribe((res: any) => {
            this.delivery = res.data;
        });
    }
    /**
     * After view init
     */
    ngAfterViewInit(): void { }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    selectAllChanged(event: any) {
        this.selectAll = event.checked;
        this.dataRow.forEach((item) => (item.checked = this.selectAll));
    }

    onTabChange(event: MatTabChangeEvent): void {
        this.selectedTabLabel = event.tab.textLabel;
        this.show_all = this.all[event.index]
        this.show_call_false = this.call_false[event.index]
        this.show_call_true = this.call_true[event.index]
    }

    applySearch() {
        this.rerender()
    }

    /**
     * Close the details
     */

    /**
     * Show flash message
     */
    showFlashMessage(type: 'success' | 'error'): void {
        // Show the message
        this.flashMessage = type;

        // Mark for check
        this._changeDetectorRef.markForCheck();

        // Hide it after 3 seconds
        setTimeout(() => {
            this.flashMessage = null;

            // Mark for check
            this._changeDetectorRef.markForCheck();
        }, 3000);
    }

    ViewOrder(itemId: any) {
        const dialogRef = this._matDialog.open(DialogViewOrderComponent, {
            width: '800px',
            height: '750px',
            data: {
                itemid: itemId
            }
        });
        dialogRef.afterClosed().subscribe(item => {
            this.rerender();
            this._changeDetectorRef.markForCheck();
        });
    }

    // New() {
    //     const dialogRef = this._matDialog.open(NewCommissionComponent, {
    //         // width: '50%',
    //         // minHeight: 'calc(100vh - 90px)',
    //         // height: 'auto'
    //         width: '800px',
    //         height: 'auto'
    //     });

    //     dialogRef.afterClosed().subscribe(item => {
    //         this.rerender();
    //         this._changeDetectorRef.markForCheck();
    //     });
    // }
    gotoHistoryOrder(item) {
        this._router.navigate([
            'worktelesale/history-customer-order/' + item.id,
        ]);
    }
    pages = { current_page: 1, last_page: 1, per_page: 10, begin: 0 };
    loadTable(): void {
        const that = this;
        this.dtOptions = {
            pagingType: 'full_numbers',
            pageLength: 10,
            serverSide: true,
            processing: true,
            searching: false,
            // ordering: false,
            order: [[5, 'desc']],
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.11.3/i18n/th.json',
            },
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.search = { value: this.searchQuery };
                dataTablesParameters.status = this.status;
                dataTablesParameters.topup_status = this.topup_status;
                dataTablesParameters.date_start = this.form.value.date_start;
                dataTablesParameters.date_end = this.form.value.date_end;
                dataTablesParameters.up_sale_date_start = this.form.value.up_sale_date_start;
                dataTablesParameters.up_sale_date_stop = this.form.value.up_sale_date_stop;
                dataTablesParameters.user_id = this.form.value.user_id;
                dataTablesParameters.channal = this.form.value.channal;
                dataTablesParameters.payment_type = this.form.value.payment_type;
                dataTablesParameters.delivery_id = this.form.value.delivery_id;
                that._Service
                    .getTelesalePage(dataTablesParameters)
                    .subscribe((resp) => {
                        this.dataRow = resp.data;
                        // this.countData  =resp
                        this.pages.current_page = resp.current_page;
                        this.pages.last_page = resp.last_page;
                        this.pages.per_page = resp.per_page;
                        if (resp.current_page > 1) {
                            this.pages.begin =
                                resp.per_page * resp.current_page - 1;
                        } else {
                            this.pages.begin = 0;
                        }
                        callback({
                            recordsTotal: resp.total,
                            recordsFiltered: resp.total,
                            data: [],
                        });
                        this._changeDetectorRef.markForCheck();
                    });
            },
            columns:[
                {data: 'action', orderable: false },
                {data: 'action', orderable: false },
                {data: 'action', orderable: false },
                {data: 'action', orderable: false },
                {data: 'action', orderable: false },
                {data: 'action', orderable: false },
                {data: 'action', orderable: false },
                {data: 'action', orderable: false },
                {data: 'action', orderable: false },
                {data: 'action', orderable: false },
                {data: 'action', orderable: false },
            ]

        };
    }

    GetDashboard():void {
        this._ServiceWorkTelesale.getDashboardTelesale('admin').subscribe((resp: any)=>{
            this.countData = resp
            this._changeDetectorRef.markForCheck()
        })
    }

    rerender(): void {
        this.GetDashboard()
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            dtInstance.ajax.reload();
        });
    }
    onCheckboxChange(event: any, id: any) {
        if (event.checked || this.selectAll) {
            // ถ้ายังไม่มี work_telesate ใน formData สร้าง array ว่าง ๆ
            // if (!this.formData.value.work_telesate) {
            //     this.formData.value.work_telesate = [];
            // }

            // ถ้าค่าที่เลือกยังไม่มีใน array ก็เพิ่มเข้าไป
            if (!this.work_telesateSelected.includes(id)) {
                this.work_telesateSelected.push(id);
            }

        } else {
            // เอาค่าที่ไม่ต้องการออกจาก array
            const index = this.work_telesateSelected.indexOf(id);
            if (index !== -1) {
                this.work_telesateSelected.splice(index, 1);
            }


        }

        // อัปเดตฟอร์มเมื่อเลือก checkbox
        this.formData.patchValue({
            work_telesate: this.work_telesateSelected
        });
    }

    // ตรวจสอบว่า checkbox ควรถูกติ้กหรือไม่
    isChecked(id: any): boolean {
        return this.work_telesateSelected.includes(id);
    }

    Delete(id: any) {
        const confirmation = this._fuseConfirmationService.open({
            title: 'ลบประเภทสินค้า',
            message: 'คุณต้องการลบประเภทสินค้าใช่หรือไม่ ',
            icon: {
                show: false,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
            },
            actions: {
                confirm: {
                    show: true,
                    label: 'ยืนยัน',
                    color: 'danger',
                },
                cancel: {
                    show: true,
                    label: 'ยกเลิก',
                },
            },
            dismissible: true,
        });

        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            // If the confirm button pressed...
            if (result === 'confirmed') {
                this._Service
                    .delete(id)
                    .pipe(takeUntil(this.destroy$))
                    .subscribe((res: any) => {
                        if (res.code == 201) {
                            this._fuseConfirmationService
                                .open({
                                    title: 'ลบข้อมูลประเภทสินค้า',
                                    message: 'บันทึกเรียบร้อย',
                                    icon: {
                                        show: true,
                                        name: 'heroicons_outline:check-circle',
                                        color: 'success',
                                    },
                                    actions: {
                                        confirm: {
                                            show: true,
                                            label: 'ตกลง',
                                            color: 'primary',
                                        },
                                        cancel: {
                                            show: false,
                                            label: 'ยกเลิก',
                                        },
                                    },
                                    dismissible: true,
                                })
                                .afterClosed()
                                .subscribe((res) => {
                                    this.rerender();
                                });
                        }
                    });
            }
        });
    }

    create() {

        const confirmation = this._fuseConfirmationService.open({
            title: 'มอบหมาย',
            message: 'คุณต้องการมอบหมายใช่หรือไม่ ',
            icon: {
                show: false,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
            },
            actions: {
                confirm: {
                    show: true,
                    label: 'ยืนยัน',
                    color: 'danger',
                },
                cancel: {
                    show: true,
                    label: 'ยกเลิก',
                },
            },
            dismissible: true,
        });
        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            // If the confirm button pressed...
            if (result === 'confirmed') {
                if (!this.formData.value.user_id) {
                    this._fuseConfirmationService.open({
                        title: 'เกิดข้อผิดพลาด',
                        message: 'กรุณาเลือกพนักงาน',
                        icon: {
                            show: true,
                            name: 'heroicons_outline:exclamation-triangle',
                            color: 'warning',
                        },
                        actions: {
                            confirm: {
                                show: false,
                                label: 'ยืนยัน',
                                color: 'primary',
                            },
                            cancel: {
                                show: false,
                                label: 'ยกเลิก',
                            },
                        },
                        dismissible: true,
                    });
                }

                this._Service
                    .assign(this.formData.value)
                    .pipe(takeUntil(this.destroy$))
                    .subscribe((res: any) => {
                        if (res.code == 201) {
                            this._fuseConfirmationService
                                .open({
                                    title: 'มอบหมายงานสำเร็จ',
                                    message: 'บันทึกเรียบร้อย',
                                    icon: {
                                        show: true,
                                        name: 'heroicons_outline:check-circle',
                                        color: 'success',
                                    },
                                    actions: {
                                        confirm: {
                                            show: false,
                                            label: 'ตกลง',
                                            color: 'primary',
                                        },
                                        cancel: {
                                            show: false,
                                            label: 'ยกเลิก',
                                        },
                                    },
                                    dismissible: true,
                                })
                                .afterClosed()
                                .subscribe((res) => {
                                    this.formData.reset()
                                    this.work_telesateSelected = []
                                    this.rerender();
                                    // window.location.reload()
                                });
                        }
                    });
            }
        });
    }

    tranvip() {
        const dialogRef = this._matDialog.open(TranvipComponent, {
            width: '600px',
            height: 'auto',
        });

        dialogRef.afterClosed().subscribe((item) => {
            this.rerender();
            this._changeDetectorRef.markForCheck();
        });
    }

    filterData() {
        const dialogRef = this._matDialog.open(FilterComponent, {
            width: '900px',
            height: '600px',
            data: {
                value: this.form.value
            }
        })
        dialogRef.afterClosed().subscribe((item) => {
            if(item) {
                this.form.patchValue({
                    ...item
                })
            }
            this.rerender();
            this._changeDetectorRef.markForCheck();
        });
    }

    getEmployee() {
        this._Service.getEmployee().subscribe((res: any) => {
            this.Employee = res.data;
        });
    }

    New() {
        const dialogRef = this._matDialog.open(NewComponent, {
            width: '600px',
            height: 'auto',
        });

        dialogRef.afterClosed().subscribe((item) => {
            if(item) {
                this.rerender();
                this._changeDetectorRef.markForCheck();
            } else {

            }

        });
    }

    Newfile(itemId) {
        const dialogRef = this._matDialog.open(NewFileComponent, {
            width: '600px',
            height: 'auto',
            data: {
                itemId: itemId,
            },
        });

        dialogRef.afterClosed().subscribe((item) => {
            this.rerender();
            this._changeDetectorRef.markForCheck();
        });
    }

    calculateTotal(data: any) {
        if (data) {
            const sumValue = data.reduce((sum, item) => sum + item.total, 0);
            return sumValue;
        } else {
            return 0;
        }
    }
}