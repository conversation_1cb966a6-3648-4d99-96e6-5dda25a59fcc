.custom-badge .mat-badge-content {
    background-color: #585858; /* สีที่ต้องการ */
    color: white; /* สีของตัวอักษรใน badge */
}

.custom-badge-notpay .mat-badge-content {
    background-color: #F44336; /* สีที่ต้องการ */
    color: white; /* สีของตัวอักษรใน badge */
}

.custom-badge-pay .mat-badge-content {
    background-color: #4CAF50; /* สีที่ต้องการ */
    color: white; /* สีของตัวอักษรใน badge */
}

.custom-badge-confirm .mat-badge-content {
    background-color: #2196F3; /* สีที่ต้องการ */
    color: white; /* สีของตัวอักษรใน badge */
}

.custom-badge-packing .mat-badge-content {
    background-color: #FF9800; /* สีที่ต้องการ */
    color: white; /* สีของตัวอักษรใน badge */
}

.custom-badge-delivery .mat-badge-content {
    background-color: #dbc92d; /* สีที่ต้องการ */
    color: white; /* สีของตัวอักษรใน badge */
}

.custom-badge-success .mat-badge-content {
    background-color: #28A745; /* สีที่ต้องการ */
    color: white; /* สีของตัวอักษรใน badge */
}

.custom-badge-unsuccess .mat-badge-content {
    background-color: #DC3545; /* สีที่ต้องการ */
    color: white; /* สีของตัวอักษรใน badge */
}

.custom-badge-return .mat-badge-content {
    background-color: #FFC107; /* สีที่ต้องการ */
    color: white; /* สีของตัวอักษรใน badge */
}

.icon {
    width: 24px; /* ขนาดไอคอน */
    height: 24px; /* ขนาดไอคอน */
  }
  /* ปรับปรุงการจัดรูปแบบตาราง */
  table {
    border-collapse: collapse;
    width: 100%;
  }

  thead {
    color: white;
  }

  th, td {
    padding: 12px;
    text-align: center;
  }

  th {
    font-weight: bold;
    border-bottom: 2px solid #ddd;
  }

  td {
    border-bottom: 1px solid #ddd;
  }



  footer {
    position: fixed; /* ติดกับหน้าจอ */
    bottom: 0;       /* แสดงที่ด้านล่างของหน้าจอ */
    width: 100%;     /* ครอบคลุมความกว้างทั้งหมด */
    padding: 10px;
    z-index: 50; /* กำหนด z-index ให้สูงกว่า footer เดิม */
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1); /* เพิ่มเงา */
  }
  .icon-white {
    color: white;
  }





