<div class="flex flex-col flex-auto min-w-0 bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between p-1 sm:pb-4 sm:px-10 border-b bg-card dark:bg-transparent">
        <div class="flex-1 min-w-0">
            <!-- Title -->
            <div class="mt-2">
                <h2 class="text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                    บันทึกไฟล์/บันทึกเสียง
                </h2>
            </div>

        </div>
    </div>

    <!-- Main -->
    <div class="flex-auto p-3 sm:p-2">
        <form [formGroup]="formData">

            <!-- <div class="flex mt-4">
                <label for="name"
                    class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">ค้นหาลูกค้า</label>
                    <button mat-stroked-button class="w-1/3" style="min-height: 48px;">
                        <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:magnifying-glass'">
                        </mat-icon>
                        ค้นหาลูกค้า
                    </button>
            </div> -->

            <!--
            <div class="flex mt-4">
                <label for="name"
                    class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">ชื่อลูกค้า</label>
                <input id="name"type="number" min="1"
                    class="mb-5 mt-2 text-gray-600 focus:outline-none focus:border focus:border-yellow-600 font-normal w-2/3 h-10 flex items-center pl-3 text-sm border-gray-300 rounded border"
                    placeholder="จำนวนวัน" formControlName="name" />
            </div> -->


            <!-- <div class="flex mt-4">
                <label for="name"
                    class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">เลือกไฟล์</label>
                <input id="name"type="file" min="1"
                    class="mt-2 text-gray-600 focus:outline-none focus:border focus:border-yellow-600 font-normal w-2/3 h-10 flex items-center pl-3 text-sm border-gray-300 rounded border"
                    placeholder="จำนวนวัน" formControlName="file" />

            </div>
             -->
            <div class="flex mt-4">
                <label for="code"
                    class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">ไฟล์/เสียง</label>
                <div class="mt-2 w-2/3 text-sm">
                    <ngx-dropzone style="width: 300px; height: 100px;" (change)="onSelect($event)">
                        <ngx-dropzone-label>เลือกไฟล์</ngx-dropzone-label>
                        <ngx-dropzone-image-preview *ngFor="let f of files" [removable]="true" [file]="f"
                            (removed)="onRemove(f)">
                        </ngx-dropzone-image-preview>
                    </ngx-dropzone>
                </div>
            </div>


            <!--
            <div class="flex mt-4">
                <label for="name"
                    class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">สถานะการติดต่อ</label>
                 <mat-form-field class="w-2/3 pr-2">

                    <mat-select [formControlName]="'status'">
                        <mat-option >สถานะการติดต่อ
                        </mat-option>
                    </mat-select>
                 </mat-form-field>
            </div> -->

            <div class="flex mt-4">
                <label for="name"
                    class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">รายละเอียด</label>
                <textarea id="name" rows="4"
                    class="mt-2 text-gray-600 focus:outline-none focus:border focus:border-yellow-600 font-normal w-2/3 h-36 flex items-center pl-3 text-sm border-gray-300 rounded border"
                    placeholder="รายละเอียด" formControlName="remark" ></textarea>
            </div>

            <div class="flex items-center justify-end w-full border-t px-8 py-4 mt-4">
                <div class="flex items-center justify-end">
                    <button mat-flat-button class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4" (click)="onClose()">
                        <mat-icon svgIcon="heroicons_solid:x-mark" class="mr-2"></mat-icon>
                        ยกเลิก
                    </button>
                    <button class="px-6 ml-3 mat-primary" mat-flat-button (click)="New()">
                        <mat-icon svgIcon="heroicons_solid:check"></mat-icon>
                        ยืนยัน
                    </button>
                </div>
            </div>


        </form>
    </div>
</div>
