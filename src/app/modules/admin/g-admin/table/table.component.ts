import { CommonModule } from '@angular/common';
import { ChangeDetectorRef, Component } from '@angular/core';
import { FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatButton, MatIconButton } from '@angular/material/button';
import { MatOption } from '@angular/material/core';
import { MatFormField } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput } from '@angular/material/input';
import { MatSelect } from '@angular/material/select';
import { DataTablesModule } from 'angular-datatables';
import { TableService } from './table.service';
import { MatDatepickerInputEvent } from '@angular/material/datepicker';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatDatepickerModule } from '@angular/material/datepicker';


@Component({
  selector: 'app-general',
  imports: [MatFormFieldModule, MatDatepickerModule, MatInputModule, MatIcon, MatInput, MatButton, FormsModule, ReactiveFormsModule,
    MatSelect, MatOption, DataTablesModule, MatIconButton, CommonModule],
  templateUrl: './table.component.html',
  styleUrl: './table.component.scss'
})

export class TableComponent {

  data: string[][] = [];
  // dateRange: { start: Date; end: Date } | null = null;
  dateheader = ['16', '17', '18', '19', '20', '21', '22', '23', '24', '25', '26', '27', '28', '29', '30', '31'];
  grayColumnsMap: Record<number, 'light' | 'dark'> = {};
  totalSum: number = 0;
  totalOT: number = 0;
  readonly PREFIXES = ['นาย', 'นางสาว', 'นาง', 'เด็กชาย', 'เด็กหญิง', 'Mr.', 'Mrs.', 'Miss'];
  constructor(
    private _service: TableService,
    private cdRef: ChangeDetectorRef
  ) {

  }

  markGrayColumns(data: string[][]): void {
    const cols = data[0]?.length ?? 0;
    this.grayColumnsMap = {};

    for (let col = 0; col < cols; col++) {
      for (let row of data) {
        if (row[col] === 'OFF') {
          this.grayColumnsMap[col] = 'dark';  // เทาเข้ม
          break; // ถ้าเจอ OFF แล้วไม่ต้องเช็ค H อีก
        }
        else if (row[col] === 'H') {
          // ถ้ายังไม่มี OFF ให้ set เทาอ่อน
          if (!this.grayColumnsMap[col]) {
            this.grayColumnsMap[col] = 'light';
          }
        }
      }
    }
  }
  // onDateChange(event: MatDatepickerInputEvent<any>) {  // ใช้ any เพราะเป็น moment
  // const momentDate = event.value;

  // if (!momentDate || !momentDate.isValid || !momentDate.isValid()) {
  //     console.log('วันที่ที่เลือกไม่ถูกต้อง หรือไม่ได้เลือกวันที่');
  //     this.dateRange = null;
  //     return;
  // }

  // const startDate: Date = momentDate.toDate();  // แปลงเป็น Date object

  // const year = startDate.getFullYear();
  // const month = startDate.getMonth();

  // const endDate = new Date(year, month + 1, 0);

  // this.dateRange = {
  //     start: startDate,
  //     end: endDate,
  // };

  // console.log('วันที่เริ่มต้นที่เลือก (Date):', startDate);
  // console.log('วันสุดท้ายของเดือน:', endDate);
  // console.log('วันสุดท้ายของเดือนจริงหรือไม่:', this.isLastDayOfMonth(endDate));
  // }

  isLastDayOfMonth(date: Date): boolean {
    const year = date.getFullYear();
    const month = date.getMonth();
    const day = date.getDate();

    const lastDay = new Date(year, month + 1, 0).getDate();

    return day === lastDay;
  }

  calculateTotalSum(): void {
    this.totalSum = this.data.reduce((sum, row) => {
      // ช่องสุดท้ายของแต่ละแถวคือ row[row.length - 1]
      let valStr = row[row.length - 1];
      // แปลง string เป็น number โดยตัด comma ออกก่อน
      let valNum = parseFloat(valStr.replace(/,/g, ''));
      if (!isNaN(valNum)) {
        return sum + valNum;
      }
      return sum;
    }, 0);
  }

  calculateTotalOT(): void {
    this.totalOT = 0;

    for (const row of this.data) {
      const secondLast = row[row.length - 2]; // ช่องก่อนสุดท้าย
      const num = parseFloat((secondLast || '').replace(/,/g, ''));
      if (!isNaN(num)) {
        this.totalOT += num;
      }
    }
  }
  isTextOnly(cell: string): boolean {
    if (!cell) return false;
    const trimmed = cell.trim();
    return /^[\p{L}\s]+$/u.test(trimmed);
  }
  isAllNumber(text: string): boolean {
    if (!text) return false;
    const trimmed = text.trim().replace(/,/g, '');
    return /^[0-9]+(\.[0-9]+)?$/.test(trimmed);
  }

  isAllText(text: string): boolean {
    if (!text) return false;
    const trimmed = text.trim();
    if (trimmed.length === 0) return false;
    // ตรวจว่าเป็นตัวอักษรไทย/อังกฤษ หรือ space เท่านั้น
    return /^[\p{L}\s]+$/u.test(trimmed);
  }


  normalizeText(text: string): string {
    return text
      ?.replace(/[\u200B-\u200D\uFEFF\u00A0]/g, '')  // invisible spaces
      .replace(/\s+/g, ' ')                         // ปรับ space หลายตัวให้เหลือ 1
      .trim();                                      // ตัด space หน้า/หลัง
  }

  splitLeft(text: string): string {
    text = this.normalizeText(text);
    const parts = text.split(' ');

    if (parts.length === 1) {
      return this.isAllNumber(parts[0]) ? '' : parts[0];
    }

    const last = parts[parts.length - 1];
    if (this.isAllNumber(last)) {
      return parts.slice(0, -1).join(' ');
    }

    const hasPrefix = this.PREFIXES.includes(parts[0]);
    if (hasPrefix && parts.length >= 3) {
      return parts.slice(0, -1).join(' ');
    }

    return text;
  }

  splitRight(text: string): string {
    text = this.normalizeText(text);
    const parts = text.split(' ');

    if (parts.length === 1) {
      return this.isAllNumber(parts[0]) ? parts[0] : '';
    }

    const last = parts[parts.length - 1];
    if (this.isAllNumber(last)) {
      return last;
    }

    const hasPrefix = this.PREFIXES.includes(parts[0]);
    if (hasPrefix && parts.length >= 3) {
      return last;
    }

    return '';
  }

  ngOnInit(): void {

    this.data = [
      ['1', 'นางสาว นุชจรินทร์ ชัยศรีประเสริฐ', 'หัวหน้าการเงิน', '13,262', 'H', 'H', '1', '1', 'OFF', '1', '1', '1', '1', '1', '1', 'OFF', '', '', '', '', '8', '', '', '13,261.50'],
      ['', 'ออฟฟิศ 26523', 'โอที', '166', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '0', '-', ''],
      ['2', 'นาย รุ่งโรจน์ วงษ์จันทร์อินทร์', 'ผจก.ทั่วไป', '8,556', 'H', 'H', '1', '1', 'OFF', '1', '1', '1', '1', '1', '1', 'OFF', '', '', '', '', '8', '', '', '8,556'],
      ['', ' 1711', 'โอที', '107', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '0', '-', ''],
      ['3', 'นาย กิ ชาวลาว', 'พนง.ทั่วไป', '6,216', 'H', 'H', '1', '1', 'OFF', '1', '1', '1', '1', '1', '1', 'OFF', '', '', '', '', '8', '', '', '6,216'],
      ['', ' ขาย ห้อง 6 12432 ', 'โอที', '78', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '0', '-', ''],
      ['4', 'นาย นิศา มาผาสุข', 'หน.ฝ่ายขาย', '8,007', 'H', 'H', '1', '1', 'OFF', '1', '1', '1', '1', '1', '1', 'OFF', '', '', '', '', '8', '', '', '8,007'],
      ['', ' ขาย 16014', 'โอที', '100', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '0', '-', ''],
      ['5', 'นาย สายชล มาผาสุข', 'หน.จัดเตรียมสินค้า', '8,330', 'H', 'H', '1', '1', 'OFF', '1', '1', '1', '1', '1', '1', 'OFF', '', '', '', '', '7', '', '', '8,330'],
      ['', ' อัดของ 16659', 'โอที', '104', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '1', '104.12', ''],
      ['6', 'นาย .แสง ชาวลาว', 'พนง.ทั่วไป', '400', 'H', 'H', '1', '1', 'OFF', '1', '1', '1', '1', '1', '1', 'OFF', '', '', '', '', '8', '', '', '3,200'],
      ['', ' เริ่ม 4/7/65', 'โอที', '75', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '0', '-', ''],
      ['7', 'นาย จิรวัฒน์ โสมบ้านกวย', 'ผู้ช่วยหน.ฝ่ายซื้อ', '7,973', 'H', 'H', '1', '1', 'OFF', '1', '1', '1', '1', '1', '1', 'OFF', '', '', '', '', '8', '', '', '7,973'],
      ['', ' ซื้อ 15946', 'โอที', '100', '', '', '', '', '', '', 'ป', '', '', '', '', '', '', '', '', '', '', '0', '-', ''],
      ['8', 'นาย ธง ชาวพม่า', 'พนง.ทั่วไป', '400', 'H', 'H', '1', '1', 'OFF', '1', '1', '1', '1', '1', '1', 'OFF', '', '', '', '', '8', '', '', '3,520'],
      ['', ' อัดของ ', 'โอที', '83', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '', '0', '-', ''],
    ];
    this.markGrayColumns(this.data);
    this.cdRef.detectChanges();
    this.calculateTotalSum();
    this.calculateTotalOT();
    console.log(JSON.stringify(this.data[10][1])); // 'นาย แสง  ชาวลาว'
  }
}



