<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">หลักฐานการจ่ายเงินค้าตอบแทน</div>
        <!-- Actions -->
    </div>
    <!-- Main -->
    <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent w-full">
        <div class="w-full mt-0">
            <div class="flex flex-col flex-auto p-2 sm:overflow-auto overflow-x-scroll">
                <div class="w-full text-left mb-1 px-4 pt-6 flex flex-col items-center gap-3">
                    <div class="w-fit text-xl text-center font-semibold">
                        หลักฐานการจ่ายเงินค่าตอบแทน พนง. บจก.จีเอส อินเตอร์ กรุ๊ป
                        <div class="flex justify-between mt-2">
                            <div class="font-semibold text-base">ประจำวันที่</div>
                            <div class="font-semibold text-base"></div>
                        </div>
                    </div>
                    <!-- <mat-form-field appearance="fill">
                    <mat-label>เลือกวันเริ่มต้น</mat-label>
                    <input matInput [matDatepicker]="picker" (dateChange)="onDateChange($event)" />
                    <mat-datepicker-toggle matSuffix [for]="picker"></mat-datepicker-toggle>
                    <mat-datepicker #picker></mat-datepicker>
                    </mat-form-field>

                    <p>ช่วงวันที่: {{ dateRange?.start | date:'dd/MM/yyyy' }} - {{ dateRange?.end | date:'dd/MM/yyyy' }}</p> -->
                </div>
                <table class="table-auto w-full text-sm rounded-lg overflow-hidden border border-gray-300">
                    <thead>
                        <tr class="bg-gray-200">
                            <th rowspan="2" class="border px-1 py-1 text-center align-middle h-[48px]">ลำดับที่</th>
                            <th rowspan="2" class="border px-1 py-1 text-center align-middle h-[48px]">ชื่อ-สกุล</th>
                            <th rowspan="2" class="border px-1 py-1 text-center align-middle h-[48px]">ตำแหน่ง</th>
                            <th rowspan="2" class="border px-1 py-1 text-center align-middle h-[48px]">ค่าแรง</th>
                            <th colspan="16" class="border px-1 py-1 text-center align-middle h-[48px]">วันที่ปฏิบัติงาน
                            </th>
                            <th rowspan="2" class="border px-1 py-1 text-center align-middle h-[48px]">วันที่คิดเงิน
                            </th>
                            <th rowspan="2" class="border px-1 py-1 text-center align-middle h-[48px]">ชม.โอที</th>
                            <th rowspan="2" class="border px-1 py-1 text-center align-middle h-[48px]">เงินโอที</th>
                            <th rowspan="2" class="border px-1 py-1 text-center align-middle h-[48px]">รวมเงินได้</th>
                        </tr>
                        <tr class="bg-gray-100">
                            <th *ngFor="let day of dateheader; let col = index"
                                class="border px-1 py-1 text-center align-middle h-[40px]" [ngClass]="{
                            'bg-gray-300 text-black': grayColumnsMap[col + 4] === 'light',
                            'bg-gray-600 text-white': grayColumnsMap[col + 4] === 'dark'
                        }">
                                {{ day }}
                            </th>
                        </tr>
                    </thead>
                    <tbody>
                        <tr *ngFor="let row of data">
                            <td *ngFor="let cell of row; let col = index" class="border px-1 py-1 align-middle h-[40px]"
                                [ngClass]="{
                                'bg-gray-300 text-black': grayColumnsMap[col] === 'light',
                                'bg-gray-600 text-white': grayColumnsMap[col] === 'dark',
                                'text-left': col === 1 && isTextOnly(cell),
                                'text-right': col === 1 && isAllNumber(cell),
                                'text-center': col !== 1
                                }">

                                <!-- ✅ กรณีพิเศษ: ช่องชื่อ (index = 1) -->
                                <ng-container *ngIf="col === 1">
                                    <ng-container *ngIf="isTextOnly(cell)">
                                        {{ cell.trim() }}
                                    </ng-container>
                                    <ng-container *ngIf="!isTextOnly(cell)">
                                        <div class="flex justify-between w-full">
                                            <span>{{ splitLeft(cell) }}</span>
                                            <span>{{ splitRight(cell) }}</span>
                                        </div>
                                    </ng-container>
                                </ng-container>

                                <!-- ✅ ช่องอื่น ๆ -->
                                <ng-container *ngIf="col !== 1">
                                    {{ cell }}
                                </ng-container>

                            </td>
                        </tr>
                    </tbody>
                    <tfoot>
                        <tr class="bg-gray-100 font-bold">
                            <td colspan="22" class="border px-2 py-2 text-right">รวม</td>
                            <td class="border px-2 py-2 text-center">{{ totalOT | number:'1.2-2' }}</td>
                            <td class="bg-gray-600 text-white border px-2 py-2 text-center">
                                {{ totalSum | number:'1.2-2' }}
                            </td>
                        </tr>
                    </tfoot>
                </table>
            </div>
        </div>
    </div>

</div>