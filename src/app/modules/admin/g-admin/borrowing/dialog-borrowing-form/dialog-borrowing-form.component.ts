import { Component, OnInit, Inject } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Form<PERSON>uilder, FormGroup, FormArray, Validators, ReactiveFormsModule, FormsModule } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA, MatDialogModule } from '@angular/material/dialog';
import { MatFormFieldModule } from '@angular/material/form-field';
import { MatInputModule } from '@angular/material/input';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatSelectModule } from '@angular/material/select';
import { MatDatepickerModule } from '@angular/material/datepicker';

import { MatCheckboxModule } from '@angular/material/checkbox';
import { BorrowingService } from '../borrowing.service';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { AuthService } from 'app/core/auth/auth.service';

@Component({
  selector: 'app-dialog-borrowing-form',
  standalone: true,
  imports: [
    CommonModule,
    ReactiveFormsModule,
    FormsModule,
    MatDialogModule,
    MatFormFieldModule,
    MatInputModule,
    MatButtonModule,
    MatIconModule,
    MatSelectModule,
    MatDatepickerModule,
    MatCheckboxModule,
  ],
  templateUrl: './dialog-borrowing-form.component.html',
  styleUrls: ['./dialog-borrowing-form.component.scss']
})
export class DialogBorrowingFormComponent implements OnInit {
  borrowingForm: FormGroup;
  isLoading = false;
  isEditMode = false;

  // Data for dropdowns
  users: any[] = [];
  equipments: any[] = [];

  constructor(
    public dialogRef: MatDialogRef<DialogBorrowingFormComponent>,
    @Inject(MAT_DIALOG_DATA) public data: any,
    private fb: FormBuilder,
    private borrowingService: BorrowingService,
    private fuseConfirmationService: FuseConfirmationService,
    private authService: AuthService,
  ) {
    this.isEditMode = !!data?.borrowingData;
    this.initForm();
  }

  ngOnInit(): void {
    this.loadMasterData();
    if (this.isEditMode) {
      this.populateForm();
    }
  }

  initForm(): void {
    this.borrowingForm = this.fb.group({
      user_id: [null, Validators.required],
      branch_id: [this.authService.currentBranch],
      borrow_date: [new Date(), Validators.required],
      expected_return_date: [null, Validators.required],
      purpose: ['', Validators.required],
      notes: [''],
      equipment_items: this.fb.array([])
    });
  }

  get equipmentItems(): FormArray {
    return this.borrowingForm.get('equipment_items') as FormArray;
  }

  createEquipmentItem(data?: any): FormGroup {
    return this.fb.group({
      equipment_id: [data?.equipment?.id || null, Validators.required],
      quantity: [data?.quantity || 1, [Validators.required, Validators.min(1)]],
      condition_before: [data?.condition_before || 'good', Validators.required],
      notes: [data?.notes || '']
    });
  }

  addEquipmentItem(): void {
    this.equipmentItems.push(this.createEquipmentItem());
  }

  removeEquipmentItem(index: number): void {
    this.equipmentItems.removeAt(index);
  }

  loadMasterData(): void {
    // Load users
    this.borrowingService.getUsers().subscribe(response => {
      this.users = response.data || [];
    });

    // Load equipments
    this.borrowingService.getEquipments().subscribe(response => {
      this.equipments = response.data || [];
    });
  }

  populateForm(): void {
    if (this.data.borrowingData) {
      const data = this.data.borrowingData;

      this.borrowingForm.patchValue({
        user_id: data.user_id,
        branch_id: data.branch_id,
        borrow_date: new Date(data.borrow_date),
        expected_return_date: new Date(data.expected_return_date),
        purpose: data.purpose,
        notes: data.notes
      });

      // Populate equipment items
      if (data.borrowing_items && data.borrowing_items.length > 0) {

        data.borrowing_items.forEach(item => {
          this.equipmentItems.push(this.createEquipmentItem(item));
        });
      }
    }
  }

  getEquipmentName(equipmentId: number): string {
    const equipment = this.equipments.find(eq => eq.id === equipmentId);
    return equipment ? equipment.name : '';
  }

  onSubmit(): void {
    if (this.borrowingForm.valid) {
      this.isLoading = true;

      const formData = this.borrowingForm.value;

      // Format dates
      formData.borrow_date = this.formatDate(formData.borrow_date);
      formData.expected_return_date = this.formatDate(formData.expected_return_date);

      const request = this.isEditMode
        ? this.borrowingService.updateBorrowing(this.data.borrowingData.id, formData)
        : this.borrowingService.createBorrowing(formData);

      request.subscribe({
        next: (response) => {
          this.isLoading = false;
          this.showSuccessMessage();
          this.dialogRef.close(response);
        },
        error: (error) => {
          this.isLoading = false;
          this.showErrorMessage(error.message || 'เกิดข้อผิดพลาดในการบันทึกข้อมูล');
        }
      });
    } else {
      this.markFormGroupTouched();
    }
  }

  formatDate(date: Date): string {
    if (!date) return '';
    const d = new Date(date);
    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    return `${year}-${month}-${day}`;
  }

  markFormGroupTouched(): void {
    Object.keys(this.borrowingForm.controls).forEach(key => {
      const control = this.borrowingForm.get(key);
      control?.markAsTouched();

      if (control instanceof FormArray) {
        control.controls.forEach(item => {
          if (item instanceof FormGroup) {
            Object.keys(item.controls).forEach(itemKey => {
              item.get(itemKey)?.markAsTouched();
            });
          }
        });
      }
    });
  }

  showSuccessMessage(): void {
    this.fuseConfirmationService.open({
      title: 'สำเร็จ',
      message: this.isEditMode ? 'แก้ไขใบขอยืมเรียบร้อย' : 'สร้างใบขอยืมเรียบร้อย',
      icon: {
        show: true,
        name: 'heroicons_outline:check-circle',
        color: 'success'
      },
      actions: {
        confirm: {
          show: false
        },
        cancel: {
          show: false
        }
      },
      dismissible: true
    });
  }

  showErrorMessage(message: string): void {
    this.fuseConfirmationService.open({
      title: 'เกิดข้อผิดพลาด',
      message: message,
      icon: {
        show: true,
        name: 'heroicons_outline:exclamation-triangle',
        color: 'warn'
      },
      actions: {
        confirm: {
          show: false
        },
        cancel: {
          show: false
        }
      },
      dismissible: true
    });
  }

  onCancel(): void {
    this.dialogRef.close();
  }
}
