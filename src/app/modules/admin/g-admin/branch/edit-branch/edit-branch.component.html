<div
    class="flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between p-1 sm:pb-4 sm:px-10 border-b bg-card dark:bg-transparent">
    <div class="flex-1 min-w-0">
        <!-- Title -->
        <div class="mt-2">
            <h1 class="text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                แก้ไขสาขา
            </h1>
        </div>
    </div>
</div>
<div class="flex-auto p-3 sm:p-2">
    <form [formGroup]="formData">
        <div class="flex mt-4">
            <label for="name"
                class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">ชื่อสาขา</label>
            <input id="name"
                class="mb-5 mt-2 text-gray-600 focus:outline-none focus:border focus:border-yellow-600 font-normal w-full h-10 flex items-center pl-3 text-md border-gray-300 rounded border"
                placeholder="กรุณาระบุชื่อสาขา" formControlName="name" />
        </div>
        <div class="flex mt-4">
            <mat-label class="text-gray-800 text-lg font-bold leading-tight tracking-normal w-1/3 mt-4">สถานะ
            </mat-label>
            <mat-form-field appearance="fill" class="w-full pr-2 ">
                <mat-select [formControlName]="'status'">
                    <mat-option *ngFor="let sta of statusData" [value]="sta.id">
                        {{sta.name}}
                    </mat-option>
                </mat-select>
            </mat-form-field>
        </div>
        <div class="flex items-center justify-end w-full border-t px-8 py-4 mt-4">
            <div class="flex items-center justify-end">
                <button mat-flat-button class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4" (click)="onClose()">
                    <mat-icon svgIcon="heroicons_solid:x-mark" class="mr-2"></mat-icon>
                    ยกเลิก
                </button>
                <button class="px-6 ml-3 mat-primary" mat-flat-button (click)="UpdateBranch()">
                    <mat-icon svgIcon="heroicons_solid:check"></mat-icon>
                    ยืนยัน
                </button>
            </div>
        </div>
    </form>
</div>
