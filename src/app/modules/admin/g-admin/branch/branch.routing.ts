import { Route, Routes } from '@angular/router';
// import { CreateUserComponent } from './create-user/create-user.component';
// import { UserListComponent } from './list/list.component';




// import { AssetTypeResolver, PermissionProductsResolver } from './user.resolvers';


export default [
    // {
    //     path: '',
    //     pathMatch: 'full',
    //     redirectTo: 'brief-plan'
    // },
    {
        path: '',
        loadComponent: () => import('./branch.component').then(m => m.BranchComponent),
        children: [
            {
                path: 'list',
                loadComponent: () => import('./list/list.component').then(m => m.BranchListComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },
            {
                path: 'new-branch',
                loadComponent: () => import('./new-branch/new-branch.component').then(m => m.NewBranchComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },
            {
                path: 'edit/:id',
                loadComponent: () => import('./edit-branch/edit-branch.component').then(m => m.EditBranchComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },


        ]

        /*children : [
            {
                path     : '',
                component: ContactsListComponent,
                resolve  : {
                    tasks    : ContactsResolver,
                    countries: ContactsCountriesResolver
                },
                children : [
                    {
                        path         : ':id',
                        component    : ContactsDetailsComponent,
                        resolve      : {
                            task     : ContactsContactResolver,
                            countries: ContactsCountriesResolver
                        },
                        canDeactivate: [CanDeactivateContactsDetails]
                    }
                ]
            }
        ]*/
    }
] as Routes
