<div class="flex flex-col flex-auto min-w-0">
    <!-- Header -->
    <!-- Loader -->
    <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
        <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
    </div>
    <div class="flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between p-6 sm:py-8 sm:px-10 border-b bg-card dark:bg-transparent">
        <div class="flex-1 min-w-0">
            <!-- Title -->
            <div class="flex flex-row mt-2 justify-between">
                <h2 class="text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                    พิมพ์เอกสาร
                </h2>
            </div>
        </div>
    </div>

    <!-- Main -->
    <div class="flex-auto p-6 sm:p-10">
      <div class="bg-card rounded-2xl shadow overflow-hidden p-8 mb-8">
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4">หนังสือรับรองการหักภาษี 50 ทวิ</h3>

                <!-- ปีเอกสาร -->
                <div class="mb-6">
                    <mat-form-field appearance="outline" class="w-full md:w-1/3">
                        <mat-label>ปีเอกสาร</mat-label>
                        <mat-select [(value)]="selectedYear50">
                            <mat-option *ngFor="let year of yearList" [value]="year">
                                {{ year }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>

                <div class="flex flex-wrap gap-4 mt-8">
                    <button mat-stroked-button color="primary" [disabled]="!selectedYear50" (click)="downloadReport('tax_book_fifty')" >
                        <mat-icon>download</mat-icon>
                        ดาวน์โหลดหนังสือรับรองการหักภาษี 50 ทวิ
                    </button>
                </div>
            </div>

        </div>

           <div class="bg-card rounded-2xl shadow overflow-hidden p-8 mb-8">
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4">รายงาน ภ.ง.ด 1ก</h3>

                <!-- ปีเอกสาร -->
                <div class="mb-6">
                    <mat-form-field appearance="outline" class="w-full md:w-1/3">
                        <mat-label>ปีเอกสาร</mat-label>
                        <mat-select [(value)]="selectedYearpnk">
                            <mat-option *ngFor="let year of yearList" [value]="year">
                                {{ year }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>

                <div class="flex flex-wrap gap-4 mt-8">
                    <button mat-stroked-button color="primary" [disabled]="!selectedYearpnk " (click)="downloadReport('attached_pnk')" >
                        <mat-icon>download</mat-icon>
                        ดาวน์โหลดรายงาน ภ.ง.ด 1ก
                    </button>
                </div>
            </div>

        </div>
        <div class="bg-card rounded-2xl shadow overflow-hidden p-8 mb-8">
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4">รายงานธนาคาร</h3>

                <!-- ปีเอกสาร -->
                <div class="mb-6">
                    <mat-form-field appearance="outline" class="w-full md:w-1/3">
                        <mat-label>ปีเอกสาร</mat-label>
                        <mat-select [(value)]="selectedYearbank">
                            <mat-option *ngFor="let year of yearList" [value]="year">
                                {{ year }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>

                <!-- เลือกเดือน -->
                <div class="mb-6">
                    <mat-form-field appearance="outline" class="w-full md:w-1/3">
                        <mat-label>เดือน</mat-label>
                        <mat-select [(value)]="selectedMonth">
                            <mat-option *ngFor="let month of monthList" [value]="month.value">
                                {{ month.name }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>

                <!-- เลือกรอบ -->
                <div class="mb-6">
                    <mat-form-field appearance="outline" class="w-full md:w-1/3">
                        <mat-label>รอบ</mat-label>
                           <mat-select [(value)]="selectedRound">
                          <mat-option *ngFor="let round of roundpay" [value]="round.round">
                                {{ round.name }}
                            </mat-option>
                              </mat-select>
                        </mat-form-field>
                </div>

                <!-- ปุ่มดำเนินการ -->
                <div class="flex flex-wrap gap-4 mt-8">
                    <button mat-stroked-button color="primary" [disabled]="!selectedYearbank && !selectedMonth && !selectedRound" (click)="downloadReport('export_bank')" >
                        <mat-icon>download</mat-icon>
                        ดาวน์โหลดรายงานธนาคาร
                    </button>
                </div>
            </div>

        </div>
        <div class="bg-card rounded-2xl shadow overflow-hidden p-8 mb-8">
            <div class="mb-8">
                <h3 class="text-xl font-semibold mb-4">รายงานประกันสังคม</h3>

                <!-- ปีเอกสาร -->
                <div class="mb-6">
                    <mat-form-field appearance="outline" class="w-full md:w-1/3">
                        <mat-label>ปีเอกสาร</mat-label>
                        <mat-select [(value)]="selectedYearbank">
                            <mat-option *ngFor="let year of yearList" [value]="year">
                                {{ year }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>

                <!-- เลือกเดือน -->
                <div class="mb-6">
                    <mat-form-field appearance="outline" class="w-full md:w-1/3">
                        <mat-label>เดือน</mat-label>
                        <mat-select [(value)]="selectedMonth">
                            <mat-option *ngFor="let month of monthList" [value]="month.value">
                                {{ month.name }}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                <!-- ปุ่มดำเนินการ -->
                <div class="flex flex-wrap gap-4 mt-8">
                    <button mat-stroked-button color="primary" [disabled]="!selectedYearbank && !selectedMonth" (click)="downloadReport('export_social')" >
                        <mat-icon>download</mat-icon>
                        ดาวน์โหลดรายงานประกันสังคม
                    </button>
                </div>
            </div>

        </div>
    </div>
</div>
