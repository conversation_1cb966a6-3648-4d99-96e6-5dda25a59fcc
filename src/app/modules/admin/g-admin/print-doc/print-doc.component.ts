import { HttpClient, HttpResponse } from '@angular/common/http';
import {
  ChangeDetectionStrategy,
  ChangeDetectorRef,
  Component,
  ViewEncapsulation,
} from '@angular/core';
import { FormBuilder, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { ActivatedRoute, Router } from '@angular/router';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { PrintDocService } from './print-doc.service';
import { NgIf, NgClass, CommonModule } from '@angular/common';
import { MatProgressBar } from '@angular/material/progress-bar';
import { MatFormField, MatLabel } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { MatSelect } from '@angular/material/select';
import { MatOption } from '@angular/material/autocomplete';
import { MatDialogActions } from '@angular/material/dialog';
import { MatButton } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { environment } from 'environments/environment';
import { createFileFromBlob } from 'app/helper';
import { saveAs } from 'file-saver';

@Component({
  selector: 'print-doc',
  templateUrl: './print-doc.component.html',
  encapsulation: ViewEncapsulation.None,
  changeDetection: ChangeDetectionStrategy.OnPush,
  imports: [NgIf, CommonModule, MatProgressBar, MatIconModule, FormsModule, ReactiveFormsModule, MatFormField, NgClass, MatLabel, MatInput, MatSelect, MatOption, MatDialogActions, MatButton]
})
export class PrintDocComponent {
  isLoading = false;
  selectedYear50: number;
  selectedYearpnk: number;
  selectedYearbank: number;

  selectedDocType: string;
  documentGenerated = false;
  selectedRound: any;
  roundpay: any[]
  // สร้างรายปีให้เลือก (5 ปีย้อนหลังและปีปัจจุบัน)
  yearList: number[] = [];
  selectedMonth: any;
  monthList = [
    { value: '01', name: 'มกราคม' },
    { value: '02', name: 'กุมภาพันธ์' },
    { value: '03', name: 'มีนาคม' },
    { value: '04', name: 'เมษายน' },
    { value: '05', name: 'พฤษภาคม' },
    { value: '06', name: 'มิถุนายน' },
    { value: '07', name: 'กรกฎาคม' },
    { value: '08', name: 'สิงหาคม' },
    { value: '09', name: 'กันยายน' },
    { value: '10', name: 'ตุลาคม' },
    { value: '11', name: 'พฤศจิกายน' },
    { value: '12', name: 'ธันวาคม' }
  ];
  constructor(private fb: FormBuilder,
    private PrintDocService: PrintDocService,
  ) {
    const currentYear = new Date().getFullYear() + 543;
    for (let i = 0; i < 6; i++) {
      this.yearList.push(currentYear - i);
    }
  }

  ngOnInit(): void {
    this.PrintDocService.getRoundpayroll().subscribe((resp: any) => {
      console.log(resp)
      this.roundpay = resp.data
    })
  }

  generateDocument() {
    this.isLoading = true;
    // จำลองการโหลดข้อมูล
    setTimeout(() => {
      this.documentGenerated = true;
      this.isLoading = false;
    }, 1500);
  }

  downloadReport(type: string) {
    if (type == 'attached_pnk') {
      const yearthai = this.selectedYearpnk - 543

      this.PrintDocService.previewdoc(type, yearthai).subscribe((resp: any) => {

        const fileUrl = URL.createObjectURL(resp.body!);

        // เปิดไฟล์ในแท็บใหม่
        window.open(fileUrl, '_blank');
        //  const pdfUrl = URL.createObjectURL(resp);

      })
    }
    if (type == 'tax_book_fifty') {
      const yearthai = this.selectedYear50 - 543
      this.PrintDocService.previewdoc(type, yearthai).subscribe((resp: any) => {

        const fileUrl = URL.createObjectURL(resp.body!);

        // เปิดไฟล์ในแท็บใหม่
        window.open(fileUrl, '_blank');
        //  const pdfUrl = URL.createObjectURL(resp);

      })
    }

    if (type === 'export_bank') {
      const yearthai = this.selectedYearbank - 543;
      this.PrintDocService.previewdoc(type, yearthai, this.selectedMonth, this.selectedRound)
        .subscribe({
          next: (resp: HttpResponse<Blob>) => {
            if (resp.body) {
              const filename = `${type}_${this.selectedMonth}_${this.selectedRound}_${this.selectedYearbank}.xls`;
              const blob = new Blob([resp.body], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
              });
              createFileFromBlob(blob, filename); // ใช้ resp.body โดยมั่นใจว่าเป็น Blob
            } else {
              console.error('❌ ไม่มีข้อมูลไฟล์ใน response');
            }
          },
          error: (err) => {
            console.error('❌ API error:', err);
          }
        });
    }

    if (type === 'export_social') {
      const yearthai = this.selectedYearbank - 543;
      const round = '1'
      this.PrintDocService.previewdoc(type, yearthai, this.selectedMonth, round)
        .subscribe({
          next: (resp: HttpResponse<Blob>) => {
            if (resp.body) {
              const filename = `${type}_${this.selectedMonth}_${this.selectedYearbank}.xls`;
              const blob = new Blob([resp.body], {
                type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
              });
              createFileFromBlob(blob, filename); // ใช้ resp.body โดยมั่นใจว่าเป็น Blob
            } else {
              console.error('❌ ไม่มีข้อมูลไฟล์ใน response');
            }
          },
          error: (err) => {
            console.error('❌ API error:', err);
          }
        });
    }
  }

  printDocument() {
    window.print();
  }
}
