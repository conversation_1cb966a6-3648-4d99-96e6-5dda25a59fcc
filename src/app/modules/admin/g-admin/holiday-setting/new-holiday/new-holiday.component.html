<div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">
    <!-- Header -->
    <div class="flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between p-6 sm:pb-4 sm:px-10 border-b bg-card dark:bg-transparent">
        <div class="flex-1 min-w-0">
            <div class="mt-2">
                <h2 class="text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                    เพิ่มกลุ่มกะการทำงาน
                </h2>
            </div>
        </div>
    </div>
    <!-- Main Content -->
    <div class="flex-auto p-3 sm:p-6">
        <div class="overflow-auto bg-white shadow sm:rounded-lg">
            <div class="grid gap-4 w-full sm:grid-cols-1 md:grid-cols-1 bg-card p-4">
                <form [formGroup]="formGroup">
                    <!-- Group Name -->
                    <div class="flex flex-col sm:flex-row mb-6">
                        <mat-form-field class="w-full sm:w-1/2 px-3">
                            <mat-label>ชื่อกลุ่ม</mat-label>
                            <input matInput formControlName="name" >
                        </mat-form-field>
                    </div>
                    <!-- Work Shifts Table -->
                    <div class="overflow-x-auto">
                        <table class="w-full table-auto text-left border-collapse border border-gray-300 shadow-md">
                            <thead>
                                <tr>
                                    <th class="px-4 py-2">วัน</th>
                                    <th class="px-4 py-2">เวลาเข้างาน</th>
                                    <th class="px-4 py-2">เวลาออกงาน</th>
                                    <th class="px-4 py-2">เวลาเริ่มพักกลางวัน</th>
                                    <th class="px-4 py-2">เวลาหมดพักกลางวัน</th>
                                    <th class="px-4 py-2">สถานะ</th>
                                </tr>
                            </thead>
                            <tbody formArrayName="work_shift_time">
                                <tr *ngFor="let shift of Workshift.controls; let i = index" [formGroupName]="i">
                                    <td class="px-4 py-2 border border-gray-300">{{ shift.value.fullname }}</td>
                                    <td class="px-4 py-2 border border-gray-300">
                                        <div class="flex flex-col p-4">
                                            <div class="">
                                                <div>เวลาเข้างาน</div>
                                                <input matInput type="time" formControlName="time_in"
                                                class="pt-4 w-full max-w-xs border border-gray-300 rounded-md bg-gray-100 text-gray-700 px-2 py-1 focus:border-blue-500 focus:ring focus:ring-blue-200">
                                            </div>
                                            <div class="pt-2">
                                                <div>เวลาเริ่มเข้างาน</div>
                                                <input matInput type="time" formControlName="time_in_start"
                                                class="pt-4 w-full max-w-xs border border-gray-300 rounded-md bg-gray-100 text-gray-700 px-2 py-1 focus:border-blue-500 focus:ring focus:ring-blue-200">
                                            </div>
                                            <div class="pt-2">
                                                <div>เวลาสิ้นสุดเข้างาน</div>
                                                <input matInput type="time" formControlName="time_in_end"
                                                class="pt-4 w-full max-w-xs border border-gray-300 rounded-md bg-gray-100 text-gray-700 px-2 py-1 focus:border-blue-500 focus:ring focus:ring-blue-200">
                                            </div>
                                        </div>

                                    </td>
                                    <td class="px-4 py-2 border border-gray-300">
                                        <div class="flex flex-col p-4">
                                            <div>
                                                <div>เวลาออกงาน</div>
                                                <input matInput type="time" formControlName="time_out"
                                                class="pt-4 w-full max-w-xs border border-gray-300 rounded-md bg-gray-100 text-gray-700 px-2 py-1 focus:border-blue-500 focus:ring focus:ring-blue-200">
                                            </div>
                                            <div class="pt-2">
                                                <div>เวลาเริ่มออกงาน</div>
                                                <input matInput type="time" formControlName="time_out_start"
                                                class="pt-4 w-full max-w-xs border border-gray-300 rounded-md bg-gray-100 text-gray-700 px-2 py-1 focus:border-blue-500 focus:ring focus:ring-blue-200">
                                            </div>
                                            <div class="pt-2">
                                                <div>เวลาสิ้นสุดออกงาน</div>
                                                <input matInput type="time" formControlName="time_out_end"
                                                class="pt-4 w-full max-w-xs border border-gray-300 rounded-md bg-gray-100 text-gray-700 px-2 py-1 focus:border-blue-500 focus:ring focus:ring-blue-200">
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-4 py-2 border border-gray-300">
                                        <div class="flex flex-col p-4">
                                            <div>
                                                <div>เวลาพัก</div>
                                                <input matInput type="time" formControlName="time_brake_in"
                                                class="pt-4 w-full max-w-xs border border-gray-300 rounded-md bg-gray-100 text-gray-700 px-2 py-1 focus:border-blue-500 focus:ring focus:ring-blue-200">
                                            </div>
                                            <div class="pt-2">
                                                <div>เวลาเริ่มพัก</div>
                                                <input matInput type="time" formControlName="time_brake_in_start"
                                                class="pt-4 w-full max-w-xs border border-gray-300 rounded-md bg-gray-100 text-gray-700 px-2 py-1 focus:border-blue-500 focus:ring focus:ring-blue-200">
                                            </div>
                                            <div class="pt-2">
                                                <div>เวลาสิ้นสุดพัก</div>
                                                <input matInput type="time" formControlName="time_brake_in_end"
                                                class="pt-4 w-full max-w-xs border border-gray-300 rounded-md bg-gray-100 text-gray-700 px-2 py-1 focus:border-blue-500 focus:ring focus:ring-blue-200">
                                            </div>
                                        </div>

                                    </td>
                                    <td class="px-4 py-2 border border-gray-300">
                                        <div class="flex flex-col p-4">
                                            <div>
                                                <div>เวลาหยุดพัก</div>
                                                <input matInput type="time" formControlName="time_brake_out"
                                                class="pt-4 w-full max-w-xs border border-gray-300 rounded-md bg-gray-100 text-gray-700 px-2 py-1 focus:border-blue-500 focus:ring focus:ring-blue-200">
                                            </div>
                                            <div class="pt-2">
                                                <div>เวลาเริ่มหยุดพัก</div>
                                                <input matInput type="time" formControlName="time_brake_out_start"
                                                class="pt-4 w-full max-w-xs border border-gray-300 rounded-md bg-gray-100 text-gray-700 px-2 py-1 focus:border-blue-500 focus:ring focus:ring-blue-200">
                                            </div>
                                            <div class="pt-2">
                                                <div>เวลาสิ้นสุดหยุดพัก</div>
                                                <input matInput type="time" formControlName="time_brake_out_end"
                                                class="pt-4 w-full max-w-xs border border-gray-300 rounded-md bg-gray-100 text-gray-700 px-2 py-1 focus:border-blue-500 focus:ring focus:ring-blue-200">
                                            </div>
                                        </div>

                                    </td>
                                    <td class="px-4 py-2 text-start border border-gray-300">
                                        <div class="relative inline-flex items-center cursor-pointer" (click)="shift.patchValue({ status: !shift.value.status })">
                                            <span class="mr-3 text-sm font-medium"
                                                [ngClass]="{'text-green-500': shift.value.status, 'text-red-400': !shift.value.status}">
                                                {{ shift.value.status ? 'วันทำงาน' : 'วันหยุด' }}
                                            </span>
                                            <div class="relative rounded-xl inline-block w-10 h-6 transition duration-200 ease-in"
                                                [ngClass]="{'bg-green-500': shift.value.status, 'bg-gray-300': !shift.value.status}">
                                                <span class="absolute left-1 top-1 bg-white w-4 h-4 rounded-full transition-transform transform"
                                                    [ngClass]="{'translate-x-4': shift.value.status, 'translate-x-0': !shift.value.status}"></span>
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            </tbody>
                        </table>
                    </div>


                   <div class="flex justify-center gap-2 mt-5">
                        <button class="bg-blue-500 text-white px-4 py-2 rounded-md" mat-flat-button (click)="Submit()">
                            <mat-icon svgIcon="heroicons_solid:check"></mat-icon> บันทึก
                        </button>
                        <button class="bg-red-500 text-white px-4 py-2 rounded-md" mat-flat-button (click)="Back()">
                             ย้อนกลับ
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
