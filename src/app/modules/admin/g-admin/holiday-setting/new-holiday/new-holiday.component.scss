.container {
    display: flex;
    flex-flow: wrap;
    flex-wrap: wrap;
}

.card1 {
    width: 50%;
    height: 100%;
    flex-direction: column;

    justify-content: center !important;
    flex: 1;
    padding: 2rem;
    border: 1px solid rgb(210, 210, 210);
}


.card2 {
    width: 50%;
    height: 100%;
    flex: 1;
    flex-direction: column;
    justify-content: center !important;
    padding: 2rem;
    border: 1px solid rgb(210, 210, 210);

}

.card3 {
    width: 100%;
    height: 100%;
    flex: 2;
    justify-content: center !important;
    flex-direction: column;
    padding: 2rem;
    border: 1px solid rgb(210, 210, 210);

}

.card_sum {

    width: 30% !important;
    height: 100%;
    padding: 20px !important;
    padding: 2rem;
    border: 1px solid rgb(210, 210, 210);
    border-radius: 5%;


}

.example-button-row .mat-mdc-button-base {
    margin: 8px 8px 8px 0;
}

.img {
    display: flex;
    width: 100%;
    height: 200px;
    align-items: center !important;
    background: #fff !important;
    color: #717386 !important;
    border: 2px dashed #717386 !important;
    border-radius: 5px !important;
    font-size: 16px !important;
    overflow-x: auto !important;
}


.img {
    display: flex;
    width: 300px;
    height: 300px;
    align-items: center !important;
    background: #fff !important;
    color: #717386 !important;
    border: 2px solid #717386 !important;
    border-radius: 5px !important;
    font-size: 16px !important;
    overflow-x: auto !important;
    cursor: pointer;
}

#file {
    display: none;

}

:host {
    display: block;
  }

  .timeline-item {
    transition: all 0.3s ease-in-out;
  }

  .timeline-item:hover {
    transform: translateX(5px);
  }
