import {
    AfterViewInit,
    ChangeDetectorRef,
    Component,
    OnDestroy,
    OnInit,
    ViewChild,
} from '@angular/core';
import { FormArray, FormBuilder, FormControl, FormGroup, Validators, FormsModule, ReactiveFormsModule } from '@angular/forms';
import { MatPaginator } from '@angular/material/paginator';
import { MatSort } from '@angular/material/sort';
import { map, Observable, Subject, startWith, lastValueFrom, ReplaySubject, takeUntil } from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';

import { PositionService } from '../../position/position.service';
import { DepartmentService } from '../../department/department.service';
import { BranchService } from '../../branch/branch.service';
import { ModalItem } from '../../item/modal-item/modal-item.component';
import { DialogAddressComponent } from '../../customer/dialog-address/dialog-address.component';
import { DialogCustomerComponent } from '../../customer/dialog-customer/dialog-customer.component';
import { DialogEditCustomerComponent } from '../../customer/dialog-edit-customer/page.component';
import { CalendarService } from '../../calendar/calendar.service';
import { HolidaySettingService } from '../holiday-setting.service';
import { Location, NgFor, NgClass } from '@angular/common';
import { MatFormField, MatLabel } from '@angular/material/form-field';
import { MatInput } from '@angular/material/input';
import { MatButton } from '@angular/material/button';
import { MatIcon } from '@angular/material/icon';

@Component({
    selector: 'app-new-holiday',
    templateUrl: './new-holiday.component.html',
    styleUrls: ['./new-holiday.component.scss'],
    animations: fuseAnimations,
    imports: [FormsModule, ReactiveFormsModule, MatFormField, MatLabel, MatInput, NgFor, NgClass, MatButton, MatIcon]
})
export class NewHolidayComponent {
    @ViewChild(MatPaginator) private _paginator: MatPaginator;
    @ViewChild(MatSort) private _sort: MatSort;

    formGroup: FormGroup;
    daysOfWeek = [
        { full: 'จันทร์', short: 'Mon' },
        { full: 'อังคาร', short: 'Tue' },
        { full: 'พุธ', short: 'Wed' },
        { full: 'พฤหัสบดี', short: 'Thu' },
        { full: 'ศุกร์', short: 'Fri' },
        { full: 'เสาร์', short: 'Sat' },
        { full: 'อาทิตย์', short: 'Sun' }
    ];

    /**
     * Constructor
     */
    editid: any;
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _fuseConfirmationService: FuseConfirmationService,
        private _formBuilder: FormBuilder,
        // private _Service: PermissionService,
        private _Service: HolidaySettingService,
        private _ServicePosition: PositionService,
        private _ServiceDepartment: DepartmentService,
        private _ServiceBranch: BranchService,
        private _matDialog: MatDialog,
        private _router: Router,
        private _activatedRoute: ActivatedRoute,
        private _authService: AuthService,
        private _serviceCalendar: CalendarService,
        private location: Location,
    ) {
        this.editid = this._activatedRoute.snapshot.paramMap.get('id')

        this.formGroup = this._formBuilder.group({
            name: [''],
            status: true,
            work_shift_time: this._formBuilder.array([]),
        });



    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    async ngOnInit() {

        if (this.editid) {
            this._Service.getWorkshiftById(this.editid).subscribe((resp: any) => {

                this.formGroup.patchValue({
                    name: resp.data.name
                })
                for (const item of resp.data.work_shift_times) {
                    this.addShiftbyid(item)
                }
            })
        }
        else {
            for (const day of this.daysOfWeek) {
                this.addShift(day)
            }
        }
    }

    addShift(shift?: any) {
        const shiftGroup = this._formBuilder.group({
            day: [shift?.short || ''],
            fullname: [shift?.full || ''],
            time_in: [shift?.time_in || ''],
            time_in_start: [shift?.time_in_start || ''],
            time_in_end: [shift?.time_in_end || ''],
            time_out: [shift?.time_out || ''],
            time_out_start: [shift?.time_out_start || ''],
            time_out_end: [shift?.time_out_end || ''],
            time_brake_in: [shift?.time_brake_in || ''],
            time_brake_in_start: [shift?.time_brake_in_start || ''],
            time_brake_in_end: [shift?.time_brake_in_end || ''],
            time_brake_out: [shift?.time_brake_out || ''],
            time_brake_out_start: [shift?.time_brake_out_start || ''],
            time_brake_out_end: [shift?.time_brake_out_end || ''],
            status: [shift?.status || false]
        });
        this.Workshift.push(shiftGroup);
    }
    addShiftbyid(shift?: any) {
        const filterday = this.daysOfWeek.find(day => day.short === shift?.day);

        const shiftGroup = this._formBuilder.group({
            day: [shift?.day || ''],
            fullname: [filterday.full || ''],
            time_in: [shift?.time_in || ''],
            time_in_start: [shift?.time_in_start || ''],
            time_in_end: [shift?.time_in_end || ''],
            time_out: [shift?.time_out || ''],
            time_out_start: [shift?.time_out_start || ''],
            time_out_end: [shift?.time_out_end || ''],
            time_brake_in: [shift?.time_brake_in || ''],
            time_brake_in_start: [shift?.time_brake_in_start || ''],
            time_brake_in_end: [shift?.time_brake_in_end || ''],
            time_brake_out: [shift?.time_brake_out || ''],
            time_brake_out_start: [shift?.time_brake_out_start || ''],
            time_brake_out_end: [shift?.time_brake_out_end || ''],
            status: [shift?.status === 1]
        });
        this.Workshift.push(shiftGroup);
    }

    get Workshift() {
        return this.formGroup.get("work_shift_time") as FormArray;
    }


    removeEmployee(index: any) {
        this.Workshift.removeAt(index);
    }

    private createShiftFormGroup(day: { full: string; short: string }): FormGroup {
        return this._formBuilder.group({
            day: [day.short],
            fullname: [day.full],
            time_in: [''],
            time_out: [''],
            status: [false]
        });
    }

    Submit() {
        if (this.editid) {
            const confirmation = this._fuseConfirmationService.open({
                title: 'ยืนยันการแก้ไขข้อมูลกะการทำงาน',
                message: 'คุณต้องการแก้ไขข้อมูลใช่หรือไม่ ?',
                icon: {
                    show: true,
                    name: 'heroicons_outline:exclamation-triangle',
                    color: 'warning',
                },
                actions: {
                    confirm: {
                        show: true,
                        label: 'ยืนยัน',
                        color: 'primary',
                    },
                    cancel: {
                        show: true,
                        label: 'ยกเลิก',
                    },
                },
                dismissible: true,
            });
            confirmation.afterClosed().subscribe((result) => {
                // If the confirm button pressed...
                if (result === 'confirmed') {
                    this._Service.updateWorkshift(this.editid, this.formGroup.value).subscribe((resp: any) => {
                        this.location.back()
                    })
                }
            });
        }
        else {
            const confirmation = this._fuseConfirmationService.open({
                title: 'ยืนยันการเพิ่มข้อมูลกะการทำงาน',
                message: 'คุณต้องการเพิ่มข้อมูลใช่หรือไม่ ?',
                icon: {
                    show: true,
                    name: 'heroicons_outline:exclamation-triangle',
                    color: 'warning',
                },
                actions: {
                    confirm: {
                        show: true,
                        label: 'ยืนยัน',
                        color: 'primary',
                    },
                    cancel: {
                        show: true,
                        label: 'ยกเลิก',
                    },
                },
                dismissible: true,
            });
            confirmation.afterClosed().subscribe((result) => {
                // If the confirm button pressed...
                if (result === 'confirmed') {
                    this._Service.createWorkShift(this.formGroup.value).subscribe((resp: any) => {
                        this.location.back()
                    })
                }
            });
        }

    }


    Back() {
        this.location.back()
    }
}


