import { Route, Routes } from '@angular/router';
// import { CreateUserComponent } from './create-user/create-user.component';
// import { UserListComponent } from './list/list.component';

import { StatusComponent } from '../item-return/status/status.component';
import { HolidaySettingComponent } from './holiday-setting.component';


// import { AssetTypeResolver, PermissionProductsResolver } from './user.resolvers';

export default [
    // {
    //     path: '',
    //     pathMatch: 'full',
    //     redirectTo: 'brief-plan'
    // },
    {
        path: '',
        component:HolidaySettingComponent,
        children: [
            {
                path: 'list',
                loadComponent: () => import('./list/list.component').then(m => m.ListComponent),
            },
            {
                path: 'add-holiday',
                loadComponent: () => import('./new-holiday/new-holiday.component').then(m => m.NewHolidayComponent),
            },
            {
                path: 'edit-holiday/:id',
                loadComponent: () => import('./new-holiday/new-holiday.component').then(m => m.NewHolidayComponent),
            },
        ],

        /*children : [
            {
                path     : '',
                component: ContactsListComponent,
                resolve  : {
                    tasks    : ContactsResolver,
                    countries: ContactsCountriesResolver
                },
                children : [
                    {
                        path         : ':id',
                        component    : ContactsDetailsComponent,
                        resolve      : {
                            task     : ContactsContactResolver,
                            countries: ContactsCountriesResolver
                        },
                        canDeactivate: [CanDeactivateContactsDetails]
                    }
                ]
            }
        ]*/
    },
] as Routes
