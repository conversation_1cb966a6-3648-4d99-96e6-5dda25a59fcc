import { Route, Routes } from '@angular/router';







export default [
    // {
    //     path: '',
    //     pathMatch: 'full',
    //     redirectTo: 'brief-plan'
    // },
    {
        path: '',
        loadComponent: () => import('./bank.component').then(m => m.BankComponent),
        children: [
            {
                path: 'list',
                loadComponent: () => import('./list-bank/list-bank.component').then(m => m.ListBankComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },
            {
                path: 'new-bank',
                loadComponent: () => import('./new-bank/new-bank.component').then(m => m.NewBankComponent),
                // resolve: {
                //     permission: PermissionProductsResolver,
                //     department: DepartmentResolver,
                //     resolveGet: PositionResolve,
                //     branch: BranchResolver,
                // }
            },
            {
                path: 'edit/:id',
                loadComponent: () => import('./edit-bank/edit-bank.component').then(m => m.EditBankComponent),
                // resolve: {
                //     products: PermissionProductsResolver,

                // }
            },



        ]
        /*children : [
            {
                path     : '',
                component: ContactsListComponent,
                resolve  : {
                    tasks    : ContactsResolver,
                    countries: ContactsCountriesResolver
                },
                children : [
                    {
                        path         : ':id',
                        component    : ContactsDetailsComponent,
                        resolve      : {
                            task     : ContactsContactResolver,
                            countries: ContactsCountriesResolver
                        },
                        canDeactivate: [CanDeactivateContactsDetails]
                    }
                ]
            }
        ]*/
    }
] as Routes
