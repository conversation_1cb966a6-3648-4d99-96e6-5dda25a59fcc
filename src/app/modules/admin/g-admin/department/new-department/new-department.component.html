<div class="flex flex-col flex-auto min-w-0 bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between p-2 sm:pb-4 sm:px-10 border-b bg-card dark:bg-transparent">
        <div class="flex-1 min-w-0">
            <!-- Title -->
            <div class="mt-2">
                <h2 class="text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                    สร้างแผนกใหม่
                </h2>
            </div>

        </div>
    </div>

    <!-- Main -->
    <div class="flex-auto p-3">
        <form class="flex flex-col mt-3 p-0 pb-4 bg-card shadow overflow-hidden ng-valid" [formGroup]="formData">
            <div class="flex flex-col sm:flex-row p-1">


                <div class="flex flex-auto flex-wrap">

                    <div class="flex flex-col w-full lg:w-4/4 sm:pl-8">
                        <!-- name -->

                        <div class="flex mt-4">
                            <mat-form-field class="w-full pr-2">
                                <mat-label>ชื่อแผนก</mat-label>
                                <input matInput [formControlName]="'name'">
                            </mat-form-field>
                        </div>
                    </div>
                </div>
            </div>
            <!-- button -->
            <div class="flex items-center justify-end w-full border-t px-8 py-4">
                <div class="flex items-center justify-end">
                    <a class="ml-4" mat-flat-button (click)="onClose()">
                        <mat-icon svgIcon="heroicons_solid:x-mark"></mat-icon>
                        ยกเลิก
                    </a>
                    <button class="px-6 ml-3 mat-primary" mat-flat-button (click)="createNewBranch()">
                        <mat-icon svgIcon="heroicons_solid:check"></mat-icon>
                        ยืนยัน
                    </button>
                </div>
            </div>
        </form>
    </div>

</div>