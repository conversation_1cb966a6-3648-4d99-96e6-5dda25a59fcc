<div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

    <!-- Header -->
    <div
        class="flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between p-6 sm:py-8 sm:px-10 border-b bg-card dark:bg-transparent">
        <div class="flex-1 min-w-0">
            <!-- Title -->
            <div class="mt-2">
                <h2 class="text-3xl md:text-4xl font-extrabold tracking-tight leading-7 sm:leading-10 truncate">
                    แก้ไขสินค้า
                </h2>
            </div>

        </div>
    </div>
    <!-- Main -->
    <div class="flex-auto p-3 sm:p-6">
        <!-- This example requires Tailwind CSS v2.0+ -->
        <div class="overflow-hidden bg-white shadow sm:rounded-lg">
            <div class="flex">
                <div class="w-full">
                    <div class="px-4 mt-4 sm:px-6">
                        <h2 class="text-2xl font-extrabold leading-6 text-gray-900">รายละเอียดสินค้า</h2>
                        <p class="text-lg mt-1 max-w-2xl text-gray-500">กรุณากรอกข้อมูลสินค้า</p>
                    </div>
                </div>
            </div>
            <form class="flex flex-col mt-3 p-8 pb-4 bg-card shadow overflow-hidden ng-valid" [formGroup]="formData">
                <div class="flex">
                    <div class="w-full">
                        <div class="border-t border-gray-200">
                            <dl>
                                <div class="bg-white px-4 mt-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-l font-extrabold text-gray-500  mt-4">SKU</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                                        <mat-form-field class="w-full">
                                            <input matInput [formControlName]="'sku'"
                                                placeholder="กรุณาระบุ SKU">
                                        </mat-form-field>
                                    </dd>
                                </div>
                                <div class="bg-white px-4 mt-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-l font-extrabold text-gray-500  mt-4">ชื่อสินค้า</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                                        <mat-form-field class="w-full">
                                            <input matInput [formControlName]="'name'"
                                                placeholder="กรุณาระบุชื่อสินค้า">
                                        </mat-form-field>
                                    </dd>
                                </div>
                                <div class="bg-white-50 px-4 mt-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-l font-extrabold text-gray-500  mt-4">แบรนด์</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                                        <mat-form-field class="w-full">
                                            <input matInput [formControlName]="'brand'"
                                                placeholder="กรุณาระบุแบรนด์สินค้า">
                                        </mat-form-field>
                                    </dd>
                                </div>
                                <div class="bg-white-50 px-4 mt-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-l font-extrabold text-gray-500  mt-4">ราคาต้นทุน</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                                        <mat-form-field class="w-full">
                                            <input matInput [formControlName]="'unit_cost'"
                                                placeholder="กรุณาระบุราคาต้นทุนสินค้า">
                                        </mat-form-field>
                                    </dd>
                                </div>
                                <div class="bg-white-50 px-4 mt-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-l font-extrabold text-gray-500  mt-4">ราคาขาย</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                                        <mat-form-field class="w-full">
                                            <input matInput [formControlName]="'unit_price'"
                                                placeholder="กรุณาระบุราคาขายสินค้า">
                                        </mat-form-field>
                                    </dd>
                                </div>
                                <div class="bg-white px-4 mt-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-l font-extrabold text-gray-500  mt-4">หมวดหมู่สินค้า</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                                        <mat-form-field class="w-full pr-2">
                                            <mat-select [formControlName]="'item_type_id'">
                                                <mat-option [value]="''">
                                                    กรุณาเลือกหมวดหมู่สินค้า
                                                </mat-option>
                                                <mat-option *ngFor="let item of itemtypeData" [value]="item.id">
                                                    {{item.name}}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                    </dd>
                                </div>
                                <div class="bg-white px-4 mt-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-l font-extrabold text-gray-500  mt-4">จุดเก็บหมู่สินค้า</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                                        <mat-form-field class="w-full pr-2">
                                            <mat-select [formControlName]="'location_id'">
                                                <mat-option [value]="''">
                                                    กรุณาเลือกจุดเก็บสินค้า
                                                </mat-option>
                                                <mat-option *ngFor="let loc of locationData" [value]="loc.id">
                                                    {{loc.name}}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                    </dd>
                                </div>
                                <div class="bg-white px-4 mt-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-l font-extrabold text-gray-500  mt-4">ซัพพลายเออร์</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                                        <mat-form-field class="w-full pr-2">
                                            <mat-select [formControlName]="'vendor_id'">
                                                <mat-option [value]="''">
                                                    กรุณาเลือกซัพพลายเออร์
                                                </mat-option>
                                                <mat-option *ngFor="let ven of vendorData" [value]="ven.id">
                                                    {{ven.name}}
                                                </mat-option>
                                            </mat-select>
                                        </mat-form-field>
                                    </dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                    <div class="w-full">
                        <div class="border-t border-gray-200">
                            <dl>
                                <div class="bg-white px-4 mt-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-l font-extrabold text-gray-500  mt-4">รูปสินค้า</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                                        <div class="flex justify-center items-center w-full">
                                            <div class="bg-indigo-300 ">
                                                <label>
                                                    <input (change)='onChangeSignature($event)' type="file" id="file">
                                                    <img class="object-fill h-80 w-80" [src]="this.url_sig"
                                                        style="cursor: pointer;" alt="">
                                                </label>
                                            </div>
                                        </div>
                                    </dd>
                                </div>
                                <div class="bg-white px-4 mt-4 sm:grid sm:grid-cols-3 sm:gap-4 sm:px-6">
                                    <dt class="text-l font-extrabold text-gray-500  mt-4">รายละเอียด</dt>
                                    <dd class="mt-1 text-sm text-gray-900 sm:col-span-2 sm:mt-0">
                                        <mat-form-field class="w-full">
                                            <textarea matInput rows="4" [formControlName]="'description'"
                                                placeholder="รายละเอียด"></textarea>
                                        </mat-form-field>
                                    </dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                </div>
            </form>
            <div class="flex items-center justify-end w-full border-t px-8 py-4">
                <div class="flex items-center justify-end">
                    <button mat-flat-button class="bg-gray-700 text-gray-700 hover:bg-gray-500 dark:bg-gray-600 dark:text-gray-200 dark:hover:bg-gray-500 rounded-lg py-2 px-4" [routerLink]="['/item/list']">
                        <mat-icon svgIcon="heroicons_solid:x-mark" class="mr-2"></mat-icon>
                        ยกเลิก
                    </button>
                    <button class="px-6 ml-3 mat-primary" mat-flat-button (click)="updateItem()">
                        <mat-icon svgIcon="heroicons_solid:check"></mat-icon>
                        ยืนยัน
                    </button>
                </div>
            </div>
        </div>
    </div>

</div>

<!-- check: {{ this.formData.value | json}} -->
