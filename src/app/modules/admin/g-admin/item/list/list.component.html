<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">รายการสินค้า</div>
        <!-- Actions -->
        <div class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4">

            <!-- Add product button -->


        </div>
    </div>

    <!-- Main -->
    <div class="flex flex-col flex-auto min-w-0 bg-gray-100 dark:bg-transparent">

        <div class="flex flex-col p-3 sm:p-6">
            <div class="overflow-hidden  bg-white shadow sm:rounded-lg">


                <div class="flex flex-col flex-auto p-5 sm:overflow-auto overflow-x-scroll">

                    <mat-tab-group [selectedIndex]="selected.value" (selectedIndexChange)="selected.setValue($event)">
                        <mat-tab *ngFor="let tab of tabs; let index = index">
                            <ng-template mat-tab-label>
                                <div class="text-md font-bold">{{tab}}</div>
                            </ng-template>
                            <div *ngIf="tab === 'สินค้าธรรมดา'" class="table-responsive">
                                <div class="flex flex-row justify-between py-2 px-5">
                                    <div class="flex justify-start items-center w-[405px] mt-5">
                                        <div class="flex text-md font-normal tracking-tight leading-7 sm:leading-10 truncate justify-center items-center">
                                            <mat-form-field [ngClass]="formFieldHelpers" class="min-w-[405px] min-h-[40px]">
                                                <mat-icon svgIcon="search"></mat-icon>
                                                <input matInput placeholder="ค้นหา" [(ngModel)]="searchQuery" (ngModelChange)="applySearch()">
                                            </mat-form-field>
                                        </div>
                                    </div>
                                    <div class="flex">
                                        <div *ngIf="!hiddenSave()" class="flex text-md font-normal tracking-tight leading-7 sm:leading-10 truncate justify-center items-center">
                                            <div  class="flex items-center mt-6 sm:mt-0"></div>
                                            <a routerLink="../new-item" class="ml-4" mat-flat-button [color]="'primary'" *ngIf="!hiddenSave()">
                                                <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                                                <span class="ml-2 mr-1">เพิ่มสินค้าใหม่</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <table datatable [dtOptions]="dtOptionsNormal" class="table w-full text-left text-gray-500 overflow-hidden">
                                    <thead class="bg-gray-300 text-black">
                                        <tr>
                                            <th class="text-center ">จัดการ</th>
                                            <th class="text-center">ลำดับ</th>
                                            <th class="text-center">รูปภาพ</th>
                                            <th class="text-center">รหัสสินค้า</th>
                                            <th class="text-center">SKU</th>
                                            <th class="text-center">ชื่อสินค้า</th>
                                            <th class="text-center">จำนวนทั้งหมด</th>
                                            <th class="text-center">สินค้าถูกจอง</th>
                                            <th class="text-center">พร้อมขาย</th>
                                            <th class="text-center">สถานะ</th>
                                            <th class="text-center">สร้างโดย</th>
                                            <th class="text-center ">วันที่สร้าง</th>
                                        </tr>
                                    </thead>
                                    <tbody *ngIf="items?.length != 0">
                                        <tr *ngFor="let item of items; let i = index"
                                            class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-md">
                                            <td style="width: 80px; min-width: 80px;">
                                                <button mat-icon-butto class="edit-button" (click)="edit(item)" title="แก้ไขข้อมูล"
                                                    [disabled]="hiddenEdit()">
                                                    <mat-icon class="">edit</mat-icon>
                                                </button>
                                                <button mat-icon-button class="delete-button" (click)="Delete(item.id)" title="ลบข้อมูล"
                                                    [disabled]="hiddenDelete()">
                                                    <mat-icon class="delete-icon">delete</mat-icon>
                                                </button>
                                            </td>
                                            <td class="text-center whitespace-nowrap">{{ pages.begin + (i + 1) }}</td>
                                            <td class="text-center whitespace-nowrap">
                                                <img src="{{item.image}}" class="h-14 w-14 rounded-full shadow-md" alt="">
                                            </td>
                                            <td class="text-center whitespace-nowrap">{{ item.item_id }}</td>
                                            <td class="text-center whitespace-nowrap">{{ item.sku }}</td>
                                            <td class="text-center whitespace-nowrap">{{ item.name }}</td>
                                            <td class="text-center whitespace-nowrap text-blue-500">{{ item.qty }}</td>
                                            <td class="text-center whitespace-nowrap text-red-500">{{ item.booking }}</td>
                                            <td class="text-center whitespace-nowrap text-green-500">{{ item.balance }}</td>
                                            <td class="text-center whitespace-nowrap">
                                                <span *ngIf="item.status === 1" class="bg-green-200 text-green-800 px-3 py-1 rounded-full text-xs font-medium">เปิดการใช้งาน</span>
                                                <span *ngIf="item.status === 0" class="bg-red-200 text-red-800 px-3 py-1 rounded-full text-xs font-medium">ปิดการใช้งาน</span>
                                            </td>
                                            <td class="text-center whitespace-nowrap">{{ item.user_create ?
                                                item.user_create.first_name : 'No data' }} {{
                                                item.user_create ?
                                                item.user_create.last_name: '' }}
                                            </td>
                                            <td class="text-center whitespace-nowrap">{{ item.created_at | date:
                                                'dd/MM/yyyy HH:mm' }}</td>
                                        </tr>
                                    </tbody>
                                    <tbody *ngIf="items?.length == 0">
                                        <tr>
                                            <td colspan="11" class="no-data-available text-center">ไม่มีข้อมูล !</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                            <div *ngIf="tab === 'สินค้าโปรโมชั่น'" class="table-responsive">
                                <div class="flex flex-row justify-between py-2 px-5">
                                    <div class="flex justify-start items-center w-[405px] mt-5">
                                        <div class="flex text-md font-normal tracking-tight leading-7 sm:leading-10 truncate justify-center items-center">
                                            <mat-form-field [ngClass]="formFieldHelpers" class="min-w-[405px] min-h-[40px]">
                                                <mat-icon svgIcon="search"></mat-icon>
                                                <input matInput placeholder="ค้นหา" [(ngModel)]="searchQuery" (ngModelChange)="applySearch()">
                                            </mat-form-field>
                                        </div>
                                    </div>
                                    <div class="flex">
                                        <div *ngIf="!hiddenSave()" class="flex text-md font-normal tracking-tight leading-7 sm:leading-10 truncate justify-center items-center">
                                            <div  class="flex items-center mt-6 sm:mt-0"></div>
                                            <a routerLink="../new-item-promotion" class="ml-4" mat-flat-button [color]="'primary'" *ngIf="!hiddenSave()">
                                                <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                                                <span class="ml-2 mr-1">เพิ่มสินค้าโปรโมชั่นใหม่</span>
                                            </a>
                                        </div>
                                    </div>
                                </div>
                                <table datatable [dtOptions]="dtOptionsPromotion"
                                    class="table row-border hover w-full text-lg text-left text-gray-500 dark:text-gray-400"
                                    style="width: 100%">
                                    <thead>
                                        <tr>
                                            <th class="text-center whitespace-nowrap py-4 ">จัดการ</th>
                                            <th class="text-center whitespace-nowrap py-4">ลำดับ</th>
                                            <th class="text-center whitespace-nowrap py-4">รูปภาพ</th>
                                            <th class="text-center whitespace-nowrap py-4">รหัสสินค้า</th>
                                            <th class="text-center whitespace-nowrap py-4">SKU</th>
                                            <th class="text-center whitespace-nowrap py-4">ชื่อสินค้า</th>
                                            <th class="text-center whitespace-nowrap py-4">จำนวนทั้งหมด</th>
                                            <th class="text-center whitespace-nowrap py-4">สินค้าถูกจอง</th>
                                            <th class="text-center whitespace-nowrap py-4">พร้อมขาย</th>
                                            <th class="text-center whitespace-nowrap py-4">สถานะ</th>
                                            <th class="text-center whitespace-nowrap py-4">สร้างโดย</th>
                                            <th class="text-center whitespace-nowrap py-4 ">วันที่สร้าง</th>
                                        </tr>
                                    </thead>
                                    <tbody *ngIf="itemsPromotion?.length != 0">
                                        <tr *ngFor="let item of itemsPromotion; let i = index"
                                        class="bg-white border-b dark:bg-gray-800 dark:border-gray-700 text-md">
                                        <td style="width: 80px; min-width: 80px;">
                                            <button mat-icon-butto class="edit-button" (click)="edit(item)" title="แก้ไขข้อมูล"
                                                [disabled]="hiddenEdit()">
                                                <mat-icon class="">edit</mat-icon>
                                            </button>
                                            <button mat-icon-button class="delete-button" (click)="Delete(item.id)" title="ลบข้อมูล"
                                                [disabled]="hiddenDelete()">
                                                <mat-icon class="delete-icon">delete</mat-icon>
                                            </button>
                                        </td>
                                        <td class="text-center whitespace-nowrap">{{ pages.begin + (i + 1) }}</td>
                                        <td class="text-center whitespace-nowrap">
                                            <img src="{{item.image}}" class="h-14 w-14 rounded-full shadow-md" alt="">
                                        </td>
                                        <td class="text-center whitespace-nowrap">{{ item.item_id }}</td>
                                        <td class="text-center whitespace-nowrap">{{ item.sku }}</td>
                                        <td class="text-center whitespace-nowrap">{{ item.name }}</td>
                                        <td class="text-center whitespace-nowrap text-blue-500">{{ item.qty }}</td>
                                        <td class="text-center whitespace-nowrap text-red-500">{{ item.booking }}</td>
                                        <td class="text-center whitespace-nowrap text-green-500">{{ item.balance }}</td>
                                        <td class="text-center whitespace-nowrap">
                                            <span *ngIf="item.status === 1" class="bg-green-200 text-green-800 px-3 py-1 rounded-full text-xs font-medium">เปิดการใช้งาน</span>
                                            <span *ngIf="item.status === 0" class="bg-red-200 text-red-800 px-3 py-1 rounded-full text-xs font-medium">ปิดการใช้งาน</span>
                                        </td>
                                        <td class="text-center whitespace-nowrap">{{ item.user_create ?
                                            item.user_create.first_name : 'No data' }} {{
                                            item.user_create ?
                                            item.user_create.last_name: '' }}
                                        </td>
                                        <td class="text-center whitespace-nowrap">{{ item.created_at | date:
                                            'dd/MM/yyyy HH:mm' }}</td>
                                    </tr>
                                    </tbody>
                                    <tbody *ngIf="itemsPromotion?.length == 0">
                                        <tr>
                                            <td colspan="11" class="no-data-available text-center">ไม่มีข้อมูล !</td>
                                        </tr>
                                    </tbody>
                                </table>
                            </div>
                        </mat-tab>
                    </mat-tab-group>

                </div>
            </div>
        </div>
    </div>


</div>
