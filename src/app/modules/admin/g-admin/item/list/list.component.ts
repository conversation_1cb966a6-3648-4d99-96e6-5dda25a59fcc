import {
    ChangeDetector<PERSON><PERSON>,
    Component,
    <PERSON><PERSON><PERSON><PERSON>,
    OnInit,
    ViewChild,
} from '@angular/core';
import { FormBuilder, FormControl, FormsModule } from '@angular/forms';
import { PageEvent } from '@angular/material/paginator';
import { lastValueFrom, Subject, takeUntil } from 'rxjs';
import { fuseAnimations } from '@fuse/animations';
import { FuseConfirmationService } from '@fuse/services/confirmation';
import { MatDialog } from '@angular/material/dialog';
import { ActivatedRoute, Router, RouterLink } from '@angular/router';
import { environment } from 'environments/environment';
import { AuthService } from 'app/core/auth/auth.service';
import { Item } from '../item.types';
import { ItemService } from '../item.service';
import { DataTableDirective, DataTablesModule } from 'angular-datatables';
import { NgIf, NgFor, NgClass, DatePipe } from '@angular/common';
import { MatProgressBar } from '@angular/material/progress-bar';
import { <PERSON><PERSON>ab<PERSON>roup, MatTab, MatTabLabel } from '@angular/material/tabs';
import { MatFormField } from '@angular/material/form-field';
import { MatIcon } from '@angular/material/icon';
import { MatInput } from '@angular/material/input';
import { MatAnchor, MatIconButton } from '@angular/material/button';

@Component({
    selector: 'item-list',
    templateUrl: './list.component.html',
    styleUrls: ['./list.component.scss'],
    // encapsulation: ViewEncapsulation.None,
    // changeDetection: ChangeDetectionStrategy.OnPush,
    animations: fuseAnimations,
    imports: [NgIf, MatProgressBar, MatTabGroup, NgFor, MatTab, MatTabLabel, MatFormField, NgClass, MatIcon, MatInput, FormsModule, MatAnchor, RouterLink, DataTablesModule, MatIconButton, DatePipe]
})
export class ItemListComponent implements OnInit, OnDestroy {
    @ViewChild(DataTableDirective)
    dtElement!: DataTableDirective;
    tabs = ['สินค้าธรรมดา', 'สินค้าโปรโมชั่น'];
    selected = new FormControl(0);
    isLoading: boolean = false;
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    env_path = environment.API_URL;
    dtOptions: DataTables.Settings = {};
    dtOptionsNormal: DataTables.Settings = {};
    dtOptionsPromotion: DataTables.Settings = {};
    // items: Item[] = [];
    // itemsPromotion: Item[] = [];
    private destroy$ = new Subject<any>();
    items: any = [];
    itemsPromotion: any = [];
    // flashMessage: 'success' | 'error' | null = null;
    // searchInputControl: FormControl = new FormControl();
    // selectedProduct: any | null = null;
    // filterForm: FormGroup;
    // tagsEditMode: boolean = false;

    // me: any | null;

    // supplierId: string | null;
    // pagination: CustomerPagination;

    /**
     * Constructor
     */
    constructor(
        private _changeDetectorRef: ChangeDetectorRef,
        private _Service: ItemService,
        private _router: Router,
        private _fuseConfirmationService: FuseConfirmationService
    ) {}

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */    searchQuery : string = ''
    formFieldHelpers: string[] = ['fuse-mat-dense'];







    applySearch(){
        this.rerender()
    }





    ngOnInit() {
        this.loadTableNormal();
        this.LoadTablePromotion();
    }
    pages = { current_page: 1, last_page: 1, per_page: 10, begin: 0 };
    loadTableNormal(): void {
        const that = this;
        this.dtOptionsNormal = {
            // scrollX: true,
            pagingType: 'full_numbers',
            pageLength: 10,
            serverSide: true,
            processing: true,
            searching: false,
            order: [[3, 'asc']],
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.11.3/i18n/th.json',
            },
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.search = { value: this.searchQuery };
                dataTablesParameters.item_type_id = null;
                dataTablesParameters.set_type = 'normal';
                that._Service
                    .getItem(dataTablesParameters)
                    .subscribe((resp) => {
                        this.items = resp.data;
                        this.pages.current_page = resp.current_page;
                        this.pages.last_page = resp.last_page;
                        this.pages.per_page = resp.per_page;
                        if (resp.current_page > 1) {
                            this.pages.begin =
                                resp.per_page * resp.current_page - 1;
                        } else {
                            this.pages.begin = 0;
                        }
                        callback({
                            recordsTotal: resp.total,
                            recordsFiltered: resp.total,
                            data: [],
                        });
                        this._changeDetectorRef.markForCheck();
                    });
            },
            columns: [
                { data: 'actice', orderable: false },
                { data: 'no' },
                { data: 'image' },
                { data: 'item_id' },
                { data: 'sku' },
                { data: 'name' },
                { data: 'qty' },
                { data: 'booking' },
                { data: 'balance' },
                { data: 'status' },
                { data: 'create_by' },
                { data: 'created_at' },
            ],
        };
    }
    hiddenEdit() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 3);
        return menu.edit == 0;
    }
    hiddenDelete() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 3);
        return menu.delete == 0;
    }
    hiddenSave() {
        const getpermission = JSON.parse(localStorage.getItem('permission'));
        const menu = getpermission.find((e) => e.menu_id == 3);
        return menu.save == 0;
    }
    LoadTablePromotion(): void {
        const that = this;
        this.dtOptionsPromotion = {
            pagingType: 'full_numbers',
            pageLength: 10,
            serverSide: true,
            processing: true,
            searching: false,
            order: [[3, 'desc']],
            language: {
                url: 'https://cdn.datatables.net/plug-ins/1.11.3/i18n/th.json',
            },
            ajax: (dataTablesParameters: any, callback) => {
                dataTablesParameters.item_type_id = null;
                dataTablesParameters.set_type = 'set_products';
                dataTablesParameters.search = { value: this.searchQuery };
                that._Service
                    .getItem(dataTablesParameters)
                    .subscribe((resp) => {
                        this.itemsPromotion = resp.data;
                        this.pages.current_page = resp.current_page;
                        this.pages.last_page = resp.last_page;
                        this.pages.per_page = resp.per_page;
                        if (resp.current_page > 1) {
                            this.pages.begin =
                                resp.per_page * resp.current_page - 1;
                        } else {
                            this.pages.begin = 0;
                        }
                        callback({
                            recordsTotal: resp.total,
                            recordsFiltered: resp.total,
                            data: [],
                        });
                        this._changeDetectorRef.markForCheck();
                    });
            },
            columns: [
                { data: 'actice', orderable: false },
                { data: 'id' },
                { data: 'image' },
                { data: 'item_id' },
                { data: 'name' },
                { data: 'qty' },
                { data: 'booking' },
                { data: 'balance' },
                { data: 'status' },
                { data: 'create_by' },
                { data: 'created_at' },
            ],
        };
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    addTab(selectAfterAdding: boolean) {
        this.tabs.push('New');

        if (selectAfterAdding) {
            this.selected.setValue(this.tabs.length - 1);
        }
    }

    removeTab(index: number) {
        this.tabs.splice(index, 1);
    }

    // resetForm(): void {
    //     this.filterForm.reset();
    //     this.filterForm.get('asset_type').setValue("default");
    //     this._changeDetectorRef.markForCheck();
    // }

    /**
     * Close the details
     */
    // closeDetails(): void {
    //     this.selectedProduct = null;
    // }

    /**
     * Show flash message
     */
    // showFlashMessage(type: 'success' | 'error'): void {
    //     // Show the message
    //     this.flashMessage = type;

    //     // Mark for check
    //     this._changeDetectorRef.markForCheck();

    //     // Hide it after 3 seconds
    //     setTimeout(() => {

    //         this.flashMessage = null;

    //         // Mark for check
    //         this._changeDetectorRef.markForCheck();
    //     }, 3000);
    // }

    callDetail(productId: string): void {
        // alert(this.selectedProduct.id);
        // // If the product is already selected...
        // if (this.selectedProduct && this.selectedProduct.id === productId) {
        //     // Close the details
        //     // this.closeDetails();
        //     return;
        // }

        this._router.navigate(['marketing/brief-plan/' + productId]);
    }

    edit(data: any): void {
        if (data.set_type === 'normal') {
            this._router.navigate(['/item/edit/' + data.id]);
        } else {
            this._router.navigate(['/item/edit-item-promotion/' + data.id]);
        }
        //if (data.main_item_line.length === 0) {
        //    this._router.navigate(['/item/edit/' + data.id]);
        //} else {
        //    this._router.navigate(['/item/edit-item-promotion/' + data.id]);
        //}
    }

    openNewBrief(): void {
        this._router.navigateByUrl('marketing/brief-plan/brief/create');
    }

    openNewOrder(productId: string): void {
        // If the product is already selected...
        // if (this.selectedProduct && this.selectedProduct.id === productId) {
        //     // Close the details
        //     // this.closeDetails();
        //     return;
        // }

        this._router.navigate([
            'marketing/data/assets-list/new-order/' + productId,
        ]);
    }
    Delete(id) {
        const confirmation = this._fuseConfirmationService.open({
            title: 'ยืนยันลบรายการสินค้า',
            message: 'คุณต้องการลบรายการสินค้าใช่หรือไม่ ?',
            icon: {
                show: true,
                name: 'heroicons_outline:exclamation-triangle',
                color: 'warning',
            },
            actions: {
                confirm: {
                    show: true,
                    label: 'ยืนยัน',
                    color: 'primary',
                },
                cancel: {
                    show: true,
                    label: 'ยกเลิก',
                },
            },
            dismissible: true,
        });

        // Subscribe to the confirmation dialog closed action
        confirmation.afterClosed().subscribe((result) => {
            // If the confirm button pressed...
            if (result === 'confirmed') {
                this._Service
                    .delete(id)
                    .pipe(takeUntil(this.destroy$))
                    .subscribe((res: any) => {
                        if (res.code == 201) {
                            this._fuseConfirmationService
                                .open({
                                    title: 'ลบรายการสินค้า',
                                    message: 'บันทึกเรียบร้อย',
                                    icon: {
                                        show: true,
                                        name: 'heroicons_outline:check-circle',
                                        color: 'success',
                                    },
                                    actions: {
                                        confirm: {
                                            show: false,
                                            label: 'ตกลง',
                                            color: 'primary',
                                        },
                                        cancel: {
                                            show: false,
                                            label: 'ยกเลิก',
                                        },
                                    },
                                    dismissible: true,
                                })
                                .afterClosed()
                                .subscribe((res) => {
                                    this.rerender();
                                });
                        }
                    });
            }
        });
    }
    // openDialog() {
    //     const dialogRef = this._matDialog.open(NewBranchComponent);
    // }

    // openImportOsm(): void {
    //     this._matDialog.open(ImportOSMComponent)
    // }
    rerender(): void {
        this.dtElement.dtInstance.then((dtInstance: DataTables.Api) => {
            dtInstance.ajax.reload();
        });
    }
}
