<div class="sm:absolute sm:inset-0 flex flex-col flex-auto min-w-0 sm:overflow-auto bg-card dark:bg-transparent">

    <!-- Header -->
    <div
        class="relative flex flex-col sm:flex-row flex-0 sm:items-center sm:justify-between py-8 px-6 md:px-8 border-b">
        <!-- Loader -->
        <div class="absolute inset-x-0 bottom-0" *ngIf="isLoading">
            <mat-progress-bar [mode]="'indeterminate'"></mat-progress-bar>
        </div>
        <!-- Title -->
        <div class="text-4xl font-extrabold tracking-tight">รายการสินค้าโปรโมชั่น</div>
        <!-- Actions -->
        <div *ngIf="roleType == 'marketing'" class="flex shrink-0 items-center mt-6 sm:mt-0 sm:ml-4">

            <!-- Add product button -->
            <a href="item/new-item-promotion" class="ml-4" mat-flat-button [color]="'primary'">
                <mat-icon [svgIcon]="'heroicons_outline:plus'"></mat-icon>
                <span class="ml-2 mr-1">เพิ่มสินค้าโปรโมชั่นใหม่</span>
            </a>

        </div>
    </div>

    <!-- search -->
    <div class="search-box relative flex flex-col flex-0 px-6 md:px-8 border-b mt-4">
        <!-- <form [formGroup]="filterForm">
            <div class="flex flex-auto">
                <div class="flex flex-col w-1/6">
                    <mat-form-field class="search-box w-full xs:ml-0">
                        <mat-select [formControlName]="'asset_type'">
                            <mat-option value="default">Asset Type</mat-option>
                            <mat-option *ngFor="let asset_type of asset_types ; let i = index;" [value]="asset_type.id">
                                {{asset_type.name}}
                            </mat-option>
                        </mat-select>
                    </mat-form-field>
                </div>
                <div class="flex flex-col w-5/6">
                    <mat-form-field class="w-full">
                        <input matInput [formControlName]="'searchInputControl'" [autocomplete]="'off'"
                            [placeholder]="'Search for Code , Name'">
                        <mat-icon class="icon-size-5" matPrefix [svgIcon]="'heroicons_solid:magnifying-glass'"></mat-icon>
                    </mat-form-field>
                </div>
                <button mat-stroked-button class="w-fit" style="min-height: 48px;" (click)="resetForm()">
                    <mat-icon class="icon-size-5" [svgIcon]="'heroicons_solid:refresh'"></mat-icon>
                </button>
            </div>
        </form> -->
    </div>

    <!-- Main -->
    <div class="flex flex-auto overflow-hidden">

        <!-- Products list -->

        <div class="flex flex-col flex-auto p-5 overflow-hidden sm:overflow-y-auto">
            <table datatable [dtOptions]="dtOptions"
                class="table row-border hover w-full text-lg text-left text-gray-500 dark:text-gray-400"
                style="width: 100%">
                <thead>
                    <tr>
                        <th>ลำดับ</th>
                        <th>รูป</th>
                        <th>รหัสสินค้า</th>
                        <th>ชื่อ</th>
                        <th>จำนวนทั้งหมด</th>
                        <th>สินค้าถูกจอง</th>
                        <th>พร้อมขาย</th>
                        <th>สถานะ</th>
                        <th>สร้างโดย</th>
                        <th>วันที่สร้าง</th>
                        <th>จัดการ</th>
                    </tr>
                </thead>
                <tbody *ngIf="items?.length != 0">
                    <tr *ngFor="let item of items; let i = index">
                        <td style="width: 5%;">{{ pages.begin + (i + 1) }}</td>
                        <td *ngIf="item.image === null || ''"> </td>
                        <td *ngIf="item.image !== null || ''"><img src="{{item.image}}"
                                style="width: 30px!important;height: 30px!important"> </td>
                        <td style="width: 10%;">{{ item.item_id }}</td>
                        <td style="width: 20%;">{{ item.name }}</td>
                        <td style="width: 9%;">{{ item.qty }}</td>
                        <td style="width: 9%;">{{ item.booking }}</td>
                        <td style="width: 9%;">{{ item.balance }}</td>
                        <td>
                            <div *ngIf="item.status === 1">
                                เปิดการใช้งาน
                            </div>
                            <div *ngIf="item.status === 0">
                                ปิดการใช้งาน
                            </div>
                        </td>
                        <td>{{ item.user_create ? item.user_create.first_name : 'No data' }} {{ item.user_create ?
                            item.user_create.last_name: '' }}
                        </td>
                        <td>{{ item.created_at | thaiDate | thaiDate }}</td>
                        <td>
                            <button mat-button (click)="edit(item.id)">
                                <mat-icon>edit</mat-icon>
                            </button>
                        </td>
                    </tr>
                </tbody>
                <tbody *ngIf="items?.length == 0">
                    <tr>
                        <td colspan="11" class="no-data-available text-center">ไม่มีข้อมูล !</td>
                    </tr>
                </tbody>
            </table>
        </div>

    </div>

</div>
