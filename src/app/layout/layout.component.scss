layout {
    display: flex;
    flex: 1 1 auto;
    width: 100%;
    max-width: 100%;
    min-width: 0;
    /* Base styles for individual layouts */
    >* {
        position: relative;
        display: flex;
        flex: 1 1 auto;
        width: 100%;
    }
    /* Base styles for components that load as a route */
    router-outlet {
        +* {
            position: relative;
            display: flex;
            flex: 1 1 auto;
            width: 100%;
        }
    }
}

.fuse-vertical-navigation-item-active {
    background-color: var(--gs-selection-color) !important;
    color: var(--gs-selection-text-color) !important;
    border-radius: 2px;
}

.dark fuse-vertical-navigation-group-item>.fuse-vertical-navigation-item-wrapper .fuse-vertical-navigation-item .fuse-vertical-navigation-item-title-wrapper .fuse-vertical-navigation-item-title {
    color: #d4af51!important;
}
