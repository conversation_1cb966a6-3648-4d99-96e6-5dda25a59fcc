import { Compo<PERSON>, <PERSON><PERSON><PERSON><PERSON>, OnInit, ViewEncapsulation } from '@angular/core';
import { ActivatedRoute, Router, RouterOutlet } from '@angular/router';
import { forkJoin, lastValueFrom, Observable, of, Subject, switchMap, takeUntil } from 'rxjs';
import { FuseMediaWatcherService } from '@fuse/services/media-watcher';
import {
    FuseNavigationService,
    FuseVerticalNavigationComponent,
} from '@fuse/components/navigation';
import { Navigation } from 'app/core/navigation/navigation.types';
import { NavigationService } from 'app/core/navigation/navigation.service';
import { AuthService } from 'app/core/auth/auth.service';
import { FuseLoadingBarComponent } from '../../../../../@fuse/components/loading-bar/loading-bar.component';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { FuseFullscreenComponent } from '../../../../../@fuse/components/fullscreen/fullscreen.component';
import { UserComponent } from '../../../common/user/user.component';
import { CommonModule, NgIf } from '@angular/common';
import { LanguagesComponent } from 'app/layout/common/languages/languages.component';
import { MessagesComponent } from 'app/layout/common/messages/messages.component';
import { NotificationsComponent } from 'app/layout/common/notifications/notifications.component';
import { QuickChatComponent } from 'app/layout/common/quick-chat/quick-chat.component';
import { SearchComponent } from 'app/layout/common/search/search.component';
import { ShortcutsComponent } from 'app/layout/common/shortcuts/shortcuts.component';
import { MatSelectModule } from '@angular/material/select';
import { FormsModule, ReactiveFormsModule, UntypedFormControl } from '@angular/forms';
import { BranchService } from 'app/modules/admin/g-admin/branch/branch.service';
import { UserService } from 'app/core/user/user.service';
import { environment } from 'environments/environment';
import { HttpClient } from '@angular/common/http';

@Component({
    selector: 'classic-layout',
    templateUrl: './classic.component.html',
    encapsulation: ViewEncapsulation.None,
    standalone: true,
    imports: [
        NgIf,
        FuseLoadingBarComponent,
        FuseVerticalNavigationComponent,
        MatButtonModule,
        MatIconModule,
        LanguagesComponent,
        FuseFullscreenComponent,
        SearchComponent,
        ShortcutsComponent,
        MessagesComponent,
        NotificationsComponent,
        UserComponent,
        RouterOutlet,
        QuickChatComponent,
        MatSelectModule,
        FormsModule,
        CommonModule,
        ReactiveFormsModule
    ],
})
export class ClassicLayoutComponent implements OnInit, OnDestroy {
    isScreenSmall: boolean;
    navigation: Navigation;
    Datanavigation: any;
    private _unsubscribeAll: Subject<any> = new Subject<any>();
    branchs = [];
    branchControl = new UntypedFormControl();
    user: any
    private _jwt: string = null;
    // Sidebar logo that changes by selected branch (initialized from localStorage to avoid flash)
    currentLogo: string = (localStorage.getItem('activeBranchLogo') || 'Tconfig/logo/logo.png');
    /**
     * Constructor
     */
    private _authenticated: boolean = false;
    constructor(

        private _activatedRoute: ActivatedRoute,
        private _router: Router,
        private _navigationService: NavigationService,
        private _fuseMediaWatcherService: FuseMediaWatcherService,
        private _fuseNavigationService: FuseNavigationService,
        private _branchService: BranchService,
        private _userService: UserService,
        private _httpClient: HttpClient,
    ) { }

    // -----------------------------------------------------------------------------------------------------
    // @ Accessors
    // -----------------------------------------------------------------------------------------------------

    /**
     * Getter for current year
     */
    get currentYear(): number {
        return new Date().getFullYear();
    }

    get isBanrai(): boolean {
        return (this.currentLogo || '').includes('banraiLogo');
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Lifecycle hooks
    // -----------------------------------------------------------------------------------------------------

    /**
     * On init
     */
    ngOnInit(): void {
        this._branchService.getBranch().subscribe((resp: any) => {
            this.user = JSON.parse(localStorage.getItem('user'))
            this.branchs = resp.data
            this.branchControl.setValue(+this.user.branch_id)
            // Set logo on initial load based on current branch
            this.updateLogoByBranchId(this.user?.branch_id)
        })
        this._navigationService.navigation$
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe((navigation: Navigation) => {
                this.Datanavigation = navigation.default;
                const user = JSON.parse(localStorage.getItem('user')) || null;
                if (user.position?.name == 'นักพัฒนาระบบ') {
                    AuthService._Manager = false;
                    AuthService._Ads = false;
                    AuthService._Telesale = false;
                    AuthService._Admin = false;
                    AuthService._Packing = false;
                    AuthService._Hr = false;
                    AuthService._Report = false;
                    AuthService._Profile = false;
                    this.navigation = navigation;
                } else if (user.position?.name == 'ทีมยิงแอดโฆษณา') {
                    AuthService._Ads = false;
                    AuthService._Profile = false;
                    this.navigation = navigation;
                } else if (user.position?.name == 'ทีมแอดมินตอบแชท') {
                    AuthService._Admin = false;
                    AuthService._Profile = false;
                    this.navigation = navigation;
                } else if (user.position?.name == 'ทีมเทเลเซล') {
                    AuthService._Telesale = false;
                    AuthService._Profile = false;
                    this.navigation = navigation;
                } else if (user.position?.name == 'ทีมเทเลเซลล์') {
                    AuthService._Telesale = false;
                    AuthService._Profile = false;
                    this.navigation = navigation;
                } else if (user.position?.name == 'เทเลเซล') {
                    AuthService._Telesale = false;
                    AuthService._Profile = false;
                    this.navigation = navigation;
                } else if (user.position?.name == 'สังกัดเทเลเซล') {
                    AuthService._Packing = false;
                    AuthService._Profile = false;
                    this.navigation = navigation;
                } else if (user.position?.name == 'หน่วยเทเลเซล') {
                    AuthService._Packing = false;
                    AuthService._Profile = false;
                    this.navigation = navigation;
                } else if (user.position?.name == 'หัวหน้าเทเลเซล') {
                    AuthService._Packing = false;
                    AuthService._Profile = false;
                    this.navigation = navigation;
                } else if (user.position?.name == 'ทีมแพ็คของ') {
                    AuthService._Packing = false;
                    AuthService._Profile = false;
                    this.navigation = navigation;
                } else if (user.position?.name == 'ทีมแพ็คของ') {
                    AuthService._Packing = false;
                    AuthService._Profile = false;
                    this.navigation = navigation;
                } else if (user.position?.name == 'ทีมฝ่ายบุคคล') {
                    AuthService._Hr = false;
                    AuthService._Profile = false;
                    this.navigation = navigation;
                } else if (user.position?.name == 'หัวหน้างาน') {
                    AuthService._Manager = false;
                    AuthService._Ads = true;
                    AuthService._Telesale = true;
                    AuthService._Admin = true;
                    AuthService._Packing = true;
                    AuthService._Hr = false;
                    AuthService._Report = false;
                    AuthService._Profile = false;
                    this.navigation = navigation;
                }
                this.navigation = navigation;
            });
        // Subscribe to media changes
        this._fuseMediaWatcherService.onMediaChange$
            .pipe(takeUntil(this._unsubscribeAll))
            .subscribe(({ matchingAliases }) => {
                // Check if the screen is small
                this.isScreenSmall = !matchingAliases.includes('md');
            });
    }

    /**
     * On destroy
     */
    ngOnDestroy(): void {
        // Unsubscribe from all subscriptions
        this._unsubscribeAll.next(null);
        this._unsubscribeAll.complete();
    }

    // -----------------------------------------------------------------------------------------------------
    // @ Public methods
    // -----------------------------------------------------------------------------------------------------

    /**
     * Toggle navigation
     *
     * @param name
     */
    toggleNavigation(name: string): void {
        // Get the navigation
        const navigation =
            this._fuseNavigationService.getComponent<FuseVerticalNavigationComponent>(
                name
            );

        if (navigation) {
            // Toggle the opened status
            navigation.toggle();
        }
    }

    onSelectBranch(event: any) {
        // Optimistically update the logo before backend reloads the app
        this.updateLogoByBranchId(event.value);
        this._branchService.loginBranch({ branch_id: event.value }).subscribe(async (resp: any) => {
            if (resp.code === 200) {
            }
        })
    }

    private updateLogoByBranchId(branchId: number | string): void {
        const selectedBranch = this.branchs?.find((branch: any) => +branch.id === +branchId);
        const branchName: string = selectedBranch?.name ?? '';
        if (branchName.trim() === 'บ้านไร่') {
            this.currentLogo = 'Tconfig/logo/banraiLogo.png';
            localStorage.setItem('activeBranchLogo', this.currentLogo);
        } else {
            this.currentLogo = 'Tconfig/logo/logo.png';
            localStorage.setItem('activeBranchLogo', this.currentLogo);
        }
    }

}
