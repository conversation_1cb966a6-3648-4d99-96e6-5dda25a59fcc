import { NgModule } from '@angular/core';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';







import { SearchModule } from 'app/layout/common/search/search.module';



import { CompactLayoutComponent } from 'app/layout/layouts/vertical/compact/compact.component';

@NgModule({
    exports: [
        CompactLayoutComponent
    ], imports: [RouterModule,
    MatButtonModule,
    MatDividerModule,
    MatIconModule,
    MatMenuModule,
    SearchModule,
    CompactLayoutComponent], providers: [provideHttpClient(withInterceptorsFromDi())] })
export class CompactLayoutModule
{
}
