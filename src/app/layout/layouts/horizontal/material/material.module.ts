import { NgModule } from '@angular/core';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';






import { SearchModule } from 'app/layout/common/search/search.module';



import { MaterialLayoutComponent } from 'app/layout/layouts/horizontal/material/material.component';

@NgModule({
    exports: [
        MaterialLayoutComponent
    ], imports: [RouterModule,
    MatButtonModule,
    MatDividerModule,
    MatIconModule,
    MatMenuModule,
    SearchModule,
    MaterialLayoutComponent], providers: [provideHttpClient(withInterceptorsFromDi())] })
export class MaterialLayoutModule
{
}
