import { NgModule } from '@angular/core';
import { provideHttpClient, withInterceptorsFromDi } from '@angular/common/http';
import { RouterModule } from '@angular/router';
import { MatButtonModule } from '@angular/material/button';
import { MatDividerModule } from '@angular/material/divider';
import { MatIconModule } from '@angular/material/icon';
import { MatMenuModule } from '@angular/material/menu';







import { SearchModule } from 'app/layout/common/search/search.module';



import { EnterpriseLayoutComponent } from 'app/layout/layouts/horizontal/enterprise/enterprise.component';

@NgModule({
    exports: [
        EnterpriseLayoutComponent
    ], imports: [RouterModule,
    MatButtonModule,
    MatDividerModule,
    MatIconModule,
    MatMenuModule,
    SearchModule,
    EnterpriseLayoutComponent], providers: [provideHttpClient(withInterceptorsFromDi())] })
export class EnterpriseLayoutModule
{
}
