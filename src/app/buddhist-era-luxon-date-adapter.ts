import { Injectable, Optional, Inject } from '@angular/core';
import { LuxonDateAdapter } from '@angular/material-luxon-adapter';
import { MAT_DATE_LOCALE, MatDateFormats } from '@angular/material/core';
import { DateTime } from 'luxon';

export const MAT_LUXON_DATE_FORMATS: MatDateFormats = {
  parse: {
    dateInput: 'dd/MM/yyyy', // How the user types the date (e.g., 25/05/2568)
  },
  display: {
    dateInput: 'dd/MM/yyyy', // How the date is displayed in the input field
    monthYearLabel: 'MMMM yyyy', // Label for month/year in the calendar header (e.g., May 2568)
    dateA11yLabel: 'dd/MM/yyyy', // Accessibility label for date
    monthYearA11yLabel: 'MMMM yyyy', // Accessibility label for month/year
  },
};

@Injectable()
export class BuddhistEraLuxonDateAdapter extends LuxonDateAdapter {

  override format(date: DateTime, displayFormat: any): string {
    const buddhistDate = date.reconfigure({ outputCalendar: 'buddhist' });
    return buddhistDate.toFormat(displayFormat);
  }

  override getYearName(date: DateTime): string {
    return String(date.year + 543);
  }
}