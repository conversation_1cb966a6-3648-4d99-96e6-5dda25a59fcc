/* eslint-disable */
import { FuseNavigationItem } from '@fuse/components/navigation';
import { AuthService } from 'app/core/auth/auth.service';
const storedPermission = JSON.parse(localStorage.getItem('permission'));

export const defaultNavigation: FuseNavigationItem[] = [
    {
        title: 'ผู้จัดการ',
        subtitle: 'เมนูการใช้งานผู้จัดการ',
        type: 'group',
        icon: 'heroicons_outline:user-group',
        hidden: function () {
            return this.children.every((child) => child.hidden());
        },
        children: [
            {
                id: 'g-Admin',
                title: 'จัดการระบบ',
                hidden: () => {
                    // const storedPermission = JSON.parse(localStorage.getItem('permission'));
                    const menu = storedPermission?.find((e) => e.menu_id == 1);
                    if (menu?.view == 0) {
                        return true;
                    } else {
                        return false;
                    }
                },
                type: 'collapsable',
                icon: 'heroicons_outline:home',
                children: [
                    {
                        id: 'general',
                        title: 'ทั่วไป',
                        type: 'basic',
                        link: '/general',
                    },
                    {
                        id: 'company',
                        title: 'บริษัท',
                        type: 'basic',
                        link: '/company/list',
                    },
                    {
                        id: 'payroll-contribution-setting',
                        title: 'ตั้งค่าเงินประกันสังคมและกองทุน',
                        type: 'basic',
                        link: '/payroll-contribution-setting/list',
                    },
                    {
                        id: 'location',
                        title: 'สถานที่',
                        // subtitle: 'Admin',
                        type: 'basic',
                        link: '/location/list',
                    },
                    // {
                    //     id: 'bank',
                    //     title: 'ธนาคาร',
                    //     // subtitle: 'Admin',
                    //     type: 'basic',
                    //     link: '/bank/list',
                    // },
                    {
                        id: 'permission',
                        title: 'จัดการสิทธิ์การใช้งาน',
                        type: 'basic',
                        link: '/permission/list',
                    },
                    {
                        id: 'bonus',
                        title: 'เบี้ยขยัน',
                        type: 'basic',
                        link: '/bonus-step',
                    },
                ],
            },
            {
                id: 'config',
                title: 'จัดการ Config',
                hidden: () => {
                    // const storedPermission = JSON.parse(localStorage.getItem('permission'));
                    const menu = storedPermission?.find((e) => e.menu_id == 7);
                    if (menu?.view == 0) {
                        return true;
                    } else {
                        return false;
                    }
                },
                type: 'basic',
                icon: 'heroicons_outline:cog',
                link: '/config',
            },


        ],
    },

    {
        title: 'HR',
        subtitle: 'เมนูการใช้งานHR',
        type: 'group',
        icon: 'heroicons_outline:home',
        hidden: function () {
            return this.children.every((child) => child.hidden());
        },
        children: [
            {
                id: 'position',
                title: 'ตำแหน่งพนักงาน',
                hidden: () => {
                    // const storedPermission = JSON.parse(localStorage.getItem('permission'));
                    const menu = storedPermission?.find((e) => e.menu_id == 24);
                    if (menu?.view == 0) {
                        return true;
                    } else {
                        return false;
                    }
                },
                type: 'basic',
                icon: 'mat_outline:hotel_class',
                link: '/position/list',
            },
            {
                id: 'user',
                title: 'พนักงาน',
                hidden: () => {
                    // const storedPermission = JSON.parse(localStorage.getItem('permission'));
                    const menu = storedPermission?.find((e) => e.menu_id == 25);
                    if (menu?.view == 0) {
                        return true;
                    } else {
                        return false;
                    }
                },
                type: 'basic',
                icon: 'heroicons_outline:user',
                link: '/user/list',
            },
            {
                id: 'employee-deposit',
                title: 'รายการเงินฝากพนักงาน',
                // hidden: () => {
                //     // const storedPermission = JSON.parse(localStorage.getItem('permission'));
                //     const menu = storedPermission?.find((e) => e.menu_id == 25);
                //     if (menu?.view == 0) {
                //         return true;
                //     } else {
                //         return false;
                //     }
                // },
                type: 'basic',
                icon: 'heroicons_outline:building-library',
                link: '/employee_deposit/list',
            },
            {
                id: 'manage_warning',
                title: 'จัดการใบตักเตือนพนักงาน',
                // hidden: () => {
                //     // const storedPermission = JSON.parse(localStorage.getItem('permission'));
                //     const menu = storedPermission?.find((e) => e.menu_id == 25);
                //     if (menu?.view == 0) {
                //         return true;
                //     } else {
                //         return false;
                //     }
                // },
                type: 'basic',
                icon: 'heroicons_outline:document',
                link: '/manange_warning/list',
            },
            // {
            //     title: 'ใบเหลือง',
            //     hidden: () => {
            //         // const storedPermission = JSON.parse(localStorage.getItem('permission'));
            //         const menu = storedPermission?.find((e) => e.menu_id == 26);
            //         if (menu?.view == 0) {
            //             return true;
            //         } else {
            //             return false;
            //         }
            //     },
            //     type: 'collapsable',
            //     icon: 'heroicons_outline:document',
            //     children: [
            //         {
            //             id: 'yellow-card',
            //             title: 'ใบเหลือง',
            //             type: 'basic',
            //             icon: 'heroicons_outline:document',
            //             link: '/yellow-card/list',
            //         },
            //         {
            //             id: 'yellow-card',
            //             title: 'รายงานใบเหลือง',
            //             type: 'basic',
            //             icon: 'heroicons_outline:document',
            //             link: '/yellow-card/report',
            //         },
            //     ],
            // },
            {
                title: 'รวมรายงาน',
                // hidden: () => {
                //     // const storedPermission = JSON.parse(localStorage.getItem('permission'));
                //     const menu = storedPermission?.find((e) => e.menu_id == 27);
                //     if (menu?.view == 0) {
                //         return true;
                //     } else {
                //         return false;
                //     }
                // },
                type: 'collapsable',
                icon: 'mat_solid:folder',
                children: [
                    {
                        id: 'report_warning',
                        title: 'รายงานใบตักเตือนพนักงาน',
                        type: 'basic',
                        link: '/report_warning/list',
                    },
                    {
                        id: 'worktime',
                        title: 'รายงานรายวัน',
                        type: 'basic',
                        link: '/worktime/list',
                    },
                    {
                        id: 'worktime-monthly',
                        title: 'รายงานรายเดือน',
                        type: 'basic',
                        link: '/worktime-monthly/list',
                    },
                    {
                        id: 'time-attendance',
                        title: 'รายงานเวลาเข้างาน',
                        type: 'basic',
                        link: '/report/time-attendance/list',
                    },
                    {
                        id: 'time-attendance-zk',
                        title: 'รายงานลงเวลา',
                        type: 'basic',
                        link: '/report/time-attendance/zk',
                    },


                ],
            },
            {
                title: 'การทำงาน',
                hidden: () => {
                    // const storedPermission = JSON.parse(localStorage.getItem('permission'));
                    const menu = storedPermission?.find((e) => e.menu_id == 26);
                    if (menu?.view == 0) {
                        return true;
                    } else {
                        return false;
                    }
                },
                type: 'collapsable',
                icon: 'heroicons_outline:currency-dollar',
                children: [
                    // {
                    //     id: 'work-calendar',
                    //     title: 'ตารางวันทำงาน',
                    //     type: 'basic',
                    //     icon: 'heroicons_outline:calendar',
                    //     link: '/calendar/new-calendar',
                    // },
                    // {
                    //     id: 'work-calendar-by-user',
                    //     title: 'ตารางการทำงานรายคน',
                    //     type: 'basic',
                    //     icon: 'mat_outline:save',
                    //     link: '/calendar/user-calendar',
                    // },
                    {
                        id: 'holiday',
                        title: 'วันหยุด',
                        type: 'basic',
                        link: '/holiday/list',
                    },
                    {
                        id: 'holidaysetting',
                        title: 'กลุ่มกะการทำงาน',
                        type: 'basic',
                        link: '/holidaysetting/list',
                    },
                ],
            },

            {
                title: 'การลา',
                hidden: () => {
                    // const storedPermission = JSON.parse(localStorage.getItem('permission'));
                    const menu = storedPermission?.find((e) => e.menu_id == 27);
                    if (menu?.view == 0) {
                        return true;
                    } else {
                        return false;
                    }
                },
                type: 'collapsable',
                icon: 'mat_solid:pregnant_woman',
                children: [
                    {
                        id: 'leavelist',
                        title: 'รายการลา',
                        type: 'basic',
                        link: '/leave-list/list',
                    },
                    {
                        id: 'leave',
                        title: 'สิทธิ์การลา',
                        type: 'basic',
                        link: '/leave/list',
                    },
                    {
                        id: 'leavetype',
                        title: 'ประเภทการลา',
                        type: 'basic',
                        link: '/leave-type/list',
                    },

                ],
            },

            {
                title: 'เงินเดือน',
                hidden: () => {
                    // const storedPermission = JSON.parse(localStorage.getItem('permission'));
                    const menu = storedPermission?.find((e) => e.menu_id == 28);
                    if (menu?.view == 0) {
                        return true;
                    } else {
                        return false;
                    }
                },
                type: 'collapsable',
                icon: 'attach_money',
                children: [
                    {
                        id: 'salary',
                        title: 'รายการ',
                        type: 'basic',
                        icon: 'heroicons_solid:bars-3',
                        link: '/salary/list',
                    },
                    {
                        id: 'advancemoney',
                        title: 'เบิกเงินเดือนล่วงหน้า',
                        type: 'basic',
                        icon: 'heroicons_solid:bars-3',
                        link: '/advancemoney/list',
                    },
                    {
                        id: 'deletemoney-type',
                        title: 'ประเภทเงินหัก',
                        type: 'basic',
                        icon: 'attach_money',
                        link: '/deletemoney-type/list',

                    },

                    {
                        id: 'plusmoney-type',
                        title: 'ประเภทเงินเพิ่ม',
                        type: 'basic',
                        link: '/plusmoney-type/list',
                        icon: 'heroicons_outline:currency-dollar',

                    },
                    // {
                    //     id: 'commission',
                    //     title: 'คอมมิชชั่น',
                    //     type: 'basic',
                    //     icon: 'feather:percent',
                    //     link: '/commission/list',
                    // },
                    {
                        id: 'ot-type',
                        title: 'ประเภทโอที',
                        type: 'basic',
                        icon: 'access_time_filled',
                        link: '/ot-type/list',
                    },
                    {
                        id: 'ot',
                        title: 'โอที',
                        type: 'basic',
                        icon: 'access_time_filled',
                        link: '/ot/list',
                    },

                ],

            },
             {
                id: 'print-doc',
                title: 'พิมพ์เอกสาร',
                type: 'basic',
                icon: 'heroicons_outline:printer',
                link: '/print-doc',
            },
        ],
    },

    {
        id: 'borrowing',
        title: 'การยืม-คืน',
        subtitle: 'บันทึกการยืม-คืน',
        type: 'group',
        children: [
            {
                id: 'borrowing',
                title: 'การยืม-คืน',
                type: 'collapsable',
                icon: 'heroicons_outline:cpu-chip',
                children: [
                    {
                        id: 'borrowing',
                        title: 'รายการยืม-คืน',
                        type: 'basic',
                        link: '/borrowing',
                    },
                    {
                        id: 'borrowing-type',
                        title: 'ประเภทพัสดุ',
                        type: 'basic',
                        link: '/borrowing-type',
                    },
                    {
                        id: 'borrowing-return',
                        title: 'พัสดุ',
                        type: 'basic',
                        link: '/equipment',
                    },
                ],
            },
        ],
    },

    {
        id: 'account',
        title: 'บัญชีผู้ใช้',
        type: 'group',

        icon: 'heroicons_outline:home',
        children: [
            {
                id: 'settings',
                title: 'โปรไฟล์',
                type: 'basic',
                icon: 'feather:user',
                link: '/user/profile',
            },
            {
                id: 'worktime',
                title: 'ตารางทำงาน',
                type: 'basic',
                icon: 'feather:calendar',
                link: '/calendar/admin-calendar',
            },
            {
                id: 'user-signout',
                title: 'ออกจากระบบ',
                type: 'basic',
                icon: 'feather:log-out',
                link: '/sign-out',
            },
        ],
    },
];

export const compactNavigation: FuseNavigationItem[] = [
    {
        id: 'dashboards',
        title: 'Dashboards',
        tooltip: 'Dashboards',
        type: 'aside',
        icon: 'heroicons_outline:home',
        children: [], // This will be filled from defaultNavigation so we don't have to manage multiple sets of the same navigation
    },
    {
        id: 'apps',
        title: 'Apps',
        tooltip: 'Apps',
        type: 'aside',
        icon: 'heroicons_outline:qrcode',
        children: [], // This will be filled from defaultNavigation so we don't have to manage multiple sets of the same navigation
    },
    {
        id: 'pages',
        title: 'Pages',
        tooltip: 'Pages',
        type: 'aside',
        icon: 'heroicons_outline:document-duplicate',
        children: [], // This will be filled from defaultNavigation so we don't have to manage multiple sets of the same navigation
    },
    {
        id: 'user-interface',
        title: 'UI',
        tooltip: 'UI',
        type: 'aside',
        icon: 'heroicons_outline:collection',
        children: [], // This will be filled from defaultNavigation so we don't have to manage multiple sets of the same navigation
    },
    {
        id: 'navigation-features',
        title: 'Navigation',
        tooltip: 'Navigation',
        type: 'aside',
        icon: 'heroicons_outline:menu',
        children: [], // This will be filled from defaultNavigation so we don't have to manage multiple sets of the same navigation
    },
];
export const futuristicNavigation: FuseNavigationItem[] = [
    {
        id: 'dashboards',
        title: 'DASHBOARDS',
        type: 'group',
        children: [], // This will be filled from defaultNavigation so we don't have to manage multiple sets of the same navigation
    },
    {
        id: 'apps',
        title: 'APPS',
        type: 'group',
        children: [], // This will be filled from defaultNavigation so we don't have to manage multiple sets of the same navigation
    },
    {
        id: 'others',
        title: 'OTHERS',
        type: 'group',
    },
    {
        id: 'pages',
        title: 'Pages',
        type: 'aside',
        icon: 'heroicons_outline:document-duplicate',
        children: [], // This will be filled from defaultNavigation so we don't have to manage multiple sets of the same navigation
    },
    {
        id: 'user-interface',
        title: 'User Interface',
        type: 'aside',
        icon: 'heroicons_outline:collection',
        children: [], // This will be filled from defaultNavigation so we don't have to manage multiple sets of the same navigation
    },
    {
        id: 'navigation-features',
        title: 'Navigation Features',
        type: 'aside',
        icon: 'heroicons_outline:menu',
        children: [], // This will be filled from defaultNavigation so we don't have to manage multiple sets of the same navigation
    },
];
export const horizontalNavigation: FuseNavigationItem[] = [
    {
        id: 'dashboards',
        title: 'Dashboards',
        type: 'group',
        icon: 'heroicons_outline:home',
        children: [], // This will be filled from defaultNavigation so we don't have to manage multiple sets of the same navigation
    },
    {
        id: 'apps',
        title: 'Apps',
        type: 'group',
        icon: 'heroicons_outline:qrcode',
        children: [], // This will be filled from defaultNavigation so we don't have to manage multiple sets of the same navigation
    },
    {
        id: 'pages',
        title: 'Pages',
        type: 'group',
        icon: 'heroicons_outline:document-duplicate',
        children: [], // This will be filled from defaultNavigation so we don't have to manage multiple sets of the same navigation
    },
    {
        id: 'user-interface',
        title: 'UI',
        type: 'group',
        icon: 'heroicons_outline:collection',
        children: [], // This will be filled from defaultNavigation so we don't have to manage multiple sets of the same navigation
    },
    {
        id: 'navigation-features',
        title: 'Misc',
        type: 'group',
        icon: 'heroicons_outline:menu',
        children: [], // This will be filled from defaultNavigation so we don't have to manage multiple sets of the same navigation
    },
];
