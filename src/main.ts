import { enableProdMode, importProvidersFrom } from '@angular/core';
import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';
import { environment } from 'environments/environment';

import { DateAdapter, MAT_DATE_FORMATS, MAT_DATE_LOCALE } from '@angular/material/core';
import { MY_DATE_FORMATS } from './app/modules/date-formats';
import { MAT_MOMENT_DATE_ADAPTER_OPTIONS, MatMomentDateModule } from '@angular/material-moment-adapter';
import { BrowserModule, bootstrapApplication } from '@angular/platform-browser';
import { provideAnimations } from '@angular/platform-browser/animations';
import { RouterModule, ExtraOptions, PreloadAllModules } from '@angular/router';
import { appRoutes } from 'app/app.routing';
import { NgApexchartsModule } from 'ng-apexcharts';
import { FuseModule } from '@fuse';
import { FuseConfigModule } from '@fuse/services/config';
import { appConfig } from 'app/core/config/app.config';
import { FuseMockApiModule } from '@fuse/lib/mock-api';
import { mockApiServices } from 'app/mock-api';
import { CoreModule } from 'app/core/core.module';
import { LayoutModule } from 'app/layout/layout.module';
import { MarkdownModule } from 'ngx-markdown';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { MatDatepickerModule } from '@angular/material/datepicker';
import { AppComponent } from './app/app.component';
import { BuddhistEraLuxonDateAdapter, MAT_LUXON_DATE_FORMATS } from 'app/buddhist-era-luxon-date-adapter';

const routerConfig: ExtraOptions = {
    preloadingStrategy: PreloadAllModules,
    scrollPositionRestoration: 'enabled',
};



if (environment.production) {
    enableProdMode();
}

bootstrapApplication(AppComponent, {
    providers: [
        importProvidersFrom(BrowserModule, RouterModule.forRoot(appRoutes, routerConfig), NgApexchartsModule,
            // Fuse, FuseConfig & FuseMockAPI
            FuseModule, FuseConfigModule.forRoot(appConfig), FuseMockApiModule.forRoot(mockApiServices),
            // Core module of your application
            CoreModule,
            // Layout module of your application
            LayoutModule,
            // 3rd party modules that require global configuration via forRoot
            MarkdownModule.forRoot({}), FontAwesomeModule, MatDatepickerModule, MatMomentDateModule),
        // { provide: MAT_DATE_FORMATS, useValue: MY_DATE_FORMATS }, // ตั้งค่ารูปแบบวันที่ทั่วโปรเจกต์
        // { provide: MAT_MOMENT_DATE_ADAPTER_OPTIONS, useValue: { useUtc: true } },
        {
            provide: DateAdapter,
            useClass: BuddhistEraLuxonDateAdapter,
        },
        {
            provide: MAT_DATE_FORMATS,
            useValue: MAT_LUXON_DATE_FORMATS,
        },
        {
            provide: MAT_DATE_LOCALE,
            useValue: 'th-TH',
        },
        provideAnimations(),
    ]
})
    .catch(err => console.error(err));
