.fuse-confirmation-dialog-panel {
    @screen md {
        @apply w-128;
    }

    .mat-mdc-dialog-container {
        padding: 0 !important;
    }
}

// :host::ng-deep .mat-flat-button {
//     border-radius: 0px !important;
//     background: #4DB433 !important;
//     color: #ffffff !important;
// }

// :host::ng-deep .mat-stroked-button {
//     border-radius: 0px !important;
//     background: #ffffff !important;
//     color: #666 !important;
// }

// :host::ng-deep .mat-stroked-button.mat-primary {
//     border-radius: 0px !important;
//     background: #04a353 !important;
//     color: white !important;
// }

// :host::ng-deep .mat-stroked-button.mat-accent {
//     border-radius: 0px !important;
//     background: #4DB433 !important;
//     color: white !important;
// }
