 // Gradient Configuration File
// This file contains all gradient-related color configurations

// GS Gradient Colors
$gs-gradient-primary: #102E50;
$gs-gradient-secondary: #091b30;

// Selection and Hover Colors
$gs-selection-color: #b9cffc; // Green selection color
$gs-hover-color: #b9cffc; // Darker green for hover
$gs-selection-text-color: #333333; // Dark text for selected items

// Gradient Classes
.gs-gradient-primary {
    background-color: $gs-gradient-primary;
}

.gs-gradient-secondary {
    background-color: $gs-gradient-secondary;
}

// Gradient Direction Classes
.gs-gradient-to-t {
    background: linear-gradient(to top, $gs-gradient-primary, $gs-gradient-secondary);
}

.gs-gradient-to-b {
    background: linear-gradient(to bottom, $gs-gradient-primary, $gs-gradient-primary);
}

.gs-gradient-to-l {
    background: linear-gradient(to left, $gs-gradient-primary, $gs-gradient-secondary);
}

.gs-gradient-to-r {
    background: linear-gradient(to right, $gs-gradient-primary, $gs-gradient-secondary);
}

// Selection and Hover Classes
.gs-selection {
    background-color: $gs-selection-color;
    color: $gs-selection-text-color;
}

.gs-hover {
    background-color: $gs-hover-color;
    color: $gs-selection-text-color;
    transition: background-color 0.2s ease;
}

.gs-hover:hover {
    background-color: darken($gs-hover-color, 5%);
}

// CSS Custom Properties for use in other files
:root {
    --gs-gradient-primary: #{$gs-gradient-primary};
    --gs-gradient-secondary: #{$gs-gradient-secondary};
    --gs-selection-color: #{$gs-selection-color};
    --gs-hover-color: #{$gs-hover-color};
    --gs-selection-text-color: #{$gs-selection-text-color};
}

// Dark mode support
.dark {
    --gs-gradient-primary: #{$gs-gradient-primary};
    --gs-gradient-secondary: #{$gs-gradient-secondary};
    --gs-selection-color: #{$gs-selection-color};
    --gs-hover-color: #{$gs-hover-color};
    --gs-selection-text-color: #{$gs-selection-text-color};
}