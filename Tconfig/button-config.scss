// Button Configuration File
// This file contains all button-related styling configurations

// Button Gradient Colors
$gs-button-gradient-start: #ff8c00; // Orange start color
$gs-button-gradient-end: #fbd405;   // Yellow end color

// Button Text Colors
$gs-button-text-color: #ffffff;     // White text
$gs-button-text-hover: #ffffff;     // White text on hover

// Button Border and Border Radius
$gs-button-border-radius: 8px;      // Rounded corners
$gs-button-border: none;            // No border

// Button Padding and Spacing
$gs-button-padding-x: 16px;        // Horizontal padding
$gs-button-padding-y: 8px;         // Vertical padding
$gs-button-font-size: 16px;        // Font size
$gs-button-font-weight: bold;       // Bold text

// Button Classes
.gs-button {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: $gs-button-padding-y $gs-button-padding-x;
    border-radius: $gs-button-border-radius;
    border: $gs-button-border;
    background: linear-gradient(to right, $gs-button-gradient-start, $gs-button-gradient-end);
    color: $gs-button-text-color;
    font-size: $gs-button-font-size;
    font-weight: $gs-button-font-weight;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    user-select: none;
    
    &:hover {
        background: linear-gradient(to right, darken($gs-button-gradient-start, 5%), darken($gs-button-gradient-end, 5%));
        color: $gs-button-text-hover;
        transform: translateY(-1px);
        box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    }
    
    &:active {
        transform: translateY(0);
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }
    
    &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
        box-shadow: none;
    }
}

// Button Variants
.gs-button-primary {
    @extend .gs-button;
    // Primary button uses default gradient
}

.gs-button-secondary {
    @extend .gs-button;
    background: linear-gradient(to right, #6b7280, #9ca3af);
    color: #ffffff;
    
    &:hover {
        background: linear-gradient(to right, #4b5563, #6b7280);
    }
}

.gs-button-success {
    @extend .gs-button;
    background: linear-gradient(to right, #10b981, #34d399);
    color: #ffffff;
    
    &:hover {
        background: linear-gradient(to right, #059669, #10b981);
    }
}

.gs-button-danger {
    @extend .gs-button;
    background: linear-gradient(to right, #ef4444, #f87171);
    color: #ffffff;
    
    &:hover {
        background: linear-gradient(to right, #dc2626, #ef4444);
    }
}

// Button Sizes
.gs-button-sm {
    @extend .gs-button;
    padding: 6px 12px;
    font-size: 14px;
}

.gs-button-lg {
    @extend .gs-button;
    padding: 12px 24px;
    font-size: 18px;
}

// CSS Custom Properties for use in other files
:root {
    --gs-button-gradient-start: #{$gs-button-gradient-start};
    --gs-button-gradient-end: #{$gs-button-gradient-end};
    --gs-button-text-color: #{$gs-button-text-color};
    --gs-button-border-radius: #{$gs-button-border-radius};
}

// Dark mode support
.dark {
    --gs-button-gradient-start: #{$gs-button-gradient-start};
    --gs-button-gradient-end: #{$gs-button-gradient-end};
    --gs-button-text-color: #{$gs-button-text-color};
} 